---
description: Guidelines for implementing Next.js API routes
globs:
alwaysApply: false
---
# API Routes

## File Structure

API routes should follow this structure:

- `src/app/api/[endpoint-name]/route.ts` - Main route handler
- `src/app/api/[endpoint-name]/validation.ts` - Validation schemas

## Standard Format

Use this format for API routes:

```typescript
import type { NextRequest } from 'next/server';
import type { RequestBodyType } from './validation';
import { withError } from '@/middleware';
import { NextResponse } from 'next/server';
import { requestBodySchema } from './validation';

export const POST = withError(async (request: NextRequest) => {
  try {
    // Parse the request body
    const data: RequestBodyType = await request.json();

    // Validate the request body
    const { success, error } = requestBodySchema.safeParse(data);

    if (!success) {
      // Format Zod errors into a readable string
      const errorMessage = error.errors
        .map(e => `${e.path.join('.')}: ${e.message}`)
        .join(', ');

      return NextResponse.json(
        { error: errorMessage },
        { status: 400 },
      );
    }

    // Process the request
    // ...

    // Return a response
    return NextResponse.json({ result: 'Success' });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error processing request:', error);

    // Return an error response
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 },
    );
  }
});
```

For validation schema (`validation.ts`):

```typescript
import { z } from 'zod';

// Validation schema for request body
export const requestBodySchema = z.object({
  param1: z.string().min(1, 'Parameter is required'),
  param2: z.number().min(1).max(1000).optional(),
});
export type RequestBodyType = z.infer<typeof requestBodySchema>;
```

## Real-World Example
Please take a look at folder `src/app/api/feedback`

## Implementation Guidelines

- Use Zod for request body validation in a separate validation.ts file
- Wrap route handlers with `withError` middleware for consistent error handling
- Use proper TypeScript typing for request and response data
- Format validation errors to be user-friendly
- Handle errors gracefully with try/catch blocks
- Use NextResponse.json for regular responses
- For streaming responses, use ReadableStream with proper headers
- Log errors with appropriate context for debugging
- Place validation schemas in a separate file for reuse
- Use JSDoc comments to document the purpose of each API route

## Accessing API Routes from Client Components

When accessing API routes from client components:

```typescript
// For regular JSON responses
const response = await fetch('/api/your-endpoint', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(data),
});
const result = await response.json();

// For streaming responses
const response = await fetch('/api/feedback', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(feedbackParams),
});

if (response.ok) {
  // Process the stream using a utility function
  processServerSentEvents(response, (data) => {
    if (data.content) {
      // Handle each chunk of the stream
      setFeedbackText(prev => prev + data.content);
    }
  });
} else {
  // Handle error
  const errorData = await response.json();
  toast.error(`Error: ${errorData.error || 'Failed to generate feedback'}`);
}
```

Important notes:
- API routes are defined at `/api/*` and are excluded from locale routing in the middleware
- This allows direct access to API routes without locale prefixing
- Always use the direct path (e.g., `/api/feedback`) rather than trying to construct locale-aware paths
