# Theming System Guide

This guide explains how to use the theming system in our application. The theming system is designed to provide a consistent look and feel across the application while allowing for easy theme switching.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Using Theme Tokens](#using-theme-tokens)
3. [Creating Theme-Aware Components](#creating-theme-aware-components)
4. [Using the useThemeStyles Hook](#using-the-themestyles-hook)
5. [Best Practices](#best-practices)
6. [Migration Guide](#migration-guide)

## Architecture Overview

Our theming system is built on the following principles:

1. **CSS Variables**: We use CSS variables to define theme tokens that can be changed dynamically.
2. **Semantic Naming**: We use semantic names for our tokens (e.g., `--color-primary` instead of `--blue-500`).
3. **Tailwind Integration**: We extend Tailwind's theme with our CSS variables.
4. **Component Variants**: We use the `cva` utility to define component variants.

The key files in our theming system are:

- `tailwind.config.js`: Extends Tai<PERSON>wind's theme with our CSS variables.
- `src/styles/themes/base.ts`: Defines the base theme tokens.
- `src/styles/themes/surface.css`: Defines surface-related CSS variables.
- `src/shared/contexts/ThemeContext.tsx`: Provides theme switching functionality.
- `src/shared/hooks/useThemeStyles.ts`: Provides utilities for theme-aware styling.
- `src/shared/utils/theme.ts`: Provides utility functions for theme-related operations.

## Using Theme Tokens

Theme tokens are CSS variables that define the colors, spacing, typography, and other design elements of our application. They are defined in the `src/styles/themes/base.ts` file and mapped to Tailwind classes in the `tailwind.config.js` file.

### Example: Using Theme Tokens in Tailwind Classes

```tsx
// Using semantic color tokens
<div className="bg-primary text-primary-foreground">
  This div has a primary background color and primary foreground text color.
</div>

// Using surface colors
<div className="bg-surface hover:bg-surface-hover">
  This div has a surface background color and changes to surface-hover on hover.
</div>
```

## Creating Theme-Aware Components

To create a theme-aware component, you should use the `cva` utility to define component variants and the `cn` utility to combine classes.

### Example: Creating a Theme-Aware Button Component

```tsx
// button-variants.ts
import { cva } from 'class-variance-authority';

export const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);
```

```tsx
// Button.tsx
import { cn } from '@/shared/utils/utils';
import { buttonVariants } from './button-variants';
import type { VariantProps } from 'class-variance-authority';

type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & 
  VariantProps<typeof buttonVariants>;

export const Button: React.FC<ButtonProps> = ({
  className,
  variant,
  size,
  ...props
}) => {
  return (
    <button
      className={cn(buttonVariants({ variant, size }), className)}
      {...props}
    />
  );
};
```

## Using the useThemeStyles Hook

The `useThemeStyles` hook provides utilities for generating theme-aware classes and styles. It's particularly useful for components that need to adapt to the current theme.

### Example: Using the useThemeStyles Hook

```tsx
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';
import { cn } from '@/shared/utils/utils';

const MyComponent: React.FC = () => {
  const { classesObj } = useThemeStyles();
  
  // Define theme-aware classes
  const containerClasses = classesObj(
    'divide-y',
    {
      light: 'divide-surface-divider',
      dark: 'divide-surface-divider',
    },
  );
  
  const itemClasses = (isSelected: boolean) => {
    return cn(
      'cursor-pointer',
      classesObj(
        '',
        {
          light: 'hover:bg-surface-hover',
          dark: 'hover:bg-surface-hover',
        },
      ),
      isSelected && classesObj(
        '',
        {
          light: 'bg-surface-selected',
          dark: 'bg-surface-selected',
        },
      ),
    );
  };
  
  return (
    <div className={containerClasses}>
      <div className={itemClasses(true)}>
        Selected Item
      </div>
      <div className={itemClasses(false)}>
        Unselected Item
      </div>
    </div>
  );
};
```

## Best Practices

1. **Use Semantic Names**: Always use semantic names for your theme tokens (e.g., `--color-primary` instead of `--blue-500`).
2. **Use CSS Variables**: Use CSS variables for theme tokens instead of hardcoding colors.
3. **Use Component Variants**: Use the `cva` utility to define component variants.
4. **Use the useThemeStyles Hook**: Use the `useThemeStyles` hook for components that need to adapt to the current theme.
5. **Test in Both Themes**: Always test your components in both light and dark themes.

## Migration Guide

If you're migrating an existing component to use the new theming system, follow these steps:

1. **Create a Variants File**: Create a file for your component variants (e.g., `button-variants.ts`).
2. **Define Component Variants**: Use the `cva` utility to define component variants.
3. **Update the Component**: Update your component to use the variants and the `cn` utility.
4. **Test in Both Themes**: Test your component in both light and dark themes.

For more detailed instructions, see the [Migration Guide](../todo-theme-system.md#migration-guide) in the todo-theme-system.md file.
