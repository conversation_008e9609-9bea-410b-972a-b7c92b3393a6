# Theming System Explained

This document provides a detailed explanation of the theming implementation in our application, clarifying how CSS variables, Tailwind configuration, and React components work together to create a flexible and maintainable theming system.

## Architecture Overview

Our theming system is built on several key components:

1. **CSS Variables**: Define theme values and semantic tokens
2. **Tailwind Configuration**: Extends Tailwind with our CSS variables
3. **Theme Context**: Manages theme state and switching
4. **Theme Hooks**: Provides utilities for theme-aware styling
5. **Theme Utilities**: Offers helper functions for theme operations

The system supports multiple themes:
- Light (default)
- Dark
- Yellow
- Blue
- Frappe

## CSS Variables and @theme Decorators

The `variables.css` file uses custom CSS at-rules like `@theme` and `@theme inline` to organize CSS variables. These are processed by a PostCSS plugin to generate the final CSS.

### @theme Decorator

The `@theme` decorator is used to group related CSS variables:

```css
@theme {
  /* Font families */
  --font-*: initial;
  --font-outfit: 'Lexend Deca', sans-serif;
  --font-lexend: 'Lexend Deca', sans-serif;

  /* Font sizes with line heights */
  --text-title-2xl: 72px;
  --text-title-2xl--line-height: 90px;
  /* More variables... */
}
```

### @theme inline Decorator

The `@theme inline` decorator is used for semantic mappings:

```css
@theme inline {
  /* Border radius tokens */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  
  /* Base UI colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  /* More semantic mappings... */
}
```

### @custom-variant Decorator

The `@custom-variant dark (&:is(.dark *))` rule defines a custom variant for dark mode styling, which is likely processed by a PostCSS plugin to generate dark mode styles.

## Theme-Specific Variables

Theme-specific variables are defined in selectors for each theme:

```css
/* Light theme (default) */
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  /* More variables... */
}

/* Dark theme */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  /* More variables... */
}

/* Yellow theme */
.theme-yellow {
  --background: oklch(0.98 0.05 85);
  --foreground: oklch(0.2 0.05 85);
  /* More variables... */
}
```

## Surface-Specific Variables

The `surface.css` file defines surface-related CSS variables for different themes:

```css
/* Light theme surface variables */
:root {
  --color-surface: oklch(0.98 0 0);
  --color-surface-hover: oklch(0.96 0 0);
  --color-surface-selected: oklch(0.94 0 0);
  --color-surface-divider: oklch(0.92 0 0);
}

/* Dark theme surface variables */
.dark {
  --color-surface: oklch(0.2 0 0);
  --color-surface-hover: oklch(0.25 0 0);
  --color-surface-selected: oklch(0.3 0 0);
  --color-surface-divider: oklch(0.35 0 0);
}

/* More theme-specific surface variables... */
```

## Tailwind Configuration

The `tailwind.config.js` file extends Tailwind's theme with our CSS variables:

```js
module.exports = {
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Semantic color tokens
        background: 'var(--color-background)',
        foreground: 'var(--color-foreground)',
        
        // Surface colors
        surface: {
          DEFAULT: 'var(--color-surface)',
          hover: 'var(--color-surface-hover)',
          selected: 'var(--color-surface-selected)',
          divider: 'var(--color-surface-divider)',
        },
        
        // More color mappings...
      },
      // More theme extensions...
    },
  },
};
```

This allows using Tailwind classes like `bg-primary` and `text-foreground` that automatically adapt to the current theme.

## Theme Context

The `ThemeContext` manages the current theme state and provides methods for switching themes:

```tsx
export type Theme = 'light' | 'dark' | 'yellow' | 'blue' | 'frappe';

export type ThemeContextType = {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  systemTheme: Theme;
};
```

When the theme changes, it adds/removes appropriate classes to `document.documentElement`:

```tsx
// Apply theme to document
useEffect(() => {
  if (isInitialized) {
    // Save theme preference to localStorage
    localStorage.setItem('theme', theme);

    // Remove all theme classes
    document.documentElement.classList.remove('dark', 'theme-yellow', 'theme-blue', 'theme-frappe');

    // Apply appropriate theme class
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else if (theme === 'yellow') {
      document.documentElement.classList.add('theme-yellow');
    }
    // More theme classes...
  }
}, [theme, isInitialized]);
```

## Theme Utilities

The `theme.ts` file provides utility functions for theme operations:

```tsx
// Get the current theme
export function getCurrentTheme(): 'light' | 'dark' | 'yellow' | 'blue' | 'frappe' {
  if (document.documentElement.classList.contains('dark')) {
    return 'dark';
  } else if (document.documentElement.classList.contains('theme-yellow')) {
    return 'yellow';
  }
  // More theme checks...
  return 'light';
}

// Theme-aware class generation
export function themeClass(
  baseClasses: ClassValue,
  themeClasses?: Record<string, ClassValue>,
) {
  const currentTheme = getCurrentTheme();
  const themeSpecificClasses = themeClasses?.[currentTheme];
  return cn(baseClasses, themeSpecificClasses);
}

// More utility functions...
```

## Using Themes in Components

### 1. Using Tailwind Classes

The simplest way to use themes is with Tailwind classes that map to CSS variables:

```tsx
<div className="bg-background text-foreground p-4 border border-border rounded-lg">
  <h2 className="text-primary text-lg font-medium">Themed Content</h2>
  <p className="text-muted-foreground">This content adapts to the current theme.</p>
</div>
```

### 2. Using the useThemeStyles Hook

For more complex theme-aware styling, use the `useThemeStyles` hook:

```tsx
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';

function ThemedComponent() {
  const { classesObj } = useThemeStyles();
  
  const cardClasses = classesObj(
    'p-4 rounded-lg',
    {
      light: 'bg-white border-gray-200',
      dark: 'bg-gray-800 border-gray-700',
      yellow: 'bg-yellow-50 border-yellow-200',
      blue: 'bg-blue-50 border-blue-200',
      frappe: 'bg-gray-50 border-gray-200',
    }
  );
  
  return <div className={cardClasses}>Themed content</div>;
}
```

## How It All Works Together

1. **Theme Definition**:
   - CSS variables define raw theme values in `variables.css` and `surface.css`
   - Theme-specific selectors (`:root`, `.dark`, etc.) provide values for each theme

2. **Semantic Mapping**:
   - Semantic variables like `--color-primary` map to theme-specific variables like `--primary`
   - This creates a layer of abstraction between raw values and their semantic meaning

3. **Tailwind Integration**:
   - Tailwind configuration maps colors to CSS variables
   - This allows using Tailwind classes that automatically adapt to the current theme

4. **Theme Switching**:
   - ThemeContext adds/removes classes to the document root
   - These classes trigger different sets of CSS variable values
   - Components using semantic tokens automatically adapt to the new theme

5. **Component Usage**:
   - Components can use Tailwind classes with semantic tokens
   - For more complex cases, they can use the useThemeStyles hook

## Conclusion

Our theming system provides a flexible and maintainable way to create theme-aware components. By using CSS variables, Tailwind configuration, and React hooks, we can create components that automatically adapt to different themes without changing their code.

When implementing new components, prefer using semantic color tokens (like `bg-primary`, `text-foreground`) over hardcoded colors to ensure they work correctly across all themes.
