# Zustand and TanStack Query Integration Guide

## Introduction

This guide explains the architectural pattern used in our application for state management and data fetching, which combines Zustand for UI state with TanStack Query for server state.

### Key Benefits

- **Separation of Concerns**: UI state is managed separately from server state
- **Improved Performance**: TanStack Query provides optimized caching and background updates
- **Reduced Boilerplate**: Service files centralize API logic
- **Type Safety**: Strong typing throughout the stack
- **Maintainability**: Clear patterns make code easier to understand and extend
- **Reusability**: Hooks and services can be reused across components

## Service Layer

The service layer is the foundation of our data fetching architecture. It centralizes all API calls in dedicated service files, providing a clean interface for interacting with the backend.

### Structure of Service Files

Service files follow a consistent pattern:

1. Define types for request/response data
2. Implement functions for API operations (GET, POST, PUT, DELETE)
3. Handle request formatting and error normalization
4. Return standardized responses

### Example: Project Service

```typescript
// src/features/project-management/services/project.service.ts
import type { ApiResponse } from '@/shared/types/api-response';
import type { GetProjectsResponse, Project } from '../models';
import { http } from '@/core/http/http';

export type ProjectFilters = {
  status?: ProjectStatusEnum | 'All';
  type?: ProjectTypeEnum | 'All';
  // ... other filter properties
};

// Helper function to build query parameters
export const buildProjectQueryParams = (filters: ProjectFilters = {}): string => {
  const params = new URLSearchParams();
  
  if (filters.status && filters.status !== 'All') {
    params.append('status', filters.status.toString());
  }
  // ... other filter parameters
  
  return params.toString() ? `?${params.toString()}` : '';
};

// API function to fetch projects
export async function getProjects(filters: ProjectFilters = {}): Promise<ApiResponse<GetProjectsResponse>> {
  const queryString = buildProjectQueryParams(filters);
  const response = await http.get<GetProjectsResponse>({ url: `/projects${queryString}` });
  return response;
}

// API function to fetch a single project
export async function getProjectById(id: string): Promise<ApiResponse<Project>> {
  return await http.get<Project>({ url: `/projects/${id}` });
}

// API function to delete a project
export async function deleteProjectById(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/projects/${id}` });
}
```

### HTTP Service

Our application uses a centralized HTTP service that handles:

- Request formatting
- Authentication headers
- Error normalization
- Response standardization

All API responses follow a consistent format:

```typescript
type ApiResponse<T> = {
  statusCode: number;
  message: string;
  data: T | null;
  errorDetails?: {
    error: string;
    statusCode: number;
    timestamp: string;
    path: string;
  };
};
```

## Zustand for UI State

Zustand is used exclusively for managing UI-related state, such as:

- Filter selections
- UI visibility toggles
- Form input values
- UI preferences

### Creating a Zustand Store

```typescript
// src/features/project-management/hooks/useProjectFilters.ts
import { create } from 'zustand';

type ProjectFilterStore = {
  // State
  filters: {
    status: ProjectStatusEnum | 'All';
    type: ProjectTypeEnum | 'All';
    // ... other filter properties
  };
  // Actions
  setFilter: <K extends keyof ProjectFilterStore['filters']>(
    key: K,
    value: ProjectFilterStore['filters'][K]
  ) => void;
  applyFilters: (filters: FilterState) => void;
  resetFilters: () => void;
};

export const useProjectFilterStore = create<ProjectFilterStore>(set => ({
  // Initial state
  filters: {
    status: 'All',
    type: 'All',
    // ... other initial values
  },

  // Actions
  setFilter: (key, value) => {
    set(state => ({
      filters: {
        ...state.filters,
        [key]: value,
      },
    }));
  },
  
  // ... other actions
}));
```

### Custom Hooks for Enhanced Functionality

We often wrap Zustand stores in custom hooks to add derived state or additional functionality:

```typescript
export const useProjectFilters = () => {
  const filterStore = useProjectFilterStore();

  // Convert the filters to the format expected by the API
  const apiFilters = {
    status: filterStore.filters.status,
    type: filterStore.filters.type,
    // ... other filter mappings
  };

  // Derived state
  const hasActiveFilters = filterStore.filters.status !== 'All' || 
    filterStore.filters.type !== 'All' ||
    // ... other conditions
    
  return {
    ...filterStore,
    apiFilters,
    hasActiveFilters,
  };
};
```

## TanStack Query for Data Fetching

TanStack Query (React Query) is used for all data fetching, caching, and synchronization with the server. It handles:

- Data fetching and caching
- Background refetching
- Loading and error states
- Pagination and infinite scrolling
- Mutations (data updates)

### Query Hooks

```typescript
// src/features/project-management/hooks/useProjects.ts
import { useQuery } from '@tanstack/react-query';
import { getProjects } from '../services/project.service';

export const useProjects = (filters: ProjectFilters = {}) => {
  return useQuery({
    queryKey: ['projects', filters],
    queryFn: () => getProjects(filters),
    select: response => response.data,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};
```

### Infinite Query Hooks

```typescript
// src/features/project-management/hooks/useProjectInfiniteQuery.ts
import { useInfiniteQuery } from '@tanstack/react-query';
import { getProjectsPage } from '../services/project.service';

export const useProjectInfiniteQuery = (filters: ProjectFilters = {}) => {
  return useInfiniteQuery({
    queryKey: ['projects', 'infinite', filters],
    queryFn: ({ pageParam = 1 }) => getProjectsPage(filters, pageParam),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage.data) {
        const { page, totalPages } = lastPage.data;
        return page < totalPages ? page + 1 : undefined;
      }
      return undefined;
    },
    select: data => ({
      pages: data.pages.map(page => page.data),
      pageParams: data.pageParams,
    }),
    staleTime: 5 * 60 * 1000,
  });
};
```

### Mutation Hooks

```typescript
// Example from ProjectCard.tsx
const deleteMutation = useMutation({
  mutationFn: () => deleteProject(id),
  onSuccess: () => {
    toast.success(t('project_deleted_successfully'));
    // Invalidate and refetch projects after successful deletion
    queryClient.invalidateQueries({ queryKey: ['projects'] });
    closeDeleteConfirm();
  },
  onError: (error: any) => {
    toast.error(error?.message || 'Failed to delete project');
    closeDeleteConfirm();
  },
});
```

## Integration Patterns

The key to our architecture is how Zustand and TanStack Query work together. Here are the main integration patterns:

### Pattern 1: Zustand State as Query Parameters

```typescript
// src/features/project-management/components/project-list/ProjectList.tsx
export default function ProjectList() {
  // Get filters from Zustand store via custom hook
  const { apiFilters } = useProjectFilters();

  // Pass filters to TanStack Query hook
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch,
  } = useProjectInfiniteQuery(apiFilters);
  
  // ... component implementation
}
```

### Pattern 2: Combining Store Updates with Mutations

```typescript
// Example from useAuth.ts
const loginMutation = useMutation({
  mutationFn: async (credentials: LoginCredentials) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await loginApi(credentials);

      if (response.data?.accessToken) {
        // Store token in cookies
        setToken(response.data.accessToken);

        // Update Zustand store state
        setAuthenticated(true);
        
        if (response.data.user) {
          setUser(response.data.user);
        } else {
          // Fetch user data with TanStack Query
          await refetchUser();
        }
        
        // UI feedback
        toast.success('Login successful');
        
        // Navigation
        if (authOptions.autoRedirect) {
          router.push(authOptions.loginRedirectPath);
        }
      }

      return response;
    } catch (error: any) {
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  },
});
```

## Best Practices

### State Management Separation

1. **Use Zustand for UI state only**:
   - Filter selections
   - UI visibility toggles
   - Form input values
   - UI preferences

2. **Use TanStack Query for server state**:
   - Data fetching
   - Data mutations
   - Caching
   - Loading/error states

### Service Layer

1. **Centralize all API calls in service files**
2. **Use typed request/response interfaces**
3. **Implement consistent error handling**
4. **Return standardized API responses**

### TanStack Query

1. **Use meaningful query keys** that include all dependencies
2. **Set appropriate staleTime** based on data volatility
3. **Use select to transform response data** into the format needed by components
4. **Invalidate related queries after mutations**

### Error Handling

1. **Normalize errors** to a consistent format
2. **Provide user-friendly error messages**
3. **Log detailed errors for debugging**
4. **Handle specific error cases** (authentication, validation, etc.)

## Real-world Example: Project Management

Here's a complete example of how these patterns work together in the project management feature:

1. **Service Layer** (project.service.ts):
   - Defines API functions for fetching and manipulating projects
   - Handles query parameter building

2. **UI State** (useProjectFilters.ts):
   - Manages filter selections in Zustand
   - Provides derived state and API-compatible filter format

3. **Data Fetching** (useProjectInfiniteQuery.ts):
   - Fetches projects with TanStack Query
   - Handles pagination, caching, and refetching

4. **Component Integration** (ProjectList.tsx):
   - Gets filters from Zustand
   - Passes filters to TanStack Query
   - Renders UI based on query results

This separation provides a clean, maintainable architecture that scales well as the application grows.

## Conclusion

The combination of Zustand for UI state and TanStack Query for server state provides a powerful, flexible architecture for modern React applications. By following these patterns and best practices, you can build applications that are:

- **Performant**: Optimized rendering and data fetching
- **Maintainable**: Clear separation of concerns
- **Scalable**: Patterns that work for simple and complex features
- **Developer-friendly**: Consistent, predictable behavior

For more information, refer to the official documentation:
- [Zustand](https://github.com/pmndrs/zustand)
- [TanStack Query](https://tanstack.com/query/latest)
