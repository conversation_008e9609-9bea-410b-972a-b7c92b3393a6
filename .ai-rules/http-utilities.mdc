# HTTP Utilities Documentation

This document provides detailed information about the HTTP utilities in the application, with a focus on the `execute()` function in the `useHttp` hook and its global error handling capabilities.

## API Response Format

All API responses follow a standardized format:

```typescript
type ApiResponse<T = any> = {
  statusCode: number;
  message: string;
  data: T | null;
  errorDetails?: {
    error: string;
    statusCode: number;
    timestamp: string;
    path: string;
  };
};
```

- `statusCode`: HTTP status code (200, 400, 401, etc.)
- `message`: Human-readable message describing the response
- `data`: The actual response data (or null for errors)
- `errorDetails`: Additional error information (for error responses)

## Global Error Handling

One of the key features of our HTTP utilities is **global error handling**. This means that individual components don't need to implement their own error handling logic using try/catch blocks. Instead, all error handling is centralized in the `useHttp` hook.

### Benefits of Global Error Handling

1. **Simplified Component Code**: Components can focus on their core functionality without worrying about error handling.
2. **Consistent Error Handling**: All errors are handled in the same way throughout the application.
3. **Automatic Toast Notifications**: Errors are automatically displayed to the user via toast notifications.
4. **Standardized Error Responses**: All error responses follow the same format, making them easier to work with.
5. **Automatic Loading State Management**: The loading state is automatically updated, even when errors occur.

## The `execute()` Function

The `execute()` function is the core of our HTTP request handling system. It provides a consistent way to make HTTP requests, handle responses, and manage errors globally.

### Purpose

The `execute()` function serves as a central point for:

1. Making HTTP requests with standardized error handling
2. Managing loading states during requests
3. Handling component unmounting during in-flight requests
4. Providing toast notifications for errors
5. Supporting request cancellation
6. Invoking success and error callbacks

### Function Signature

```typescript
const execute = async <T>(
  fn: (signal: AbortSignal) => Promise<ApiResponse<T>>,
  onSuccess?: (data: T) => void,
  onError?: (err: any) => void,
  showErrorToast = true,
): Promise<ApiResponse<T>> => {
  // Implementation...
}
```

### Parameters

- `fn`: A function that performs the actual HTTP request. It accepts an `AbortSignal` for cancellation support and returns a Promise with the standardized `ApiResponse`.
- `onSuccess`: Optional callback function that receives the data on successful response.
- `onError`: Optional callback function that receives the error on failed response.
- `showErrorToast`: Boolean flag to control whether toast notifications should be shown for errors (defaults to true).

### Return Value

The function returns a Promise that resolves to an `ApiResponse<T>` object containing the standardized response format, **even for error cases**. This is a key aspect of the global error handling approach.

### Error Handling Flow

1. **Error Detection**: Catch any errors from the API request
2. **Error State**: Set the error state for the hook
3. **Toast Notification**: Display a toast notification (if enabled)
4. **Error Callback**: Call the onError callback (if provided)
5. **Standardized Response**: Return a standardized error response
6. **Loading State**: Set loading state to false

This approach allows components to handle API requests without worrying about error handling, as all errors are handled globally by this function.

### Example Usage

```typescript
// Inside a component using the useHttp hook
const { post, loading, error } = useHttp();

async function handleSubmit() {
  // No try/catch needed - errors are handled globally
  const response = await post({
    url: '/api/users',
    data: userData,
    onSuccess: (data) => {
      console.log('User created:', data);
    },
  });
  
  // Check if the response was successful
  if (isApiSuccess(response)) {
    // Handle successful response
    router.push('/users');
  }
  // No need to handle errors - they're handled globally
}
```

## Best Practices

1. **Don't use try/catch blocks** for API requests unless you need custom error handling.
2. Always check if the response was successful using the `isApiSuccess` helper function.
3. Use the `onSuccess` callback for handling specific success cases.
4. Use the `onError` callback only if you need custom error handling.
5. Use the `showErrorToast` parameter to control toast notifications for specific requests.
6. Use the loading state for UI feedback during requests.
7. Use the error state for displaying custom error messages in the UI if needed.

## Implementation Details

The `execute()` function is implemented using React's `useCallback` hook to ensure stable function references across renders. It also uses refs to track the mounted state of the component and to manage the abort controller for request cancellation.

The function is designed to work with the standardized API response format and provides a consistent way to handle both success and error cases without requiring try/catch blocks in individual components.
