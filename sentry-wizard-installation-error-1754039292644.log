npm error code ERESOLVE
npm error ERESOLVE could not resolve
npm error
npm error While resolving: react-day-picker@8.10.1
npm error Found: date-fns@4.1.0
npm error node_modules/date-fns
npm error   date-fns@"^4.1.0" from the root project
npm error
npm error Could not resolve dependency:
npm error peer date-fns@"^2.28.0 || ^3.0.0" from react-day-picker@8.10.1
npm error node_modules/react-day-picker
npm error   react-day-picker@"^8.10.1" from the root project
npm error
npm error Conflicting peer dependency: date-fns@3.6.0
npm error node_modules/date-fns
npm error   peer date-fns@"^2.28.0 || ^3.0.0" from react-day-picker@8.10.1
npm error   node_modules/react-day-picker
npm error     react-day-picker@"^8.10.1" from the root project
npm error
npm error Fix the upstream dependency conflict, or retry
npm error this command with --force or --legacy-peer-deps
npm error to accept an incorrect (and potentially broken) dependency resolution.
npm error
npm error
npm error For a full report see:
npm error /Users/<USER>/.npm/_logs/2025-08-01T09_08_01_976Z-eresolve-report.txt
npm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-08-01T09_08_01_976Z-debug-0.log
