import type { VariantProps } from 'class-variance-authority';
import React from 'react';
import { useSidebar } from '@/shared/contexts/SidebarContext';
import { backdropVariants } from './backdrop-variants';

type BackdropProps = {
  variant?: VariantProps<typeof backdropVariants>['variant'];
};

/**
 * Backdrop Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a backdrop for mobile sidebar.
 */
const Backdrop: React.FC<BackdropProps> = ({
  variant = 'default',
}) => {
  const { isMobileOpen, toggleMobileSidebar } = useSidebar();

  if (!isMobileOpen) {
    return null;
  }

  return (
    <div
      className={backdropVariants({ variant })}
      role="button"
      tabIndex={0}
      onClick={toggleMobileSidebar}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggleMobileSidebar();
        }
      }}
    />
  );
};

export default Backdrop;
