'use client';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSidebar } from '../contexts/SidebarContext';
import {
  ChevronDownIcon,
  DocumentIcon,
  GridIcon,
  UsersIcon,
} from '../icons/index';
import { Permission } from '@/core/rbac/permissions';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { useTranslations } from 'next-intl';

type NavItem = {
  name: string;
  icon: React.ReactNode;
  path?: string;
  permission?: Permission;
  subItems?: { name: string; path: string; pro?: boolean; new?: boolean; permission?: Permission }[];
};

const othersItems: NavItem[] = [
  // {
  //   icon: <PieChartIcon />,
  //   name: 'Charts',
  //   subItems: [
  //     { name: 'Line Chart', path: '/dashboard/line-chart', pro: false },
  //     { name: 'Bar Chart', path: '/dashboard/bar-chart', pro: false },
  //   ],
  // },
  // {
  //   icon: <BoxCubeIcon />,
  //   name: 'UI Elements',
  //   subItems: [
  //     { name: 'Alerts', path: '/dashboard/alerts', pro: false },
  //     { name: 'Avatar', path: '/dashboard/avatars', pro: false },
  //     { name: 'Badge', path: '/dashboard/badge', pro: false },
  //     { name: 'Buttons', path: '/dashboard/buttons', pro: false },
  //     { name: 'Images', path: '/dashboard/images', pro: false },
  //     { name: 'Videos', path: '/dashboard/videos', pro: false },
  //   ],
  // },
  // {
  //   icon: <PlugInIcon />,
  //   name: 'Authentication',
  //   subItems: [
  //     { name: 'Sign In', path: '/signin', pro: false },
  //     { name: 'Sign Up', path: '/signup', pro: false },
  //   ],
  // },
];

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const pathname = usePathname();

  const t = useTranslations('main');

  const navItems: NavItem[] = [
    {
      icon: <GridIcon />,
      name: t('project'),
      path: '/dashboard/projects',
      permission: Permission.VIEW_PROJECTS,
    },
    // {
    //   icon: <PageIcon />,
    //   name: 'Prompts',
    //   path: '/dashboard/prompts',
    //   permission: Permission.VIEW_PROMPTS,
    // },

    // {
    //   icon: <RocketLaunchIcon />,
    //   name: 'Workflow Prompts',
    //   path: '/dashboard/steps',
    //   permission: Permission.VIEW_STEPS,
    // },
    {
      icon: <UsersIcon />,
      name: t('teamMembers'),
      path: '/dashboard/team-members',
      permission: Permission.VIEW_USERS,
    },
    {
      icon: <DocumentIcon />,
      name: t('framework'),
      path: '/dashboard/frameworks-templates',
      permission: Permission.VIEW_FRAMEWORKS_TEMPLATES,
    },
  ];

  const [openSubmenu, setOpenSubmenu] = useState<{
    type: 'main' | 'others';
    index: number;
  } | null>(null);
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>(
    {},
  );
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // const isActive = (path: string) => path === pathname;
  const isActive = useCallback((path: string) => pathname.includes(path), [pathname]);

  useEffect(() => {
    // Check if the current path matches any submenu item
    let submenuMatched = false;
    ['main', 'others'].forEach((menuType) => {
      const items = menuType === 'main' ? navItems : othersItems;
      items.forEach((nav, index) => {
        if (nav.subItems) {
          nav.subItems.forEach((subItem) => {
            if (isActive(subItem.path)) {
              setOpenSubmenu({
                type: menuType as 'main' | 'others',
                index,
              });
              submenuMatched = true;
            }
          });
        }
      });
    });

    // If no submenu item matches, close the open submenu
    if (!submenuMatched) {
      setOpenSubmenu(null);
    }
  }, [pathname, isActive]);

  useEffect(() => {
    // Set the height of the submenu items when the submenu is opened
    if (openSubmenu !== null) {
      const key = `${openSubmenu.type}-${openSubmenu.index}`;
      if (subMenuRefs.current[key]) {
        setSubMenuHeight(prevHeights => ({
          ...prevHeights,
          [key]: subMenuRefs.current[key]?.scrollHeight || 0,
        }));
      }
    }
  }, [openSubmenu]);

  const handleSubmenuToggle = (index: number, menuType: 'main' | 'others') => {
    setOpenSubmenu((prevOpenSubmenu) => {
      if (
        prevOpenSubmenu
        && prevOpenSubmenu.type === menuType
        && prevOpenSubmenu.index === index
      ) {
        return null;
      }
      return { type: menuType, index };
    });
  };

  const renderMenuItems = (
    navItems: NavItem[],
    menuType: 'main' | 'others',
  ) => (
    <ul className="flex flex-col gap-4">
      {navItems.map((nav, index) => (
        <PermissionGuard permission={nav.permission} key={nav.name}>
          <li>
            {nav.subItems
              ? (
                  <button
                    type="button"
                    onClick={() => handleSubmenuToggle(index, menuType)}
                    className={`menu-item group  ${openSubmenu?.type === menuType && openSubmenu?.index === index
                      ? 'menu-item-active'
                      : 'menu-item-inactive'
                    } cursor-pointer ${!isExpanded && !isHovered
                      ? 'lg:justify-center'
                      : 'lg:justify-start'
                    }`}
                  >
                    <span
                      className={`size-5 ${openSubmenu?.type === menuType && openSubmenu?.index === index
                        ? 'menu-item-icon-active'
                        : 'menu-item-icon-inactive'
                      }`}
                    >
                      {nav.icon}
                    </span>
                    {(isExpanded || isHovered || isMobileOpen) && (
                      <span className="menu-item-text">{nav.name}</span>
                    )}
                    {(isExpanded || isHovered || isMobileOpen) && (
                      <ChevronDownIcon
                        className={`ml-auto size-5 transition-transform duration-200  ${openSubmenu?.type === menuType
                        && openSubmenu?.index === index
                          ? 'rotate-180 text-brand-500'
                          : ''
                        }`}
                      />
                    )}
                  </button>
                )
              : (
                  nav.path && (
                    <Link
                      href={nav.path}
                      className={`menu-item group ${isActive(nav.path) ? 'menu-item-active' : 'menu-item-inactive'
                      }`}
                    >
                      <span
                        className={`size-5 ${isActive(nav.path)
                          ? 'menu-item-icon-active'
                          : 'menu-item-icon-inactive'
                        }`}
                      >
                        {nav.icon}
                      </span>
                      {(isExpanded || isHovered || isMobileOpen) && (
                        <span className="menu-item-text">{nav.name}</span>
                      )}
                    </Link>
                  )
                )}
            {nav.subItems && (isExpanded || isHovered || isMobileOpen) && (
              <div
                ref={(el) => {
                  subMenuRefs.current[`${menuType}-${index}`] = el;
                }}
                className="overflow-hidden transition-all duration-300"
                style={{
                  height:
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? `${subMenuHeight[`${menuType}-${index}`]}px`
                    : '0px',
                }}
              >
                <ul className="mt-2 space-y-1 ml-9">
                  {nav.subItems.map(subItem => (
                    <li key={subItem.name}>
                      <Link
                        href={subItem.path}
                        className={`menu-dropdown-item ${isActive(subItem.path)
                          ? 'menu-dropdown-item-active'
                          : 'menu-dropdown-item-inactive'
                        }`}
                      >
                        {subItem.name}
                        <span className="flex items-center gap-1 ml-auto">
                          {subItem.new && (
                            <span
                              className={`ml-auto ${isActive(subItem.path)
                                ? 'menu-dropdown-badge-active'
                                : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge `}
                            >
                              new
                            </span>
                          )}
                          {subItem.pro && (
                            <span
                              className={`ml-auto ${isActive(subItem.path)
                                ? 'menu-dropdown-badge-active'
                                : 'menu-dropdown-badge-inactive'
                              } menu-dropdown-badge `}
                            >
                              pro
                            </span>
                          )}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </li>
        </PermissionGuard>
      ))}
    </ul>
  );

  return (
    <aside
      className={`fixed mt-16 flex flex-col lg:mt-0 top-0 px-5 left-0 bg-background text-foreground h-screen transition-all duration-300 ease-in-out z-50 border-r border-border
        ${isExpanded || isMobileOpen
      ? 'w-[240px]'
      : isHovered
        ? 'w-[240px]'
        : 'w-[90px]'
    }
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`py-8 flex ${!isExpanded && !isHovered ? 'lg:justify-center' : 'justify-center'
        }`}
      >
        <Link href="/dashboard/projects">
          {isExpanded || isHovered || isMobileOpen
            ? (
                <>
                  <Image
                    className="inline-block dark:hidden"
                    src="/images/logo/mvv-logo.png"
                    alt="Logo"
                    width={80}
                    height={80}
                  />
                  <Image
                    className="hidden dark:inline-block"
                    src="/images/logo/mvv-logo.png"
                    alt="Logo"
                    width={80}
                    height={80}
                  />
                </>
              )
            : (
                <Image
                  src="/images/logo/mvv-logo.png"
                  alt="Logo"
                  width={80}
                  height={80}
                />
              )}
        </Link>
      </div>
      <div className="flex flex-col overflow-y-auto duration-300 ease-linear no-scrollbar">
        <nav className="mb-6">
          <div className="flex flex-col gap-4">
            <div>
              {/* <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                  ? 'lg:justify-center'
                  : 'justify-start'
                }`}
              >
                {isExpanded || isHovered || isMobileOpen
                  ? (
                      'Menu'
                    )
                  : (
                      <HorizontaLDots />
                    )}
              </h2> */}
              {renderMenuItems(navItems, 'main')}
            </div>

            {/* <div className="">
              <h2
                className={`mb-4 text-xs uppercase flex leading-[20px] text-gray-400 ${!isExpanded && !isHovered
                  ? 'lg:justify-center'
                  : 'justify-start'
                }`}
              >
                {isExpanded || isHovered || isMobileOpen
                  ? (
                      'Others'
                    )
                  : (
                      <HorizontaLDots />
                    )}
              </h2>
              {renderMenuItems(othersItems, 'others')}
            </div> */}
          </div>
        </nav>
      </div>
    </aside>
  );
};

export default AppSidebar;
