import { cva } from 'class-variance-authority';

/**
 * Backdrop Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The backdrop variants are defined using the cva utility for consistent styling.
 */
export const backdropVariants = cva(
  'fixed inset-0 z-40 lg:hidden',
  {
    variants: {
      variant: {
        default: 'bg-foreground/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
