// Session expiration constants
export const SESSION_EXPIRED_COOKIE_KEY = 'session_expired_indicator';
export const SESSION_EXPIRED_EVENT_NAME = 'session-expired';

export const X_TENANT_ID = 'mvv';

export const AGENT_NAME_COPILOTKIT = {
  SUMMARIZE: 'client_summarize_flow',
  ASSESSMENT: 'client_assessment_flow',
  ANALYSIS: 'brief_analysis_flow',
  EDITING: 'content_editing_flow',
  ANSWER: 'answer_question_brief_flow',
  SCOPE: 'scope_of_work_flow',
  QUOTATION: 'quotation_flow',
};

export const AGENT_ROUTE_NAME = 'base_flow';

export const MESSAGE_SEND_ROUTE_AGENT = 'Router to direct to appropriate flows based on agent';

export const LOCALSTORAGE_KEY = {
  TASK_ID: 'current_task_id',
  STEP_ID: 'current_step_id',
};
