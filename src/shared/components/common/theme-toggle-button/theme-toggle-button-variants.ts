import { cva } from 'class-variance-authority';

/**
 * ThemeToggleButton Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The button variants are defined using the cva utility for consistent styling.
 */
export const themeToggleButtonVariants = cva(
  'relative flex items-center justify-center transition-colors rounded-full h-11 w-11',
  {
    variants: {
      variant: {
        default: 'bg-background border border-border text-muted-foreground hover:bg-accent hover:text-foreground',
        subtle: 'bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
