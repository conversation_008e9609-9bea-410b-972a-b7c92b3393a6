'use client';

import type { VariantProps } from 'class-variance-authority';
import React from 'react';
import { cn } from '@/shared/utils/utils';
import {
  componentCardBodyVariants,
  componentCardDescriptionVariants,
  componentCardHeaderVariants,
  componentCardTitleVariants,
  componentCardVariants,
} from './component-card/component-card-variants';

type ComponentCardProps = {
  title: string;
  children: React.ReactNode;
  className?: string; // Additional custom classes for styling
  desc?: string; // Description text
  variant?: VariantProps<typeof componentCardVariants>['variant'];
};

/**
 * ComponentCard Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a consistent card layout for displaying components with a title and description.
 */
const ComponentCard: React.FC<ComponentCardProps> = ({
  title,
  children,
  className = '',
  desc = '',
  variant = 'default',
}) => {
  // For backward compatibility, check if this is a project monitoring component
  // by looking for amber border in className
  if (className?.includes('border-amber') && variant === 'default') {
    variant = 'amber';
  }

  return (
    <div className={cn(componentCardVariants({ variant }), className)}>
      {/* Card Header */}
      <div className={cn(componentCardHeaderVariants({ variant }))}>
        <h3 className={cn(componentCardTitleVariants({ variant }))}>
          {title}
        </h3>
        {desc && (
          <p className={cn(componentCardDescriptionVariants({ variant }))}>
            {desc}
          </p>
        )}
      </div>

      {/* Card Body */}
      <div className={cn(componentCardBodyVariants({ variant }))}>
        <div className="space-y-6">{children}</div>
      </div>
    </div>
  );
};

export default ComponentCard;
