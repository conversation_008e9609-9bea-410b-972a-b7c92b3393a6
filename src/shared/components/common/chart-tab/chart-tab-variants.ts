import { cva } from 'class-variance-authority';

/**
 * ChartTab Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The chart tab container variants are defined using the cva utility for consistent styling.
 */
export const chartTabContainerVariants = cva(
  'flex items-center gap-0.5 rounded-lg p-0.5',
  {
    variants: {
      variant: {
        default: 'bg-muted',
        primary: 'bg-primary-100 dark:bg-primary-900/20',
        secondary: 'bg-secondary-100 dark:bg-secondary-900/20',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * ChartTab Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The chart tab button variants are defined using the cva utility for consistent styling.
 */
export const chartTabButtonVariants = cva(
  'px-3 py-2 font-medium w-full rounded-md text-theme-sm',
  {
    variants: {
      variant: {
        default: 'hover:text-foreground',
        primary: 'hover:text-primary-900 dark:hover:text-primary-200',
        secondary: 'hover:text-secondary-900 dark:hover:text-secondary-200',
      },
      selected: {
        true: 'shadow-theme-xs bg-background text-foreground',
        false: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
      selected: false,
    },
  },
);
