'use client';

import React, { useState } from 'react';
import { cn } from '@/shared/utils/utils';
import { chartTabButtonVariants, chartTabContainerVariants } from './chart-tab/chart-tab-variants';

type ChartTabProps = {
  options?: string[];
  defaultSelected?: string;
  onChange?: (selected: string) => void;
  variant?: 'default' | 'primary' | 'secondary';
  className?: string;
};

/**
 * ChartTab Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a tabbed interface for switching between chart views.
 */
const ChartTab: React.FC<ChartTabProps> = ({
  options = ['Monthly', 'Quarterly', 'Annually'],
  defaultSelected = 'Monthly',
  onChange,
  variant = 'default',
  className,
}) => {
  const [selected, setSelected] = useState<string>(defaultSelected);

  const handleSelect = (option: string) => {
    setSelected(option);
    if (onChange) {
      onChange(option);
    }
  };

  return (
    <div className={cn(chartTabContainerVariants({ variant }), className)}>
      {options.map(option => (
        <button
          key={option}
          onClick={() => handleSelect(option)}
          className={cn(
            chartTabButtonVariants({
              variant,
              selected: selected === option,
            }),
          )}
        >
          {option}
        </button>
      ))}
    </div>
  );
};

export default ChartTab;
