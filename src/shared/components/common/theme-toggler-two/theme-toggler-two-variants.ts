import { cva } from 'class-variance-authority';

/**
 * ThemeTogglerTwo Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The button variants are defined using the cva utility for consistent styling.
 */
export const themeTogglerTwoVariants = cva(
  'inline-flex items-center justify-center rounded-full transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        subtle: 'bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'size-11',
        sm: 'size-9',
        lg: 'size-14',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'lg',
    },
  },
);
