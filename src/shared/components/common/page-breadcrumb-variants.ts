import { cva } from 'class-variance-authority';

/**
 * PageBreadcrumb Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The breadcrumb variants are defined using the cva utility for consistent styling.
 */
export const pageBreadcrumbContainerVariants = cva(
  'flex flex-wrap items-center justify-between gap-3 mb-6',
  {
    variants: {
      variant: {
        default: '',
        compact: 'mb-4',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * PageBreadcrumb Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The breadcrumb title variants are defined using the cva utility for consistent styling.
 */
export const pageBreadcrumbTitleVariants = cva(
  'text-xl font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
        compact: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
