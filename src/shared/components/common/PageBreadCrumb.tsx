import type { VariantProps } from 'class-variance-authority';
import React from 'react';
import { pageBreadcrumbContainerVariants, pageBreadcrumbTitleVariants } from './page-breadcrumb-variants';

type BreadcrumbProps = {
  pageTitle: string;
  variant?: VariantProps<typeof pageBreadcrumbContainerVariants>['variant'];
};

/**
 * PageBreadcrumb Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a page title as a breadcrumb.
 */
const PageBreadcrumb: React.FC<BreadcrumbProps> = ({
  pageTitle,
  variant = 'default',
}) => {
  return (
    <div className={pageBreadcrumbContainerVariants({ variant })}>
      <h2
        className={pageBreadcrumbTitleVariants({ variant })}
        x-text="pageName"
      >
        {pageTitle}
      </h2>
    </div>
  );
};

export default PageBreadcrumb;
