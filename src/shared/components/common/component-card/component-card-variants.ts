import { cva } from 'class-variance-authority';

/**
 * ComponentCard Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card variants are defined using the cva utility for consistent styling.
 */
export const componentCardVariants = cva(
  'rounded-2xl border',
  {
    variants: {
      variant: {
        default: 'border-border bg-card text-card-foreground',
        primary: 'border-primary-200 bg-primary-50/30 dark:border-primary-800 dark:bg-primary-900/10',
        secondary: 'border-secondary-200 bg-secondary-50/30 dark:border-secondary-800 dark:bg-secondary-900/10',
        warning: 'border-warning-200 bg-warning-50/30 dark:border-warning-800 dark:bg-warning-900/10',
        success: 'border-success-200 bg-success-50/30 dark:border-success-800 dark:bg-success-900/10',
        destructive: 'border-destructive-200 bg-destructive-50/30 dark:border-destructive-800 dark:bg-destructive-900/10',
        amber: 'border-amber-200 bg-amber-50/30 dark:border-amber-800 dark:bg-amber-900/10',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const componentCardHeaderVariants = cva(
  'px-6 py-5',
  {
    variants: {
      variant: {
        default: '',
        primary: 'bg-primary-100/50 dark:bg-primary-900/20',
        secondary: 'bg-secondary-100/50 dark:bg-secondary-900/20',
        warning: 'bg-warning-100/50 dark:bg-warning-900/20',
        success: 'bg-success-100/50 dark:bg-success-900/20',
        destructive: 'bg-destructive-100/50 dark:bg-destructive-900/20',
        amber: 'bg-amber-100/50 dark:bg-amber-900/20',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const componentCardTitleVariants = cva(
  'text-base font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
        primary: 'text-primary-900 dark:text-primary-200',
        secondary: 'text-secondary-900 dark:text-secondary-200',
        warning: 'text-warning-900 dark:text-warning-200',
        success: 'text-success-900 dark:text-success-200',
        destructive: 'text-destructive-900 dark:text-destructive-200',
        amber: 'text-amber-900 dark:text-amber-200',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const componentCardDescriptionVariants = cva(
  'mt-1 text-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary-800 dark:text-primary-300/80',
        secondary: 'text-secondary-800 dark:text-secondary-300/80',
        warning: 'text-warning-800 dark:text-warning-300/80',
        success: 'text-success-800 dark:text-success-300/80',
        destructive: 'text-destructive-800 dark:text-destructive-300/80',
        amber: 'text-amber-800 dark:text-amber-300/80',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const componentCardBodyVariants = cva(
  'p-4 border-t sm:p-6',
  {
    variants: {
      variant: {
        default: 'border-border',
        primary: 'border-primary-200 dark:border-primary-800/50',
        secondary: 'border-secondary-200 dark:border-secondary-800/50',
        warning: 'border-warning-200 dark:border-warning-800/50',
        success: 'border-success-200 dark:border-success-800/50',
        destructive: 'border-destructive-200 dark:border-destructive-800/50',
        amber: 'border-amber-200 dark:border-amber-800/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
