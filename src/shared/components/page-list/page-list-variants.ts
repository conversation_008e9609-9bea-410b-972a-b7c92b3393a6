import { cva } from 'class-variance-authority';

/**
 * PageList Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The page list variants are defined using the cva utility for consistent styling.
 */

export const pageListContainerVariants = cva(
  'flex flex-col h-full',
  {
    variants: {
      variant: {
        default: 'bg-background',
        compact: 'bg-background',
        bordered: 'bg-background border border-border rounded-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListContentVariants = cva(
  'flex-1 overflow-y-auto px-6 py-4',
);

export const pageListGridVariants = cva(
  'grid gap-4',
  {
    variants: {
      cols: {
        1: 'grid-cols-1',
        2: 'grid-cols-1 md:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
        5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
        6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 3xl:grid-cols-6',
        responsive: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
        auto: 'grid-cols-[repeat(auto-fill,minmax(280px,1fr))]',
      },
    },
    defaultVariants: {
      cols: 'responsive',
    },
  },
);

export const pageListEmptyMessageVariants = cva(
  'flex items-center justify-center py-12 text-center',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        compact: 'text-muted-foreground py-8',
        bordered: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListErrorContainerVariants = cva(
  'flex flex-col items-center justify-center py-12 text-center space-y-4',
  {
    variants: {
      variant: {
        default: '',
        compact: 'py-8',
        bordered: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListErrorIconVariants = cva(
  'size-12',
  {
    variants: {
      variant: {
        default: 'text-destructive',
        compact: 'text-destructive size-10',
        bordered: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListErrorMessageVariants = cva(
  'text-sm max-w-md',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        compact: 'text-muted-foreground text-xs',
        bordered: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListLoadingMessageVariants = cva(
  'text-sm font-medium',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        compact: 'text-muted-foreground text-xs',
        bordered: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListHeaderVariants = cva(
  'p-6 border-b bg-background',
  {
    variants: {
      variant: {
        default: '',
        compact: 'p-4',
        bordered: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListFooterVariants = cva(
  'flex justify-center mt-6',
  {
    variants: {
      variant: {
        default: '',
        compact: 'mt-4',
        bordered: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const pageListTotalLabelVariants = cva(
  'text-muted-foreground',
  {
    variants: {
      variant: {
        default: '',
        compact: 'text-sm',
        bordered: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
