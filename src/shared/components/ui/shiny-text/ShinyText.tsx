'use client';

import type { JSX } from 'react';
import { motion } from 'framer-motion';
import React, { useMemo } from 'react';
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';
import { cn } from '@/shared/utils/utils';
import { shinyTextVariants } from './shiny-text-variants';

type ShinyTextProps = {
  children: string;
  as?: React.ElementType;
  className?: string;
  duration?: number;
  spread?: number;
  variant?: 'default' | 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
};

/**
 * ShinyText Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays text with a shiny animation effect.
 */
export function ShinyText({
  children,
  as: Component = 'p',
  className,
  duration = 1,
  spread = 2,
  variant = 'default',
  size,
  weight,
}: ShinyTextProps) {
  const { classesObj } = useThemeStyles();
  const MotionComponent = motion(Component as keyof JSX.IntrinsicElements);

  const dynamicSpread = useMemo(() => {
    return children.length * spread;
  }, [children, spread]);

  // Use classesObj for theme-aware classes
  const shinyEffectClasses = classesObj(
    '[background-repeat:no-repeat,padding-box]',
    {
      light: '[--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))]',
      dark: '[--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))]',
    },
  );

  return (
    <MotionComponent
      className={cn(
        shinyTextVariants({ variant, size, weight }),
        shinyEffectClasses,
        className,
      )}
      initial={{ backgroundPosition: '100% center' }}
      animate={{ backgroundPosition: '0% center' }}
      transition={{
        repeat: Infinity,
        duration,
        ease: 'linear',
      }}
      style={
        {
          '--spread': `${dynamicSpread}px`,
          'backgroundImage': `var(--bg), linear-gradient(var(--base-color), var(--base-color))`,
        } as React.CSSProperties
      }
    >
      {children}
    </MotionComponent>
  );
}
