import { cva } from 'class-variance-authority';

/**
 * ShinyText Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The shiny text variants are defined using the cva utility for consistent styling.
 */
export const shinyTextVariants = cva(
  'relative inline-block bg-[length:250%_100%,auto] bg-clip-text text-transparent',
  {
    variants: {
      variant: {
        default: '[--base-color:var(--color-muted-foreground)] [--base-gradient-color:var(--color-foreground)]',
        primary: '[--base-color:var(--color-primary-300)] [--base-gradient-color:var(--color-primary)]',
        secondary: '[--base-color:var(--color-secondary-300)] [--base-gradient-color:var(--color-secondary)]',
        accent: '[--base-color:var(--color-accent-300)] [--base-gradient-color:var(--color-accent)]',
      },
      size: {
        'sm': 'text-sm',
        'md': 'text-base',
        'lg': 'text-lg',
        'xl': 'text-xl',
        '2xl': 'text-2xl',
        '3xl': 'text-3xl',
      },
      weight: {
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      weight: 'normal',
    },
  },
);
