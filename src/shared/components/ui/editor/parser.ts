import { remark } from 'remark';
import remarkHtml from 'remark-html';

import rehypeParse from 'rehype-parse';
import rehypeRemark from 'rehype-remark';
import remarkStringify from 'remark-stringify';

import TurndownService from 'turndown';
import { marked } from 'marked';

export function markdownToHtml(markdownText: string) {
  const file = remark().use(remarkHtml).processSync(markdownText);
  return String(file);
}

export function htmlToMarkdown(htmlText: string) {
  const file = remark()
    .use(rehypeParse, { emitParseErrors: true, duplicateAttribute: false })
    .use(rehypeRemark)
    .use(remarkStringify, {
      bullet: '-',
      fences: true,
      listItemIndent: 'one',
    })
    .processSync(htmlText);

  return String(file);
}

export function markdownToHtmlVer2(markdownText: string) {
  marked.setOptions({
    gfm: true,
    breaks: true,
  });

  return marked(markdownText);
}

export function htmlToMarkdownVer2(htmlText: string) {
  const turndownService = new TurndownService();

  turndownService.addRule('table', {
    filter: ['table'],
    replacement: (_content, node) => {
      const rows = Array.from(node.querySelectorAll('tr')).map((row, rowIndex) => {
        const cells = Array.from(row.querySelectorAll('td, th')).map(cell => ` ${(cell.textContent ?? '').trim()} `);
        const rowMarkdown = `|${cells.join('|')}|`;

        if (rowIndex === 0) {
          const separator = `|${cells.map(() => '---').join('|')}|`;
          return `${rowMarkdown}\n${separator}`;
        }

        return rowMarkdown;
      });

      return rows.join('\n');
    },
  });

  return turndownService.turndown(htmlText);
}

export async function markdownToHTMLToDocFile(markdownText: string) {
  const html = await markdownToHtmlVer2(markdownText);
  return html
    .replace(/<table>/g, '<table style="border-collapse: collapse; width: 100%;">')
    .replace(/<th>/g, '<th style="border: 1px solid black; padding: 4px;">')
    .replace(/<td>/g, '<td style="border: 1px solid black; padding: 4px;">');
}
