'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import 'react-quill-new/dist/quill.snow.css';
import { htmlToMarkdownVer2, markdownToHtmlVer2 } from './parser';
import type { EditorContentChanged } from '@/shared/types/global';
import dynamic from 'next/dynamic';

// const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

// const TOOLBAR_OPTIONS = [
//   [{ header: [1, 2, 3, false] }],
//   ['bold', 'italic', 'underline', 'strike', 'blockquote', 'link'],
//   [{ list: 'ordered' }, { list: 'bullet' }],
//   [{ indent: '-1' }, { indent: '+1' }],
//   ['clean'],
// ];
const JoditEditor = dynamic(() => import('jodit-react'), { ssr: false });

export default function Editor({
  value: initialValue = '',
  onChange,
  debounceMs = 300,
  classCustom,
}: {
  value?: string;
  onChange?: (value: EditorContentChanged) => void;
  debounceMs?: number;
  classCustom?: string;
}) {
  const [value, setValue] = useState<string>('');

  const config = useMemo(
    () => ({
      readonly: false,
      placeholder: '',
    }),
    [],
  );
  useEffect(() => {
    async function fetchInitialValue() {
      const html = await markdownToHtmlVer2(initialValue || '');
      setValue(html);
    }
    fetchInitialValue();
  }, [initialValue]);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedOnChange = useCallback((content: string) => {
    if (onChange) {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        onChange({
          html: content,
          markdown: htmlToMarkdownVer2(content),
        });
      }, debounceMs);
    }
  }, [onChange, debounceMs]);

  const handleChange = (content: string) => {
    debouncedOnChange(content);
  };

  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
  // <ReactQuill
  //   theme="snow"
  //   placeholder="Start writing..."
  //   modules={{
  //     toolbar: {
  //       container: TOOLBAR_OPTIONS,
  //     },

  //   }}
  //   value={value}
  //   onChange={handleChange}
  // />
    <JoditEditor className={`jodit-react-container ${classCustom}`} value={value} config={config} onChange={handleChange} />
  );
}
