'use client';

import React from 'react';
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';
import { cn } from '@/shared/utils/utils';

type ThemedCardProps = {
  title: string;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'outline';
};

/**
 * ThemedCard Component
 *
 * This component demonstrates how to use the new theming system.
 * It uses the useThemeStyles hook to generate theme-aware classes.
 */
export const ThemedCard: React.FC<ThemedCardProps> = ({
  title,
  children,
  className,
  variant = 'default',
}) => {
  const { classesObj } = useThemeStyles();

  // Base classes that apply to all variants
  const baseClasses = 'rounded-lg border shadow-sm';

  // Variant-specific classes
  const variantClasses = {
    default: classesObj(
      'bg-card text-card-foreground',
      {
        // No additional theme-specific classes needed as they're handled by CSS variables
      },
    ),
    primary: classesObj(
      'border-primary-200',
      {
        light: 'bg-primary-50 text-primary-900',
        dark: 'bg-primary-900/10 border-primary-800 text-primary-50',
      },
    ),
    secondary: classesObj(
      'border-secondary-200',
      {
        light: 'bg-secondary-50 text-secondary-900',
        dark: 'bg-secondary-900/10 border-secondary-800 text-secondary-50',
      },
    ),
    outline: classesObj(
      'bg-transparent',
      {
        light: 'border-border text-foreground',
        dark: 'border-border text-foreground',
      },
    ),
  };

  // Header classes based on variant
  const headerClasses = {
    default: classesObj(
      'px-6 py-4 border-b',
      {
        light: 'border-border/50',
        dark: 'border-border/50',
      },
    ),
    primary: classesObj(
      'px-6 py-4 border-b',
      {
        light: 'border-primary-100 bg-primary-100/50',
        dark: 'border-primary-900/20 bg-primary-900/20',
      },
    ),
    secondary: classesObj(
      'px-6 py-4 border-b',
      {
        light: 'border-secondary-100 bg-secondary-100/50',
        dark: 'border-secondary-900/20 bg-secondary-900/20',
      },
    ),
    outline: classesObj(
      'px-6 py-4 border-b',
      {
        light: 'border-border',
        dark: 'border-border',
      },
    ),
  };

  // Title classes based on variant
  const titleClasses = {
    default: 'text-lg font-medium',
    primary: classesObj(
      'text-lg font-medium',
      {
        light: 'text-primary-900',
        dark: 'text-primary-50',
      },
    ),
    secondary: classesObj(
      'text-lg font-medium',
      {
        light: 'text-secondary-900',
        dark: 'text-secondary-50',
      },
    ),
    outline: 'text-lg font-medium',
  };

  // Content classes
  const contentClasses = 'px-6 py-4';

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)}>
      <div className={headerClasses[variant]}>
        <h3 className={titleClasses[variant]}>{title}</h3>
      </div>
      <div className={contentClasses}>
        {children}
      </div>
    </div>
  );
};

export default ThemedCard;
