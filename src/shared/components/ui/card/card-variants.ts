import { cva } from 'class-variance-authority';

/**
 * Card Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card variants are defined using the cva utility for consistent styling.
 */
export const cardVariants = cva(
  'flex flex-col gap-6 rounded-lg border shadow-sm',
  {
    variants: {
      variant: {
        default: 'bg-card text-card-foreground border-border',
        primary: 'bg-primary/5 text-primary-foreground border-primary/20',
        secondary: 'bg-secondary/10 text-secondary-foreground border-secondary/20',
        outline: 'bg-background text-foreground border-border',
      },
      size: {
        default: 'py-6',
        sm: 'py-4',
        lg: 'py-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);
