import type { VariantProps } from 'class-variance-authority';
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { modelSelectorVariants } from './model-selector-variants';

export type ModelOption = {
  id: string;
  label: string;
};

export const modelOptions: ModelOption[] = [
  { id: 'openai/gpt-4o-mini', label: 'ChatGPT 4o-mini' },
  { id: 'openai/gpt-4o', label: 'ChatGPT 4o' },
  { id: 'deepseek/deepseek-chat-v3-0324', label: 'Deepseek V3' },
];

type ModelSelectorProps = {
  selectedModel: string;
  onChange: (modelId: string) => void;
  disabled?: boolean;
  variant?: VariantProps<typeof modelSelectorVariants>['variant'];
  className?: string;
};

/**
 * ModelSelector Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a dropdown to select AI models with customizable styling.
 */
const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onChange,
  disabled = false,
  variant = 'amber',
  className,
}) => {
  return (
    <select
      value={selectedModel}
      onChange={e => onChange(e.target.value)}
      disabled={disabled}
      className={cn(modelSelectorVariants({ variant }), className)}
    >
      {modelOptions.map(model => (
        <option key={model.id} value={model.id}>
          {model.label}
        </option>
      ))}
    </select>
  );
};

export default ModelSelector;
