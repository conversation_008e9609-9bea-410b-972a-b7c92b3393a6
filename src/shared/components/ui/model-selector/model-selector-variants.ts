import { cva } from 'class-variance-authority';

/**
 * ModelSelector Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The selector variants are defined using the cva utility for consistent styling.
 */
export const modelSelectorVariants = cva(
  'ml-2 px-2 py-1 text-sm border rounded-md focus:outline-none focus:ring-2',
  {
    variants: {
      variant: {
        default: 'border-primary focus:ring-primary/50 dark:bg-background dark:border-primary-700 dark:text-foreground',
        amber: 'border-amber-300 focus:ring-amber-500 dark:bg-gray-800 dark:border-amber-700 dark:text-white',
        secondary: 'border-secondary focus:ring-secondary/50 dark:bg-background dark:border-secondary-700 dark:text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
