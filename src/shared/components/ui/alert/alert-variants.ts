import { cva } from 'class-variance-authority';

/**
 * <PERSON><PERSON>
 *
 * This component uses semantic color tokens from our theming system.
 * The alert variants are defined using the cva utility for consistent styling.
 */
export const alertVariants = cva(
  'rounded-xl border p-4',
  {
    variants: {
      variant: {
        success: 'border-success bg-success/10 text-success-foreground',
        error: 'border-destructive bg-destructive/10 text-destructive',
        warning: 'border-warning bg-warning/10 text-warning-foreground',
        info: 'border-primary bg-primary/10 text-primary',
      },
    },
    defaultVariants: {
      variant: 'info',
    },
  },
);

export const alertIconVariants = cva(
  '-mt-0.5',
  {
    variants: {
      variant: {
        success: 'text-success',
        error: 'text-destructive',
        warning: 'text-warning',
        info: 'text-primary',
      },
    },
    defaultVariants: {
      variant: 'info',
    },
  },
);
