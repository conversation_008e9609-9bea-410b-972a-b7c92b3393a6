import { cva } from 'class-variance-authority';

/**
 * Modal Overlay Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The overlay variants are defined using the cva utility for consistent styling.
 */
export const modalOverlayVariants = cva(
  'fixed inset-0 h-full w-full backdrop-blur-[5px]',
  {
    variants: {
      variant: {
        default: 'bg-foreground/50',
        primary: 'bg-primary/50',
        secondary: 'bg-secondary/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Modal Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The content variants are defined using the cva utility for consistent styling.
 */
export const modalContentVariants = cva(
  'relative w-full',
  {
    variants: {
      variant: {
        default: 'bg-background rounded-3xl',
        primary: 'bg-primary-50 dark:bg-primary-900/10 rounded-3xl',
        secondary: 'bg-secondary-50 dark:bg-secondary-900/10 rounded-3xl',
        fullscreen: 'w-full h-full',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Modal Close Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The close button variants are defined using the cva utility for consistent styling.
 */
export const modalCloseButtonVariants = cva(
  'absolute right-3 top-3 z-999 flex items-center justify-center rounded-full transition-colors sm:right-6 sm:top-6 sm:h-11 sm:w-11 h-9.5 w-9.5',
  {
    variants: {
      variant: {
        default: 'bg-muted text-muted-foreground hover:bg-muted/80 hover:text-foreground',
        primary: 'bg-primary-100 text-primary-500 hover:bg-primary-200 hover:text-primary-700 dark:bg-primary-900/20 dark:text-primary-400 dark:hover:bg-primary-900/30 dark:hover:text-primary-300',
        secondary: 'bg-secondary-100 text-secondary-500 hover:bg-secondary-200 hover:text-secondary-700 dark:bg-secondary-900/20 dark:text-secondary-400 dark:hover:bg-secondary-900/30 dark:hover:text-secondary-300',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
