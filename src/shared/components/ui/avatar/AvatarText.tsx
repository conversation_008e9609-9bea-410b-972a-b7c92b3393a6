'use client';

import React from 'react';
import { cn } from '@/shared/utils/utils';
import { avatarTextVariants } from './avatar-text-variants';

type AvatarTextProps = {
  name: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  colorScheme?: 'primary' | 'secondary' | 'accent' | 'info' | 'success' | 'warning' | 'destructive' | 'muted';
};

/**
 * AvatarText Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays initials in a colored circle, with the color determined by the name.
 */
const AvatarText: React.FC<AvatarTextProps> = ({
  name,
  className,
  size = 'md',
  colorScheme,
}) => {
  // Generate initials from name
  const initials = name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  // Generate a consistent color scheme based on the name if not provided
  const getColorScheme = (name: string): AvatarTextProps['colorScheme'] => {
    if (colorScheme) {
      return colorScheme;
    }

    const colorSchemes: AvatarTextProps['colorScheme'][] = [
      'primary',
      'secondary',
      'accent',
      'info',
      'success',
      'warning',
      'destructive',
    ];

    const index = name
      .split('')
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colorSchemes[index % colorSchemes.length];
  };

  return (
    <div
      className={cn(
        avatarTextVariants({
          size,
          colorScheme: getColorScheme(name),
        }),
        className,
      )}
    >
      <span className="font-medium">{initials}</span>
    </div>
  );
};

export default AvatarText;
