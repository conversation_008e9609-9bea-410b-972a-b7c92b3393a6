import { cva } from 'class-variance-authority';

/**
 * AvatarText Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The avatar text variants are defined using the cva utility for consistent styling.
 */
export const avatarTextVariants = cva(
  'flex items-center justify-center rounded-full',
  {
    variants: {
      size: {
        sm: 'h-8 w-8 text-xs',
        md: 'h-10 w-10 text-sm',
        lg: 'h-12 w-12 text-base',
        xl: 'h-16 w-16 text-lg',
      },
      colorScheme: {
        primary: 'bg-primary-100 text-primary-700',
        secondary: 'bg-secondary-100 text-secondary-700',
        accent: 'bg-accent-100 text-accent-700',
        info: 'bg-info-100 text-info-700',
        success: 'bg-success-100 text-success-700',
        warning: 'bg-warning-100 text-warning-700',
        destructive: 'bg-destructive-100 text-destructive-700',
        muted: 'bg-muted text-muted-foreground',
      },
    },
    defaultVariants: {
      size: 'md',
      colorScheme: 'primary',
    },
  },
);
