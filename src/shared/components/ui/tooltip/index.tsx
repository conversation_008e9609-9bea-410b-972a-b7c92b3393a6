'use client';

import type { VariantProps } from 'class-variance-authority';
import React, { useState } from 'react';
import { cn } from '@/shared/utils/utils';
import { tooltipArrowVariants, tooltipVariants } from './tooltip-variants';

type TooltipProps = {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'right' | 'bottom' | 'left';
  delay?: number;
  variant?: VariantProps<typeof tooltipVariants>['variant'];
  className?: string;
};

/**
 * Tooltip Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a tooltip with customizable position and styling.
 */
export function Tooltip({
  content,
  children,
  position = 'top',
  delay = 300,
  variant = 'default',
  className,
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    const id = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setTimeoutId(id);
  };

  const handleMouseLeave = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 mb-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 translate-x-2 ml-2';
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 translate-y-2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 -translate-x-2 mr-2';
      default:
        return 'bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 mb-2';
    }
  };

  // Get arrow position classes
  const getArrowPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'top-full left-1/2 -translate-x-1/2 -translate-y-1/2';
      case 'right':
        return 'right-full top-1/2 -translate-y-1/2 translate-x-1/2';
      case 'bottom':
        return 'bottom-full left-1/2 -translate-x-1/2 translate-y-1/2';
      case 'left':
        return 'left-full top-1/2 -translate-y-1/2 -translate-x-1/2';
      default:
        return 'top-full left-1/2 -translate-x-1/2 -translate-y-1/2';
    }
  };

  return (
    <div className="relative inline-block" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      {children}
      {isVisible && (
        <div
          className={cn(
            tooltipVariants({ variant }),
            getPositionClasses(),
            className,
          )}
        >
          {content}
          <div
            className={cn(
              tooltipArrowVariants({ variant }),
              getArrowPositionClasses(),
            )}
          />
        </div>
      )}
    </div>
  );
}
