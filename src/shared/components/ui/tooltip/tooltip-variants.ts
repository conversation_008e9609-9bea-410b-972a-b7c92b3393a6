import { cva } from 'class-variance-authority';

/**
 * Tooltip Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The tooltip variants are defined using the cva utility for consistent styling.
 */
export const tooltipVariants = cva(
  'absolute z-50 px-4 py-4 text-xs font-medium rounded-lg shadow-lg whitespace-nowrap border border-border',
  {
    variants: {
      variant: {
        default: 'bg-popover text-popover-foreground',
        primary: 'bg-primary text-primary-foreground',
        secondary: 'bg-secondary text-secondary-foreground',
        dark: 'bg-foreground text-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Tooltip Arrow Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The tooltip arrow variants are defined using the cva utility for consistent styling.
 */
export const tooltipArrowVariants = cva(
  'absolute w-2 h-2 transform rotate-45',
  {
    variants: {
      variant: {
        default: 'bg-popover',
        primary: 'bg-primary',
        secondary: 'bg-secondary',
        dark: 'bg-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
