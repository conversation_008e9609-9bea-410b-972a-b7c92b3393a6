import { cva } from 'class-variance-authority';

/**
 * LocaleSwitcher Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The switcher variants are defined using the cva utility for consistent styling.
 */
export const localeSwitcherVariants = cva(
  'border font-medium focus:outline-hidden focus-visible:ring-3',
  {
    variants: {
      variant: {
        default: 'border-border bg-background text-foreground focus-visible:ring-primary/50',
        primary: 'border-primary-300 bg-background text-foreground focus-visible:ring-primary/50',
        secondary: 'border-secondary-300 bg-background text-foreground focus-visible:ring-secondary/50',
        gray: 'border-gray-300 bg-background text-foreground focus-visible:ring-gray-500/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
