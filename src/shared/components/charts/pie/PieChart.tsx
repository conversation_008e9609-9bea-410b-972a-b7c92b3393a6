'use client';

import { GLOBAL_COLOR } from '@/shared/constants/color';
import type { ApexOptions } from 'apexcharts';
import dynamic from 'next/dynamic';

type PropsType = {
  label: string;
  data: { name: string; amount: number }[];
};

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

export function PieChart({ data, label }: PropsType) {
  const chartOptions: ApexOptions = {
    chart: {
      type: 'pie',
      fontFamily: 'inherit',
    },
    colors: GLOBAL_COLOR,
    labels: data.map(item => item.name),
    legend: {
      show: true,
      position: 'right',
      floating: false,
      width: 200,

      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
      formatter: (legendName, opts) => {
        const { series } = opts.w.globals;
        const value = series[opts.seriesIndex] ?? 0;
        return `${legendName}: ${value}%`;
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: '0%',
          background: 'transparent',
          labels: {
            show: false,
            total: {
              show: true,
              showAlways: true,
              label,
              fontSize: '16px',
              fontWeight: '400',
            },
            value: {
              show: false,
              // fontSize: '28px',
              // fontWeight: 'bold',
              // formatter: val => compactFormat(+val),
            },
          },
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    responsive: [
      {
        breakpoint: 2600,
        options: {
          chart: {
            width: 415,
          },
        },
      },
      {
        breakpoint: 640,
        options: {
          chart: {
            width: '100%',
          },
        },
      },
      {
        breakpoint: 370,
        options: {
          chart: {
            width: 260,
          },
        },
      },
    ],
  };

  return (
    <Chart
      options={chartOptions}
      series={data.map(item => item.amount)}
      type="pie"
    />
  );
}
