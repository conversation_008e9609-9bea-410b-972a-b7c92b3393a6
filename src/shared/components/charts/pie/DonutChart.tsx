'use client';

import type { ApexOptions } from 'apexcharts';
import dynamic from 'next/dynamic';

type PropsType = {
  label: string;
  data: { name: string; amount: number; label?: string }[];
};

const Chart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

export function DonutChart({ data, label }: PropsType) {
  const chartOptions: ApexOptions = {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
    },
    colors: ['#0062FF', '#FB923C', '#22D3EE', '#805CDB'],
    labels: data.map(item => item.label || item.name),
    legend: {
      show: true,
      position: 'bottom',
      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
      // formatter: (legendName, opts) => {
      //   const { seriesPercent } = opts.w.globals;
      //   return `${legendName}: ${seriesPercent[opts.seriesIndex]}%`;
      // },
    },
    plotOptions: {
      pie: {
        donut: {
          size: '80%',
          background: 'transparent',
          labels: {
            show: true,
            total: {
              show: true,
              showAlways: true,
              label,
              fontSize: '16px',
              fontWeight: '400',
            },
            value: {
              show: false,
              // fontSize: '28px',
              // fontWeight: 'bold',
              // formatter: val => compactFormat(+val),
            },
          },
        },
      },
    },
    dataLabels: {
      enabled: false,
    },
    tooltip: {
      style: {
        fontSize: '12px',
      },
      custom({ series, seriesIndex }) {
        const value = series[seriesIndex];
        return `<div style="padding: 8px; color: #ffffff; background: rgba(0, 0, 0, 0.8); border-radius: 4px;">
          <span style="color: #ffffff;">Value: ${value}</span>
        </div>`;
      },
    },
    responsive: [
      {
        breakpoint: 2600,
        options: {
          chart: {
            width: 415,
          },
        },
      },
      {
        breakpoint: 640,
        options: {
          chart: {
            width: '100%',
          },
        },
      },
      {
        breakpoint: 370,
        options: {
          chart: {
            width: 260,
          },
        },
      },
    ],
  };

  return (
    <Chart
      options={chartOptions}
      series={data.map(item => item.amount)}
      type="donut"
    />
  );
}
