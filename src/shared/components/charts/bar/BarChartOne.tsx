'use client';
import type { ApexOptions } from 'apexcharts';

import dynamic from 'next/dynamic';

import React, { useMemo } from 'react';
// Dynamically import the ReactApexChart component
const ReactApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

type Series = {
  name: string;
  data: number[] | any[];
};

type BarChartOneType = {
  colors: string[];
  categories: string[];
  series: Series[];
  height: number;
  labelMap: Map<string, string>;
};

export default function BarChartOne({
  colors,
  categories,
  series,
  height = 240,
  labelMap,
}: BarChartOneType) {
  const options: ApexOptions = useMemo(() => ({
    colors,
    chart: {
      // fontFamily: 'Lexend Deca, sans-serif',
      type: 'bar',
      height,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '50%',
        dataLabels: { position: 'top' },
        distributed: true,
      },
    },
    dataLabels: {
      enabled: false,
      formatter(val: number) {
        return val > 0 ? `${val}%` : '';
      },
    },
    stroke: {
      show: true,
      width: 2,
      colors: ['transparent'],
    },
    xaxis: {
      categories,
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      labels: {
        show: true,
        style: {
          // cssClass: 'text-label-class',
          fontFamily: 'Lexend Deca',
        },
        formatter(value) {
          return value;
        },
      },

    },
    legend: {
      show: false,
      position: 'bottom',
      horizontalAlign: 'center',
      itemMargin: { horizontal: 10 },
    },
    yaxis: {
      title: {
        text: 'Votes',
      },
      fontFamily: 'Lexend Deca',
      max: 100,
      labels: {
        formatter(val) {
          return `${val}%`;
        },
      },
    },
    grid: {
      yaxis: {
        lines: {
          show: true,
        },
      },
    },
    fill: {
      opacity: 1,
    },

    tooltip: {
      x: {
        show: false,
      },
      y: {
        show: false,
        formatter(val, option) {
          const { dataPointIndex } = option;
          const colorSelected = colors[dataPointIndex];
          const label = labelMap.get(colorSelected ?? '');
          return `${label ?? 'Other'} : ${val > 0 ? `${val}%` : ''}`;
        },
      },
    },
  }), [colors, categories, height, labelMap]);

  return (
    <div className="max-w-full overflow-x-auto custom-scrollbar">
      <div id="chartOne" className="min-w-[1000px]">
        <ReactApexChart
          options={options}
          series={series}
          categories={categories}
          type="bar"
          height={height}
        />
      </div>
    </div>
  );
}
