import { cva } from 'class-variance-authority';

/**
 * HorizontalBarChart Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The chart variants are defined using the cva utility for consistent styling.
 */
export const horizontalBarChartVariants = cva(
  'horizontal-bar-chart',
  {
    variants: {
      variant: {
        default: '',
        primary: '',
        secondary: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * HorizontalBarChart Loading Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The loading state variants are defined using the cva utility for consistent styling.
 */
export const horizontalBarChartLoadingVariants = cva(
  'flex items-center justify-center h-full w-full',
  {
    variants: {
      variant: {
        default: 'rounded h-32 w-full',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
