'use client';
import type { ApexOptions } from 'apexcharts';
import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import the ReactApexChart component
const ReactApexChart = dynamic(() => import('react-apexcharts'), {
  ssr: false,
});

// Define default colors
const defaultColors = ['#1E88E5', '#4CAF50', '#FF9800', '#9C27B0'];

export type VerticalBarChartProps = {
  title?: string;
  series: {
    name: string;
    data: number[];
  }[];
  categories: string[];
  colors?: string[];
  height?: number;
  maxValue?: number;
  showDataLabels?: boolean;
  className?: string;
  stacked?: boolean;
  barWidth?: string;
};

export default function VerticalBarChart({
  title,
  series,
  categories,
  colors = defaultColors,
  height = 300,
  maxValue,
  showDataLabels = false,
  className = '',
  stacked = false,
  barWidth = '50%',
}: VerticalBarChartProps) {
  const options: ApexOptions = {
    chart: {
      fontFamily: 'Lexend Deca, sans-serif',
      type: 'bar',
      height,
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        speed: 800,
      },
      stacked,
    },
    colors,
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: barWidth,
        borderRadius: 4,
        borderRadiusApplication: 'end',
        dataLabels: {
          position: 'top',
        },
      },
    },
    dataLabels: {
      enabled: showDataLabels,
      style: {
        colors: ['#333'],
        fontWeight: 'bold',
        fontSize: '12px',
      },
      formatter(val) {
        return val.toString();
      },
      offsetY: -20,
    },
    xaxis: {
      categories,
      labels: {
        style: {
          colors: '#9ca3af',
          fontSize: '12px',
        },
        rotate: -45,
        rotateAlways: false,
        hideOverlappingLabels: true,
        trim: true,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      max: maxValue,
      labels: {
        style: {
          colors: '#4b5563',
          fontSize: '12px',
        },
      },
      title: {
        text: '',
        style: {
          fontSize: '12px',
        },
      },
    },
    grid: {
      yaxis: {
        lines: {
          show: true,
        },
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    legend: {
      show: series.length > 1,
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '13px',
      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
    },
    tooltip: {
      shared: true,
      intersect: false,
      y: {
        formatter(val) {
          return val.toString();
        },
      },
    },
    responsive: [
      {
        breakpoint: 768,
        options: {
          plotOptions: {
            bar: {
              columnWidth: '70%',
            },
          },
          legend: {
            position: 'bottom',
            offsetY: 0,
            offsetX: 0,
          },
        },
      },
    ],
  };

  if (title) {
    options.title = {
      text: title,
      align: 'left',
      style: {
        fontSize: '16px',
        fontWeight: 'bold',
        color: '#333',
      },
    };
  }

  return (
    <div className={`vertical-bar-chart ${className}`}>
      <ReactApexChart
        options={options}
        series={series}
        type="bar"
        height={height}
      />
    </div>
  );
}
