import { cva } from 'class-variance-authority';

/**
 * SignInForm Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const signInFormContainerVariants = cva(
  'flex flex-col flex-1',
  {
    variants: {
      variant: {
        default: 'lg:w-1/2 w-full',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * SignInForm Back Link Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The back link variants are defined using the cva utility for consistent styling.
 */
export const signInFormBackLinkVariants = cva(
  'inline-flex items-center text-sm transition-colors',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground hover:text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * SignInForm Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The title variants are defined using the cva utility for consistent styling.
 */
export const signInFormTitleVariants = cva(
  'mb-2 font-medium text-title-sm sm:text-title-md',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * SignInForm Description Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The description variants are defined using the cva utility for consistent styling.
 */
export const signInFormDescriptionVariants = cva(
  'text-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * SignInForm Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The icon variants are defined using the cva utility for consistent styling.
 */
export const signInFormIconVariants = cva(
  '',
  {
    variants: {
      variant: {
        default: 'fill-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
