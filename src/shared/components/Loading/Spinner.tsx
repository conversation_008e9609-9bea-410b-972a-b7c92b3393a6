import { cn } from '@/shared/utils/utils';
import React from 'react';

type SpinnerProps = {
  size?: number;
} & React.HTMLAttributes<HTMLDivElement>;
export const Spinner: React.FC<SpinnerProps> = ({ size = 10, className, ...props }) => {
  return (
    <div
      className={cn(
        'animate-spin rounded-full border-4 border-solid border-primary border-t-transparent',
        `size-${size}`,
        className,
      )}
      {...props}
    >
    </div>
  );
};
