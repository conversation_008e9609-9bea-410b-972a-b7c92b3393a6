import React from 'react';
import { Spinner } from './Spinner';
import { cn } from '@/shared/utils/utils';

type LoadingPageProps = {} & React.HTMLAttributes<HTMLDivElement>;
export const LoadingPage: React.FC<LoadingPageProps> = ({ className, ...props }) => {
  return (
    <div
      className={cn(
        'mobile-wrapper fixed inset-0 z-[100000] flex h-screen w-full select-none items-center justify-center bg-white/40',
        className,
      )}
      {...props}
    >
      <div className="flex items-center justify-center space-x-2">
        <Spinner className="size-10"></Spinner>
      </div>
    </div>
  );
};
