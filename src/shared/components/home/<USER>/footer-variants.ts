import { cva } from 'class-variance-authority';

/**
 * Footer Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The footer variants are defined using the cva utility for consistent styling.
 */
export const footerVariants = cva(
  'py-16 md:py-32',
  {
    variants: {
      variant: {
        default: 'bg-muted text-muted-foreground',
        primary: 'bg-primary/5 text-primary-foreground',
        secondary: 'bg-secondary/10 text-secondary-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const footerLinkVariants = cva(
  'block duration-150',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground hover:text-primary',
        primary: 'text-primary-foreground/80 hover:text-primary-foreground',
        secondary: 'text-secondary-foreground/80 hover:text-secondary-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export const footerCopyrightVariants = cva(
  'block text-center text-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary-foreground/80',
        secondary: 'text-secondary-foreground/80',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
