import { cva } from 'class-variance-authority';

/**
 * Features Description Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The features description variants are defined using the cva utility for consistent styling.
 */
export const featuresDescriptionVariants = cva(
  'md:max-w-2xl',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Feature Item Description Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The feature item description variants are defined using the cva utility for consistent styling.
 */
export const featureItemDescriptionVariants = cva(
  '',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
