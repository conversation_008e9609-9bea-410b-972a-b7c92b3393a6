'use client';

import type { VariantProps } from 'class-variance-authority';
import type { ChangeEventHandler } from 'react';
import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { routing, usePathname } from '@/core/config/i18nNavigation';
import { cn } from '@/shared/utils/utils';
import { localeSwitcherVariants } from './locale-switcher/locale-switcher-variants';

type LocaleSwitcherProps = {
  variant?: VariantProps<typeof localeSwitcherVariants>['variant'];
  className?: string;
};

/**
 * LocaleSwitcher Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a dropdown to switch between locales with customizable styling.
 */
export const LocaleSwitcher = ({
  variant = 'gray',
  className,
}: LocaleSwitcherProps = {}) => {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const handleChange: ChangeEventHandler<HTMLSelectElement> = (event) => {
    router.push(`/${event.target.value}${pathname}`);
    router.refresh();
  };

  return (
    <select
      defaultValue={locale}
      onChange={handleChange}
      className={cn(localeSwitcherVariants({ variant }), className)}
      aria-label="lang-switcher"
    >
      {routing.locales.map(elt => (
        <option key={elt} value={elt}>
          {elt.toUpperCase()}
        </option>
      ))}
    </select>
  );
};
