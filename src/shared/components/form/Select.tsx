import React, { useEffect, useState } from 'react';
import { ChevronDownIcon } from '@/shared/icons';
import { cn } from '@/shared/utils/utils';
import { inputFieldHintVariants } from './input/input-field-variants';
import type { inputFieldVariants } from './input/input-field-variants';
import type { VariantProps } from 'class-variance-authority';

type Option = {
  value: string | number;
  label: string;
  disable?: boolean;
};

type SelectProps = {
  options: Option[];
  placeholder?: string;
  onChange: (value: string) => void;
  className?: string;
  defaultValue?: string | number;
  isHiddenPlaceHolder?: boolean;
  disabled?: boolean;
  bgDisabled?: string;
  success?: boolean;
  error?: boolean;
  hint?: string; // Optional hint text,
  variant?: VariantProps<typeof inputFieldVariants>['variant'];

};

const Select: React.FC<SelectProps> = ({
  options,
  placeholder = 'Select an option',
  onChange,
  className = '',
  defaultValue = '',
  isHiddenPlaceHolder = false,
  disabled = false,
  bgDisabled = '',
  success = false,
  error = false,
  hint,
  variant = 'default',
}) => {
  // Manage the selected value
  const [selectedValue, setSelectedValue] = useState<string | number>(defaultValue);

  // Update selectedValue when defaultValue changes
  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setSelectedValue(defaultValue);
  }, [defaultValue]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedValue(value);
    onChange(value); // Trigger parent handler
  };

  const inputVariant = error ? 'error' : success ? 'success' : variant;

  return (
    <div className="relative">
      <select
        className={`h-11 w-full appearance-none rounded-lg border border-input px-4 py-2.5 pr-11 text-sm shadow-theme-xs bg-background placeholder:text-muted-foreground focus:outline-hidden focus:ring-3 focus:ring-primary/10 ${
          selectedValue?.toString()
            ? 'text-foreground'
            : 'text-muted-foreground'
        } 
        ${disabled ? (bgDisabled || ' opacity-50 cursor-not-allowed') : ''}
        ${className}`}
        value={selectedValue}
        onChange={handleChange}
        disabled={disabled}

      >
        {/* Placeholder option */}
        {!isHiddenPlaceHolder && (
          <option
            value=""
            disabled
            className="text-muted-foreground bg-background"
          >
            {placeholder}
          </option>
        )}
        {/* Map over options */}
        {options.map(option => (
          <option
            key={option.value}
            value={option.value}
            disabled={option?.disable}
            className="text-foreground bg-background"
          >
            {option.label}
          </option>
        ))}
      </select>
      <span className="absolute text-muted-foreground -translate-y-1/2 pointer-events-none right-3 top-1/2 z-10">
        <ChevronDownIcon className="size-4" />
      </span>

      {/* Optional Hint Text */}
      {hint && (
        <p
          className={cn(
            inputFieldHintVariants({
              variant: inputVariant,
            }),
          )}
        >
          {hint}
        </p>
      )}
    </div>
  );
};

export default Select;
