'use client';
import React from 'react';
import ComponentCard from '../../common/ComponentCard';
import Switch from '../switch/Switch';

export default function ToggleSwitch() {
  const handleSwitchChange = (checked: boolean) => {
    console.log('Switch is now:', checked ? 'ON' : 'OFF');
  };
  return (
    <ComponentCard title="Toggle switch input">
      <div className="flex gap-4">
        <Switch
          label="Default"
          defaultChecked={true}
          onChange={handleSwitchChange}
        />
        <Switch
          label="Checked"
          defaultChecked={true}
          onChange={handleSwitchChange}
        />
        <Switch label="Disabled" disabled={true} />
      </div>
      {' '}
      <div className="flex gap-4">
        <Switch
          label="Default"
          defaultChecked={true}
          onChange={handleSwitchChange}
          color="secondary"
        />
        <Switch
          label="Checked"
          defaultChecked={true}
          onChange={handleSwitchChange}
          color="secondary"
        />
        <Switch label="Disabled" disabled={true} color="secondary" />
      </div>
    </ComponentCard>
  );
}
