import { cva } from 'class-variance-authority';

/**
 * InputField Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The input field variants are defined using the cva utility for consistent styling.
 */
export const inputFieldVariants = cva(
  'h-11 w-full rounded-lg border appearance-none px-4 py-2.5 text-sm placeholder:text-muted-foreground focus:outline-hidden focus:ring-3',
  {
    variants: {
      variant: {
        default: 'bg-transparent text-foreground border-input focus:border-primary-300 focus:ring-primary/10',
        error: 'bg-transparent text-destructive-800 border-destructive focus:ring-destructive/10',
        success: 'bg-transparent text-success border-success-400 focus:ring-success/10 focus:border-success-300',
      },
      disabled: {
        true: 'bg-muted opacity-50 text-muted-foreground border-input cursor-not-allowed',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      disabled: false,
    },
  },
);

/**
 * InputField Hint Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The input field hint variants are defined using the cva utility for consistent styling.
 */
export const inputFieldHintVariants = cva(
  'mt-1.5 text-xs',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        error: 'text-destructive',
        success: 'text-success',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
