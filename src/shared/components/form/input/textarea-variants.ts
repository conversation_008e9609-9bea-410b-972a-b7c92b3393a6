import { cva } from 'class-variance-authority';

/**
 * TextArea Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The textarea variants are defined using the cva utility for consistent styling.
 */
export const textareaVariants = cva(
  'w-full rounded-lg border px-4 py-2.5 text-sm shadow-theme-xs focus:outline-hidden',
  {
    variants: {
      variant: {
        default: 'bg-transparent text-foreground border-input focus:border-primary-300 focus:ring-3 focus:ring-primary/10',
        error: 'bg-transparent text-foreground border-input focus:border-destructive-300 focus:ring-3 focus:ring-destructive/10',
      },
      disabled: {
        true: 'bg-muted opacity-50 text-muted-foreground border-input cursor-not-allowed',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      disabled: false,
    },
  },
);

/**
 * TextArea Hint Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The textarea hint variants are defined using the cva utility for consistent styling.
 */
export const textareaHintVariants = cva(
  'mt-2 text-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        error: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
