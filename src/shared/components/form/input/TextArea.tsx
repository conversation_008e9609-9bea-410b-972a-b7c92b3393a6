'use client';

import React from 'react';
import { cn } from '@/shared/utils/utils';
import { textareaHintVariants, textareaVariants } from './textarea-variants';

type TextareaProps = {
  placeholder?: string; // Placeholder text
  rows?: number; // Number of rows
  value?: string; // Current value
  onChange?: (value: string) => void; // Change handler
  className?: string; // Additional CSS classes
  disabled?: boolean; // Disabled state
  error?: boolean; // Error state
  hint?: string; // Hint text to display
};

/**
 * TextArea Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a textarea input with customizable states and hint text.
 */
const TextArea: React.FC<TextareaProps> = ({
  placeholder = 'Enter your message', // Default placeholder
  rows = 3, // Default number of rows
  value = '', // Default value
  onChange, // Callback for changes
  className = '', // Additional custom styles
  disabled = false, // Disabled state
  error = false, // Error state
  hint = '', // Default hint text
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <div className="relative">
      <textarea
        placeholder={placeholder}
        rows={rows}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        className={cn(
          textareaVariants({
            variant: error ? 'error' : 'default',
            disabled,
          }),
          className,
        )}
      />
      {hint && (
        <p
          className={cn(
            textareaHintVariants({
              variant: error ? 'error' : 'default',
            }),
          )}
        >
          {hint}
        </p>
      )}
    </div>
  );
};

export default TextArea;
