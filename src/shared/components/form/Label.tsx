import type { VariantProps } from 'class-variance-authority';
import type { FC, ReactNode } from 'react';
import React from 'react';
import { cn } from '@/shared/utils/utils';
import { labelVariants } from './label-variants';

type LabelProps = {
  htmlFor?: string;
  children: ReactNode;
  className?: string;
  variant?: VariantProps<typeof labelVariants>['variant'];
};

/**
 * Label Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a consistent label for form elements.
 */
const Label: FC<LabelProps> = ({
  htmlFor,
  children,
  className,
  variant = 'default',
}) => {
  return (
    <label
      htmlFor={htmlFor}
      className={cn(labelVariants({ variant }), className)}
    >
      {children}
    </label>
  );
};

export default Label;
