import { cva } from 'class-variance-authority';

/**
 * Switch Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The switch container variants are defined using the cva utility for consistent styling.
 */
export const switchContainerVariants = cva(
  'flex cursor-pointer select-none items-center gap-3 text-sm font-medium',
  {
    variants: {
      disabled: {
        true: 'text-muted-foreground',
        false: 'text-foreground',
      },
    },
    defaultVariants: {
      disabled: false,
    },
  },
);

/**
 * Switch Track Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The switch track variants are defined using the cva utility for consistent styling.
 */
export const switchTrackVariants = cva(
  'block transition duration-150 ease-linear h-6 w-11 rounded-full',
  {
    variants: {
      color: {
        primary: '',
        secondary: '',
      },
      checked: {
        true: '',
        false: '',
      },
      disabled: {
        true: 'bg-muted pointer-events-none',
        false: '',
      },
    },
    compoundVariants: [
      {
        color: 'primary',
        checked: true,
        disabled: false,
        className: 'bg-primary',
      },
      {
        color: 'primary',
        checked: false,
        disabled: false,
        className: 'bg-muted',
      },
      {
        color: 'secondary',
        checked: true,
        disabled: false,
        className: 'bg-secondary',
      },
      {
        color: 'secondary',
        checked: false,
        disabled: false,
        className: 'bg-muted',
      },
    ],
    defaultVariants: {
      color: 'primary',
      checked: false,
      disabled: false,
    },
  },
);

/**
 * Switch Knob Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The switch knob variants are defined using the cva utility for consistent styling.
 */
export const switchKnobVariants = cva(
  'absolute left-0.5 top-0.5 h-5 w-5 rounded-full shadow-theme-sm duration-150 ease-linear transform bg-background',
  {
    variants: {
      checked: {
        true: 'translate-x-full',
        false: 'translate-x-0',
      },
    },
    defaultVariants: {
      checked: false,
    },
  },
);
