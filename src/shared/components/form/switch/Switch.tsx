'use client';

import React, { useState } from 'react';
import { cn } from '@/shared/utils/utils';
import { switchContainerVariants, switchKnobVariants, switchTrackVariants } from './switch-variants';

type SwitchProps = {
  label: string;
  defaultChecked?: boolean;
  disabled?: boolean;
  onChange?: (checked: boolean) => void;
  color?: 'primary' | 'secondary'; // Updated to use semantic color names
  className?: string;
};

/**
 * Switch Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a toggle switch with customizable colors and states.
 */
const Switch: React.FC<SwitchProps> = ({
  label,
  defaultChecked = false,
  disabled = false,
  onChange,
  color = 'primary', // Default to primary color
  className,
}) => {
  const [isChecked, setIsChecked] = useState(defaultChecked);

  const handleToggle = () => {
    if (disabled) {
      return;
    }
    const newCheckedState = !isChecked;
    setIsChecked(newCheckedState);
    if (onChange) {
      onChange(newCheckedState);
    }
  };

  return (
    <div
      className={cn(
        switchContainerVariants({ disabled }),
        className,
      )}
      role="button"
      tabIndex={0}
      onClick={handleToggle}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleToggle();
        }
      }}
    >
      <div className="relative">
        <div
          className={cn(
            switchTrackVariants({
              color,
              checked: isChecked,
              disabled,
            }),
          )}
        >
        </div>
        <div
          className={cn(
            switchKnobVariants({
              checked: isChecked,
            }),
          )}
        >
        </div>
      </div>
      {label}
    </div>
  );
};

export default Switch;
