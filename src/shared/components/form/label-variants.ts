import { cva } from 'class-variance-authority';

/**
 * Label Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The label variants are defined using the cva utility for consistent styling.
 */
export const labelVariants = cva(
  'mb-1.5 block text-sm font-medium',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary',
        secondary: 'text-secondary',
        error: 'text-destructive',
        success: 'text-success',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
