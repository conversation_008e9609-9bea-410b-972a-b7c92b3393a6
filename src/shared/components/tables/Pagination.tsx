'use client';

import type { VariantProps } from 'class-variance-authority';
import React from 'react';
import { cn } from '@/shared/utils/utils';
import {
  paginationButtonVariants,
  paginationEllipsisVariants,
  paginationPageNumberVariants,
} from './pagination-variants';
import { useTranslations } from 'next-intl';

type PaginationProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  variant?: VariantProps<typeof paginationButtonVariants>['variant'];
};

/**
 * Pagination Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides navigation controls for paginated content.
 */
const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  variant = 'default',
}) => {
  const t = useTranslations('paginator');

  const pagesAroundCurrent = Array.from(
    { length: Math.min(3, totalPages) },
    (_, i) => i + Math.max(currentPage - 1, 1),
  );

  return (
    <div className="flex items-center">
      <button
        type="button"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={cn(
          paginationButtonVariants({ variant }),
          'mr-2.5',
        )}
      >
        {t('previous')}
      </button>
      <div className="flex items-center gap-2">
        {currentPage > 3 && (
          <span className={paginationEllipsisVariants()}>...</span>
        )}
        {pagesAroundCurrent.map(page => (
          <button
            type="button"
            key={page}
            onClick={() => onPageChange(page)}
            className={cn(
              paginationPageNumberVariants({
                variant: currentPage === page ? 'active' : 'default',
              }),
            )}
          >
            {page}
          </button>
        ))}
        {currentPage < totalPages - 2 && (
          <span className={paginationEllipsisVariants()}>...</span>
        )}
      </div>
      <button
        type="button"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={cn(
          paginationButtonVariants({ variant }),
          'ml-2.5',
        )}
      >
        {t('next')}

      </button>
    </div>
  );
};

export default Pagination;
