import { cva } from 'class-variance-authority';

/**
 * Pagination Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The pagination button variants are defined using the cva utility for consistent styling.
 */
export const paginationButtonVariants = cva(
  'flex items-center justify-center rounded-lg border text-sm h-10',
  {
    variants: {
      variant: {
        default: 'border-border bg-background text-foreground hover:bg-muted shadow-sm disabled:opacity-50',
        active: 'bg-primary text-primary-foreground border-primary',
      },
      size: {
        default: 'px-3.5 py-2.5',
        icon: 'w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

/**
 * Pagination Page Number Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The pagination page number variants are defined using the cva utility for consistent styling.
 */
export const paginationPageNumberVariants = cva(
  'flex items-center justify-center h-10 w-10 rounded-lg text-sm font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground hover:bg-primary/10 hover:text-primary',
        active: 'bg-primary text-primary-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Pagination Ellipsis Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The pagination ellipsis variants are defined using the cva utility for consistent styling.
 */
export const paginationEllipsisVariants = cva(
  'px-2 text-muted-foreground',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
