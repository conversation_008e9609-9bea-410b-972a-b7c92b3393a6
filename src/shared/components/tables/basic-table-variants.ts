import { cva } from 'class-variance-authority';

/**
 * Basic Table Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table container variants are defined using the cva utility for consistent styling.
 */
export const basicTableContainerVariants = cva(
  'overflow-hidden rounded-xl border',
  {
    variants: {
      variant: {
        default: 'border-border bg-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table Header Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table header variants are defined using the cva utility for consistent styling.
 */
export const basicTableHeaderVariants = cva(
  'border-b',
  {
    variants: {
      variant: {
        default: 'border-border/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table Header Cell Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table header cell variants are defined using the cva utility for consistent styling.
 */
export const basicTableHeaderCellVariants = cva(
  'px-5 py-3 font-medium text-start text-theme-xs',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table Body Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table body variants are defined using the cva utility for consistent styling.
 */
export const basicTableBodyVariants = cva(
  'divide-y',
  {
    variants: {
      variant: {
        default: 'divide-border/50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table Cell Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table cell variants are defined using the cva utility for consistent styling.
 */
export const basicTableCellVariants = cva(
  'px-4 py-3 text-start text-theme-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table User Name Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table user name variants are defined using the cva utility for consistent styling.
 */
export const basicTableUserNameVariants = cva(
  'block font-medium text-theme-sm',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table User Role Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table user role variants are defined using the cva utility for consistent styling.
 */
export const basicTableUserRoleVariants = cva(
  'block text-theme-xs',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Basic Table Team Image Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The table team image container variants are defined using the cva utility for consistent styling.
 */
export const basicTableTeamImageContainerVariants = cva(
  'w-6 h-6 overflow-hidden border-2 rounded-full',
  {
    variants: {
      variant: {
        default: 'border-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
