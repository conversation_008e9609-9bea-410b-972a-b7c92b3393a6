'use client';

import type { VariantProps } from 'class-variance-authority';
import Image from 'next/image';
import React from 'react';

import { Badge } from '../ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from '../ui/table';
import {
  basicTableBodyVariants,
  basicTableCellVariants,
  basicTableContainerVariants,
  basicTableHeaderCellVariants,
  basicTableHeaderVariants,
  basicTableTeamImageContainerVariants,
  basicTableUserNameVariants,
  basicTableUserRoleVariants,
} from './basic-table-variants';

type Order = {
  id: number;
  user: {
    image: string;
    name: string;
    role: string;
  };
  projectName: string;
  team: {
    images: string[];
  };
  status: string;
  budget: string;
};

// Define the table data using the interface
const tableData: Order[] = [
  {
    id: 1,
    user: {
      image: '/images/user/user-17.jpg',
      name: '<PERSON>',
      role: 'Web Designer',
    },
    projectName: 'Agency Website',
    team: {
      images: [
        '/images/user/user-22.jpg',
        '/images/user/user-23.jpg',
        '/images/user/user-24.jpg',
      ],
    },
    budget: '3.9K',
    status: 'Active',
  },
  {
    id: 2,
    user: {
      image: '/images/user/user-18.jpg',
      name: 'Kaiya George',
      role: 'Project Manager',
    },
    projectName: 'Technology',
    team: {
      images: ['/images/user/user-25.jpg', '/images/user/user-26.jpg'],
    },
    budget: '24.9K',
    status: 'Pending',
  },
  {
    id: 3,
    user: {
      image: '/images/user/user-17.jpg',
      name: 'Zain Geidt',
      role: 'Content Writing',
    },
    projectName: 'Blog Writing',
    team: {
      images: ['/images/user/user-27.jpg'],
    },
    budget: '12.7K',
    status: 'Active',
  },
  {
    id: 4,
    user: {
      image: '/images/user/user-20.jpg',
      name: 'Abram Schleifer',
      role: 'Digital Marketer',
    },
    projectName: 'Social Media',
    team: {
      images: [
        '/images/user/user-28.jpg',
        '/images/user/user-29.jpg',
        '/images/user/user-30.jpg',
      ],
    },
    budget: '2.8K',
    status: 'Cancel',
  },
  {
    id: 5,
    user: {
      image: '/images/user/user-21.jpg',
      name: 'Carla George',
      role: 'Front-end Developer',
    },
    projectName: 'Website',
    team: {
      images: [
        '/images/user/user-31.jpg',
        '/images/user/user-32.jpg',
        '/images/user/user-33.jpg',
      ],
    },
    budget: '4.5K',
    status: 'Active',
  },
];

type BasicTableOneProps = {
  variant?: VariantProps<typeof basicTableContainerVariants>['variant'];
};

/**
 * BasicTableOne Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a basic table with user information, project details, and status.
 */
export default function BasicTableOne({ variant = 'default' }: BasicTableOneProps = {}) {
  return (
    <div className={basicTableContainerVariants({ variant })}>
      <div className="max-w-full overflow-x-auto">
        <div className="min-w-[1102px]">
          <Table>
            {/* Table Header */}
            <TableHeader className={basicTableHeaderVariants({ variant })}>
              <TableRow>
                <TableCell
                  className={basicTableHeaderCellVariants({ variant })}
                >
                  User
                </TableCell>
                <TableCell
                  className={basicTableHeaderCellVariants({ variant })}
                >
                  Project Name
                </TableCell>
                <TableCell
                  className={basicTableHeaderCellVariants({ variant })}
                >
                  Team
                </TableCell>
                <TableCell
                  className={basicTableHeaderCellVariants({ variant })}
                >
                  Status
                </TableCell>
                <TableCell
                  className={basicTableHeaderCellVariants({ variant })}
                >
                  Budget
                </TableCell>
              </TableRow>
            </TableHeader>

            {/* Table Body */}
            <TableBody className={basicTableBodyVariants({ variant })}>
              {tableData.map(order => (
                <TableRow key={order.id}>
                  <TableCell className="px-5 py-4 sm:px-6 text-start">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 overflow-hidden rounded-full">
                        <Image
                          width={40}
                          height={40}
                          src={order.user.image}
                          alt={order.user.name}
                        />
                      </div>
                      <div>
                        <span className={basicTableUserNameVariants({ variant })}>
                          {order.user.name}
                        </span>
                        <span className={basicTableUserRoleVariants({ variant })}>
                          {order.user.role}
                        </span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className={basicTableCellVariants({ variant })}>
                    {order.projectName}
                  </TableCell>
                  <TableCell className={basicTableCellVariants({ variant })}>
                    <div className="flex -space-x-2">
                      {order.team.images.map((teamImage, index) => (
                        <div
                          key={index}
                          className={basicTableTeamImageContainerVariants({ variant })}
                        >
                          <Image
                            width={24}
                            height={24}
                            src={teamImage}
                            alt={`Team member ${index + 1}`}
                            className="w-full"
                          />
                        </div>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className={basicTableCellVariants({ variant })}>
                    <Badge
                      color={
                        order.status === 'Active'
                          ? 'success'
                          : order.status === 'Pending'
                            ? 'warning'
                            : 'error'
                      }
                    >
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell className={basicTableCellVariants({ variant })}>
                    {order.budget}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
