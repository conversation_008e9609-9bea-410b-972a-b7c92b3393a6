'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { AlertTriangle } from 'lucide-react';
import { useCallback } from 'react';

type DeleteConfirmationModalProps = {
  /**
   * The ID of the entity to delete
   */
  entityId: string;

  /**
   * The name of the entity to display in the confirmation message
   */
  entityName: string;

  /**
   * The title of the confirmation modal
   */
  title: string;

  /**
   * The confirmation message to display
   * Can be a string with {name} as a placeholder for the entity name
   * or a React node (for rich text formatting)
   */
  confirmationMessage: string | React.ReactNode;

  /**
   * The text for the delete button
   */
  deleteButtonText?: string;

  /**
   * The text for the delete button when deleting
   */
  deletingButtonText?: string;

  /**
   * The text for the cancel button
   */
  cancelButtonText?: string;

  /**
   * The function to call when the user confirms deletion
   */
  onDelete: (id: string) => Promise<void>;

  /**
   * Whether the delete operation is in progress
   */
  isDeleting?: boolean;
};

/**
 * DeleteConfirmationModal Component
 *
 * A reusable confirmation modal for deleting entities.
 * This component can be used across different features to maintain consistent UI.
 */
export function DeleteConfirmationModal({
  entityId,
  title,
  confirmationMessage,
  deleteButtonText = 'Delete',
  deletingButtonText = 'Deleting...',
  cancelButtonText = 'Cancel',
  onDelete,
  isDeleting = false,
}: DeleteConfirmationModalProps) {
  const { isOpen, openModal, closeModal } = useModal();

  const handleDelete = useCallback(async () => {
    try {
      await onDelete(entityId);
      closeModal();
    } catch (error) {
      console.error(`Failed to delete entity (${entityId}):`, error);
    }
  }, [entityId, onDelete, closeModal]);

  // Process the confirmation message if it's a string
  const formattedMessage = confirmationMessage;

  return (
    <>
      <Button
        variant="destructive"
        size="sm"
        onClick={openModal}
      >
        {deleteButtonText}
      </Button>

      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-md">
        <div className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <h3 className="text-lg font-medium">{title}</h3>
          </div>

          <p className="mb-6 text-gray-600 dark:text-gray-300 whitespace-normal break-words">
            {formattedMessage}
          </p>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={closeModal}
              disabled={isDeleting}
            >
              {cancelButtonText}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? deletingButtonText : deleteButtonText}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}
