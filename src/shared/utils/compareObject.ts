export const compareObject = <T extends object>(obj1: T, obj2: T): boolean => {
  const keys = Object.keys(obj1) as Array<keyof T>;

  for (const key of keys) {
    const value1 = obj1[key];
    const value2 = obj2[key];

    if (value1 === null || value1 === undefined) {
      // Bỏ qua trường nếu giá trị của trường trong obj1 là null hoặc rỗng
      continue;
    }

    if (value2 === undefined) {
      // Nếu trường không tồn tại trong obj2, trả về false
      return false;
    }

    if ((value1 === null || value1 === '') !== (value2 === null || value2 === '')) {
      return false;
    }

    if (value1 !== value2) {
      if (typeof value1 === 'object' || typeof value2 === 'object') {
        return compareObject(value1 as any, value2 as any);
      } else if (typeof value1 === 'string' && typeof value2 === 'string') {
        return value1 === value2;
      } else {
        return false;
      }
    }
  }

  return true;
};

export const compareObjectArray = <T extends object>(initialForm: T[], formValue: T[]): boolean => {
  // Kiểm tra độ dài của hai mảng
  if (initialForm.length !== formValue.length) {
    return false;
  }
  // Hàm đệ quy để so sánh giá trị trong đối tượng/mảng
  const compareDynamicValues = (value1: any, value2: any): boolean => {
    if (value1 === value2) {
      return true; // Giá trị giống nhau
    }

    if (typeof value1 !== typeof value2) {
      return false; // Khác kiểu dữ liệu
    }

    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) {
        return false; // Khác số lượng phần tử
      }
      // So sánh từng phần tử trong mảng
      return value1.every((item, index) => compareDynamicValues(item, value2[index]));
    }

    if (typeof value1 === 'object' && typeof value2 === 'object') {
      const keys1 = Object.keys(value1);
      const keys2 = Object.keys(value2);

      if (keys1.length !== keys2.length) {
        return false; // Số lượng khóa khác nhau
      }

      // So sánh từng cặp khóa-giá trị
      return keys1.every(key => compareDynamicValues(value1[key], value2[key]));
    }

    // Giá trị nguyên thủy
    return value1 === value2;
  };

  // So sánh từng đối tượng trong mảng
  for (let i = 0; i < initialForm.length; i++) {
    const initialObj = initialForm[i];
    const formValueObj = formValue[i];

    if (!compareDynamicValues(initialObj, formValueObj)) {
      return false; // Phát hiện sự khác nhau
    }
  }

  return true; // Tất cả phần tử giống nhau
};
