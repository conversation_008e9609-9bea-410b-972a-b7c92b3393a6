import { toast } from 'sonner';

export const getFileTitleTimestamp = (): string => {
  const now = new Date();

  const pad = (n: number) => n.toString().padStart(2, '0');

  const year = now.getFullYear();
  const month = pad(now.getMonth() + 1); // 0-based
  const day = pad(now.getDate());
  const hour = pad(now.getHours());
  const minute = pad(now.getMinutes());
  const second = pad(now.getSeconds());

  return `${year}-${month}-${day}_${hour}-${minute}-${second}`;
};

export const downloadMDToFile = async (
  html: string,
  nameProject: string,
  nameStep: string,
  researchName?: string,
  type?: string,
) => {
  const date = getFileTitleTimestamp();

  // Build filename parts
  const parts = [nameProject, nameStep];

  if (researchName?.trim()) {
    parts.push(researchName.trim());
  }

  if (type?.trim()) {
    parts.push(type.trim());
  }

  parts.push(date);

  const data = { html };

  const fileName = `${parts.join('_')}.docx`;

  const baseUrl = window.location.origin;

  const response = await fetch(`${baseUrl}/api/download-docx`, {
    method: 'POST',
    body: JSON.stringify({ data }),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (response.status === 200) {
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const fileRef = document.createElement('a');
    fileRef.href = url;
    fileRef.download = `${fileName}`;
    fileRef.click();
  } else {
    toast.error('Oops! Something went wrong. Unable to download the file.');
  }
};
