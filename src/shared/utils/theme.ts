import type { ClassValue } from 'clsx';
import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function for merging class names with Tailwind classes
 * This is an enhanced version of the existing cn utility
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get the current theme
 * This function determines the current theme based on document classes
 * Returns 'light' as default during SSR when document is not available
 *
 * @returns The current theme ('light', 'dark', 'yellow', 'blue', or 'frappe')
 */
export function getCurrentTheme(): 'light' | 'dark' | 'yellow' | 'blue' | 'frappe' {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return 'light'; // Default theme for SSR
  }

  if (document.documentElement.classList.contains('dark')) {
    return 'dark';
  } else if (document.documentElement.classList.contains('theme-yellow')) {
    return 'yellow';
  } else if (document.documentElement.classList.contains('theme-blue')) {
    return 'blue';
  } else if (document.documentElement.classList.contains('theme-frappe')) {
    return 'frappe';
  }
  return 'light';
}

/**
 * Theme-aware class generation
 * This function helps generate classes based on the current theme
 *
 * @param baseClasses - Base classes that apply regardless of theme
 * @param themeClasses - Object with theme-specific classes
 * @returns Combined classes string
 */
export function themeClass(
  baseClasses: ClassValue,
  themeClasses?: Record<string, ClassValue>,
) {
  const currentTheme = getCurrentTheme();
  const themeSpecificClasses = themeClasses?.[currentTheme];

  return cn(baseClasses, themeSpecificClasses);
}

/**
 * Get a CSS variable value
 * This function retrieves the computed value of a CSS variable
 * Returns empty string during SSR when document is not available
 *
 * @param variableName - The CSS variable name (without the -- prefix)
 * @param element - The element to get the variable from (defaults to :root)
 * @returns The computed value of the CSS variable
 */
export function getCssVariable(
  variableName: string,
  element?: HTMLElement,
): string {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return ''; // Return empty string for SSR
  }

  const targetElement = element || document.documentElement;
  return getComputedStyle(targetElement).getPropertyValue(`--${variableName}`).trim();
}

/**
 * Set a CSS variable value
 * This function sets the value of a CSS variable
 * Does nothing during SSR when document is not available
 *
 * @param variableName - The CSS variable name (without the -- prefix)
 * @param value - The value to set
 * @param element - The element to set the variable on (defaults to :root)
 */
export function setCssVariable(
  variableName: string,
  value: string,
  element?: HTMLElement,
): void {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return; // Do nothing during SSR
  }

  const targetElement = element || document.documentElement;
  targetElement.style.setProperty(`--${variableName}`, value);
}

/**
 * Theme-aware style generation
 * This function helps generate styles based on the current theme
 *
 * @param baseStyles - Base styles that apply regardless of theme
 * @param themeStyles - Object with theme-specific styles
 * @returns Combined styles object
 */
export function themeStyle<T extends Record<string, any>>(
  baseStyles: T,
  themeStyles?: {
    light?: Partial<T>;
    dark?: Partial<T>;
    yellow?: Partial<T>;
    blue?: Partial<T>;
    frappe?: Partial<T>;
  },
): T {
  const currentTheme = getCurrentTheme();
  const themeSpecificStyles = themeStyles?.[currentTheme];

  return {
    ...baseStyles,
    ...themeSpecificStyles,
  } as T;
}

/**
 * Convert a color to a CSS variable reference
 * This function helps create CSS variable references for colors
 *
 * @param colorName - The color name (e.g., 'primary', 'surface')
 * @param variant - The color variant (e.g., '500', 'hover')
 * @returns CSS variable reference
 */
export function colorVar(colorName: string, variant?: string | number): string {
  if (variant) {
    return `var(--color-${colorName}-${variant})`;
  }
  return `var(--color-${colorName})`;
}

/**
 * Check if the system prefers dark mode
 * This function checks if the system color scheme preference is dark
 * Returns false during SSR when window is not available
 *
 * @returns True if the system prefers dark mode
 */
export function systemPrefersDarkMode(): boolean {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return false; // Default to false during SSR
  }

  return window.matchMedia('(prefers-color-scheme: dark)').matches;
}
