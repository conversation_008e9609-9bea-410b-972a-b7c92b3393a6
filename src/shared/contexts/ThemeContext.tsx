'use client';

import type React from 'react';
import { createContext, use, useEffect, useState } from 'react';
import { systemPrefersDarkMode } from '../utils/theme';

// We now support light, dark, yellow, blue, and frappe themes
export type Theme = 'light' | 'dark' | 'yellow' | 'blue' | 'frappe';

export type ThemeContextType = {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  systemTheme: Theme;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  enableSystemTheme?: boolean;
};

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'light',
  enableSystemTheme = true,
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [systemTheme, setSystemTheme] = useState<Theme>('light');
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    // This code will only run on the client side
    const savedTheme = localStorage.getItem('theme') as Theme | null;

    // Check system preference
    const prefersDark = systemPrefersDarkMode();
    const systemPreference: Theme = prefersDark ? 'dark' : 'light';
    setSystemTheme(systemPreference);

    // Determine initial theme
    let initialTheme: Theme;
    if (savedTheme) {
      // User has explicitly set a theme
      initialTheme = savedTheme;
    } else if (enableSystemTheme) {
      // Use system preference if enabled
      initialTheme = systemPreference;
    } else {
      // Fall back to default theme
      initialTheme = defaultTheme;
    }

    setThemeState(initialTheme);
    setIsInitialized(true);
  }, [defaultTheme, enableSystemTheme]);

  // Listen for system theme changes
  useEffect(() => {
    if (!enableSystemTheme) {
      return;
    }

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');

      // Only update theme if user hasn't explicitly set one
      if (!localStorage.getItem('theme')) {
        setThemeState(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [enableSystemTheme]);

  // Apply theme to document
  useEffect(() => {
    if (isInitialized) {
      // Save theme preference to localStorage
      localStorage.setItem('theme', theme);

      // Remove all theme classes
      document.documentElement.classList.remove('dark', 'theme-yellow', 'theme-blue', 'theme-frappe');

      // Apply appropriate theme class
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      } else if (theme === 'yellow') {
        document.documentElement.classList.add('theme-yellow');
      } else if (theme === 'blue') {
        document.documentElement.classList.add('theme-blue');
      } else if (theme === 'frappe') {
        document.documentElement.classList.add('theme-frappe');
      }
      // 'light' theme is the default (no class needed)
    }
  }, [theme, isInitialized]);

  // Toggle between all available themes
  const toggleTheme = () => {
    setThemeState((prevTheme) => {
      switch (prevTheme) {
        case 'light': return 'dark';
        case 'dark': return 'light';
        // case 'yellow': return 'blue';
        // case 'blue': return 'frappe';
        // case 'frappe': return 'light';
        default: return 'light';
      }
    });
  };

  // Set a specific theme
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  return (
    <ThemeContext value={{ theme, toggleTheme, setTheme, systemTheme }}>
      {children}
    </ThemeContext>
  );
};

export const useTheme = () => {
  const context = use(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
