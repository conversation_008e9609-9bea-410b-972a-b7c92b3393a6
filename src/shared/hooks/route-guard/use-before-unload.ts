'use client';

import { useEffect } from 'react';

export function useBeforeUnload(
  enabled: boolean,
  getMessage: () => string,
): void {
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (enabled) {
        const message = getMessage();
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
      return undefined; // Default return value when enabled is false
    };

    if (enabled) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [enabled, getMessage]);
}
