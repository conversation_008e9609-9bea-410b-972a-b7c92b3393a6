'use client';

import { useCallback, useEffect, useRef } from 'react';

type UseRouteGuardOptions = {
  when: boolean;
  message?: string;
  onBeforeUnload?: () => void;
  onRouteChangeStart?: (href?: string) => boolean;
};

export function useRouteGuard({
  when,
  message = 'You have unsaved changes. Are you sure you want to leave?',
  onBeforeUnload,
  onRouteChangeStart,
}: UseRouteGuardOptions) {
  const savedCallback = useRef<(href?: string) => boolean>(null);
  // Save the latest callback using useCallback for Next.js 15 optimization
  const memoizedCallback = useCallback(onRouteChangeStart || (() => true), [onRouteChangeStart]);

  useEffect(() => {
    savedCallback.current = memoizedCallback;
  }, [memoizedCallback]);

  // Handle browser navigation (refresh, close tab, etc.) - Next.js 15 compatible
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (when) {
        onBeforeUnload?.();
        e.preventDefault();
        e.returnValue = '';
        return message;
      }
      return undefined; // Ensure all code paths return a value
    };

    if (when) {
      window.addEventListener('beforeunload', handleBeforeUnload, { passive: false });
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [when, message, onBeforeUnload]);

  // Handle keyboard shortcuts for refresh (F5, Ctrl+R) - Next.js 15 enhanced
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!when) {
        return;
      }

      const isRefresh
        = e.key === 'F5'
          || (e.key.toLowerCase() === 'r' && (e.ctrlKey || e.metaKey))
          || (e.code === 'KeyR' && (e.ctrlKey || e.metaKey));

      if (isRefresh) {
        e.preventDefault();
        e.stopPropagation();

        const shouldProceed = savedCallback.current?.('refresh') ?? false;
        // eslint-disable-next-line no-empty
        if (!shouldProceed) {

        }
      }
    };

    if (when) {
      document.addEventListener('keydown', handleKeyDown, { capture: true, passive: false });
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true });
    };
  }, [when]);

  // Intercept link clicks for App Router navigation - Next.js 15 optimized
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (!when) {
        return;
      }

      const target = e.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;

      if (link && link.href) {
        const href = link.getAttribute('href');
        const isExternalLink = link.href.startsWith('http') && !link.href.includes(window.location.origin);
        const isHashLink = href?.startsWith('#');
        const isSpecialLink = href?.startsWith('mailto:') || href?.startsWith('tel:');

        // Only intercept internal navigation links
        if (!isExternalLink && !isHashLink && !isSpecialLink && href) {
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();

          const shouldProceed = savedCallback.current?.(href) ?? false;
          // eslint-disable-next-line no-empty
          if (!shouldProceed) {

          }
        }
      }
    };

    if (when) {
      document.addEventListener('click', handleClick, { capture: true, passive: false });
    }

    return () => {
      document.removeEventListener('click', handleClick, { capture: true });
    };
  }, [when]);
}
