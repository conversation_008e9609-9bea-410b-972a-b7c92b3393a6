'use client';

import type { ClassValue } from 'clsx';
import { useTheme } from '../contexts/ThemeContext';
import { cn, themeClass, themeStyle } from '../utils/theme';

/**
 * Hook for generating theme-aware styles
 * This hook provides utilities for creating styles that adapt to the current theme
 */
export function useThemeStyles() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  const isYellow = theme === 'yellow';
  const isBlue = theme === 'blue';
  const isFrappe = theme === 'frappe';

  /**
   * Generate theme-aware class names
   *
   * @param baseClasses - Base classes that apply regardless of theme
   * @param lightClasses - Classes that apply in light theme
   * @param darkClasses - Classes that apply in dark theme
   * @param yellowClasses - Classes that apply in yellow theme
   * @param blueClasses - Classes that apply in blue theme
   * @param frappeClasses - Classes that apply in frappe theme
   * @returns Combined classes string
   *
   * @example
   * const className = classes(
   *   'p-4 rounded',
   *   'bg-white text-gray-900',
   *   'bg-gray-800 text-white',
   *   'bg-yellow-100 text-yellow-900',
   *   'bg-blue-100 text-blue-900',
   *   'bg-indigo-100 text-indigo-900'
   * );
   */
  const classes = (
    baseClasses: ClassValue,
    lightClasses: ClassValue,
    darkClasses: ClassValue,
    yellowClasses?: ClassValue,
    blueClasses?: ClassValue,
    frappeClasses?: ClassValue,
  ): string => {
    let themeClasses: ClassValue;

    if (isDark) {
      themeClasses = darkClasses;
    } else if (isYellow && yellowClasses) {
      themeClasses = yellowClasses;
    } else if (isBlue && blueClasses) {
      themeClasses = blueClasses;
    } else if (isFrappe && frappeClasses) {
      themeClasses = frappeClasses;
    } else {
      themeClasses = lightClasses;
    }

    return cn(baseClasses, themeClasses);
  };

  /**
   * Generate theme-aware class names using an object
   *
   * @param baseClasses - Base classes that apply regardless of theme
   * @param themeClasses - Object with theme-specific classes
   * @returns Combined classes string
   *
   * @example
   * const className = classesObj(
   *   'p-4 rounded',
   *   {
   *     light: 'bg-white text-gray-900',
   *     dark: 'bg-gray-800 text-white',
   *     yellow: 'bg-yellow-100 text-yellow-900',
   *     blue: 'bg-blue-100 text-blue-900',
   *     frappe: 'bg-indigo-100 text-indigo-900'
   *   }
   * );
   */
  const classesObj = (
    baseClasses: ClassValue,
    themeClasses: {
      light?: ClassValue;
      dark?: ClassValue;
      yellow?: ClassValue;
      blue?: ClassValue;
      frappe?: ClassValue;
    },
  ): string => {
    return themeClass(baseClasses, themeClasses);
  };

  /**
   * Generate theme-aware styles
   *
   * @param baseStyles - Base styles that apply regardless of theme
   * @param themeStyles - Object with theme-specific styles
   * @returns Combined styles object
   *
   * @example
   * const styles = stylesObj(
   *   { padding: '1rem' },
   *   {
   *     light: { backgroundColor: 'white', color: 'black' },
   *     dark: { backgroundColor: 'black', color: 'white' },
   *     yellow: { backgroundColor: '#FFF9C4', color: '#F57F17' },
   *     blue: { backgroundColor: '#E3F2FD', color: '#0D47A1' },
   *     frappe: { backgroundColor: '#EEF2FF', color: '#4F46E5' }
   *   }
   * );
   */
  const stylesObj = <T extends Record<string, any>>(
    baseStyles: T,
    themeStyles: {
      light?: Partial<T>;
      dark?: Partial<T>;
      yellow?: Partial<T>;
      blue?: Partial<T>;
      frappe?: Partial<T>;
    },
  ): T => {
    return themeStyle(baseStyles, themeStyles);
  };

  /**
   * Get the current theme
   *
   * @returns The current theme ('light', 'dark', 'yellow', 'blue', or 'frappe')
   */
  const currentTheme = (): 'light' | 'dark' | 'yellow' | 'blue' | 'frappe' => {
    return theme;
  };

  /**
   * Check if the current theme is dark
   *
   * @returns True if the current theme is dark
   */
  const isThemeDark = (): boolean => {
    return isDark;
  };

  /**
   * Check if the current theme is yellow
   *
   * @returns True if the current theme is yellow
   */
  const isThemeYellow = (): boolean => {
    return isYellow;
  };

  /**
   * Check if the current theme is blue
   *
   * @returns True if the current theme is blue
   */
  const isThemeBlue = (): boolean => {
    return isBlue;
  };

  /**
   * Check if the current theme is frappe
   *
   * @returns True if the current theme is frappe
   */
  const isThemeFrappe = (): boolean => {
    return isFrappe;
  };

  return {
    classes,
    classesObj,
    stylesObj,
    currentTheme,
    isThemeDark,
    isThemeYellow,
    isThemeBlue,
    isThemeFrappe,
  };
}
