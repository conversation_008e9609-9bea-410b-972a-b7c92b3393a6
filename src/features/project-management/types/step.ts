import type { ETypeStep } from './workflow';

export type stepData = {
  id: string;
  name: string;
  description: '';
  position: number;
  type: ETypeStep;
  status: boolean;
};

export enum EOrderStep {
  INITIAL_SCREENING,
}

export type StepQA<T, D> = {
  id: string;
  projectId: string;
  stepInfo: T[];
  stepInfoPrevious: D[];
  stepInfoNext: any[];
  formStep: any[];
  conversation: conversationType[];
};

type conversationType = {
  projectId: string;
  stepId: string;
  createdAt: string;
  updatedAt: string;
  id: string;
};

export type infosQA = {
  id: string;
  infos: StepInfosQA[];
};

export type StepInfosQA = {
  question: string;
  answer: string;
  id: string;
  name: string;
  url?: any[];
  modelAI?: string;
};
