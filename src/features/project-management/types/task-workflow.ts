// Define task status type
export type TaskStatus = 'pending' | 'in-progress' | 'completed';

// Define step type
export type Step = {
  id: string;
  name: string;
  description: string;
  status: TaskStatus;
};

// Define task type
export type Task = {
  id: string;
  name: string;
  description: string;
  steps: Step[];
  status: TaskStatus;
};

export enum ETypeStep {
  FORM = 'form',
  EVALUATION = 'evaluation',
  OUTCOME = 'outCome',
}

export enum EStatusTask {
  PENDING = 'pending',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
}
