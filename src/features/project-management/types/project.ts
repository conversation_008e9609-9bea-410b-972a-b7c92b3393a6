import type { IFileResponse } from '@/shared/types/global';

export enum ProjectCampaignEnum {
  CORPORATE,
  CRISIS_MANAGEMENT,
  EVENT,
  GR_ADVOCACY,
  IMC,
  MARKET_RESEARCH,
  MEDIA_RELATION_PR,
  MI_BRAND_BRANDING,
  PRODUCT_LAUNCH,
  SOCIAL_DIGITAL_CORPORATE,
  SOCIAL_DIGITAL_PRODUCT,
  TVC_VIDEO_PRODUCTION,
}

export enum ProjectTypeVer2Enum {
  BLUE_C,
  MI_BRAND,
  MVV_ACADEMY,
  SNP,
}

export enum ProjectStatusEnum {
  PLANNED,
  IN_PROGRESS,
  COMPLETED,
  ON_HOLD,
}

export enum ProjectTypeEnum {
  BRANDING,
  GENERAL_CONSULTING,
  DIAGNOSTICS,
}

// Type definitions
type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

export type FilterState = {
  status: ProjectStatusEnum | 'All';
  type: ProjectTypeEnum | 'All';
  campaign: ProjectCampaignEnum | 'All';
  startDateRange: DateRange;
  endDateRange: DateRange;
  searchQuery: string;
};

export type SortOption = 'latest' | 'oldest';

export type Project = {
  id: string;
  name: string;
  slugName: string;
  description?: string;
  status: ProjectStatusEnum;
  type: ProjectTypeVer2Enum;
  campaign: ProjectCampaignEnumVer2;
  startDate: string;
  endDate?: string;

  // Client information
  clientName: string;
  address: string;
  taxCode: string;
  contactPerson: string;
  tel: string;
  email: string;
  industry?: string;

  // Team information
  ownedId?: string;
  memberIds: string[];

  // Metadata
  createdAt: string;
  updatedAt: string;
};

export type GetProjectsResponse = {
  items: Project[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type questionResponseCrew = {
  question: string;
  answer: string;
  id: string;
};

export type documentUrlCrew = {
  id: string;
  url: string;
  originalname: string;
  filename: string;
  key: string;
};

export type fileUploadResponse = {
  infos: stepInfoFile[];
  model: string;
};

export type stepInfoFile = {
  files: {
    file: string;
    name: string;
    type: string;
    id: string;
  }[];
  serviceOption: ProjectCampaignEnum;
};

export type stepInfosMarkdownResponse = {
  infos: { value: string }[];
  isGenerate: boolean;
  model: string;
  stepId: string;
};

export type TemplateFiles = {
  type: ETypeFile.BRIEF_QUESTION | ETypeFile.BRIEF_TEMPLATE | ETypeFile.QUOTATION;
  file: IFileResponse;
};

export enum ETypeFile {
  BRIEF_QUESTION = 'brief_question',
  BRIEF_TEMPLATE = 'brief_template',
  QUOTATION = 'quotation',
}

export type documentFileUpload = {
  infos: stepInfoDocumentFile[];
  order: number;
};

export type discoveryQuestionnaire = {
  infos: any;
  order: number;
};

export type stepInfoDocumentFile = {
  files: {
    file: string;
    name: string;
    type: string;
    id: string;
  }[];
  typeSelected: string[];
};

export type ScoringReportDataType = {
  report: string;
  score: number;
};

export enum ProjectCampaignEnumVer2 {
  // BLueC
  CCVH,
  EMPLOYER_BRANDING,
  GKNV,
  HSC,
  ST_VHDN,
  TNNV,
  TTNB,
  VHDN,
  VHLKHLTT,
  VHS,
  TTNT_VHDN,
  // MiBrand
  MARKET_RESEARCH,
  BRANDING,

  // SNP

  CORPORATE,
  CRISIS_MANAGEMENT,
  EVENT,
  GR_ADVOCACY,
  IMC,
  MEDIA_RELATION_PR,
  SOCIAL_DIGITAL_PRODUCT,
  TVC_VIDEO_PRODUCTION,
  FACTORY_OPENING,
  SOCIAL_DIGITAL_CORPORATE,

  BCC,
  // Chiến lược dẫn dắt về tư duy
  CLDD_VTD,
  // Chiến lược nội dung
  CLND,
  // Truyền thông thị trường quốc tế
  TTTR_QT,
  // Chương trình khuyến mại
  CTKM,
  // CSR
  CSR,
  // Digital Marketing
  DIGITAL_MARKETING,
  // GTM Thâm nhập thị trường mới
  GTM_TNTRM,
  // Hình ảnh lãnh đạo
  HALD,
  // Influencer Marketing Campaign
  INFLUENCER_MARKETING_CAMPAIGN,
  // Kích hoạt tài trợ
  KHTR,
  // SNP Media Relations
  SNP_MEDIA_RELATIONS,
  // Product Launch
  PRODUCT_LAUNCH,
  // Social Media Communication
  SOCIAL_MEDIA_COMMUNICATION,
  // Truyền miệng và lòng trung thành
  TM_LTT,
  // Truyền thông IPO
  TT_IPO,

  // MVV ACADEMY
  // Công nghệ trong đào tạo
  CNDT,
  // Giải pháp tích hợp
  GPTH,
  // Đào tạo
  DT,
  // Tư vấn
  TV,
  // MIBrand - extra

  // Brand Architecture
  BA,
  // Brand Design
  BD,
  // Brand Health Check
  BHC,
  // Brand Value Development
  BVD,
  // Chiến lược quản lý và phát triển kênh PP
  CLQL_PTPP,
  // Đo lường Hài lòng khách hàng
  DLHLKH,
}

export enum MiBrandCampaign {
}

export enum MVVAcademyCampaign {

}

export enum SNPCampaign {
}

export type CampaignListType = {
  label: string;
  value: string | number;
};
