import type { MessageRole } from '@copilotkit/runtime-client-gql';

export type MessageType = {
  content: string;
  createdAt: Date;
  id: string;
  role: MessageRole;
  status: { code: 'Success' };
  type: 'TextMessage' | 'ActionExecutionMessage' | 'ResultMessage';
  scope: string;
  arguments: Record<string, any>;
  actionExecutionId: string;
  actionName: string;
  result: string;
  name: string;
};

export type GetListMessageResponse = {
  items: ItemMessageType[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type ItemMessageType = {
  conversationId: string;
  createdAt: string;
  id: string;
  createdBy: {
    firstName: string;
    lastName: string;
    email: string;
  };
  data: MessageType[];
};
