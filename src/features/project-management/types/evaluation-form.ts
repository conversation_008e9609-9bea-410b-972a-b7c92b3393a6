export type IOverallScore = {
  score: number;
  percentile: number;
  rank: string;
  data: IOverallScoreData;
};

export type IOverallScoreData = {
  clientProfileSection: EvaluationCriteria[];
  financialCapacitySection: EvaluationCriteria[];
  collaborationSection: EvaluationCriteria[];
  growthPotentialSection: EvaluationCriteria[];
};

export enum SectionType {
  CLIENT_PROFILE = 'clientProfileSection',
  FINANCIAL_CAPACITY = 'financialCapacitySection',
  COLLABORATION = 'collaborationSection',
  GROWTH_POTENTIAL = 'growthPotentialSection',
}

// Evaluation criteria type
export type EvaluationCriteria = {
  id: string;
  criteria: string;
  answer: string;
  criteriaType: string;
  weight: string;
  criteriaScore: string;
  convertedScore: string;
  type: string;
  // Additional fields from the old store for backward compatibility
  confidence?: string;
  citation?: string;
  selectedOptionIndex?: number;
  name?: string;
};

// Overall score type
export type OverallScore = {
  score: number;
  percentile: number;
  rank: string;
  data: {
    [SectionType.CLIENT_PROFILE]: EvaluationCriteria[];
    [SectionType.FINANCIAL_CAPACITY]: EvaluationCriteria[];
    [SectionType.COLLABORATION]: EvaluationCriteria[];
    [SectionType.GROWTH_POTENTIAL]: EvaluationCriteria[];
  };
};
