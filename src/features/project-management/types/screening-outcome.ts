// Type definitions for the API response
export type ScreeningCriteria = {
  id: string;
  criteria: string;
  answer: string;
  confidence: string;
  citation: string;
  criteriaType: string;
  weight: string;
  criteriaScore: string;
  convertedScore: string;
  type: string;
};

export type ScreeningOutcomeData = {
  score: number;
  percentile: number;
  rank: string;
  data: {
    clientProfileSection?: ScreeningCriteria[];
    financialCapacitySection?: ScreeningCriteria[];
    collaborationSection?: ScreeningCriteria[];
    growthPotentialSection?: ScreeningCriteria[];
    [key: string]: ScreeningCriteria[] | undefined;
  };
};

// Type definitions for chart data
export type ChartDataItem = {
  name: string;
  amount: number;
  label?: string;
};

export type SectionChartData = {
  categories: {
    name: string;
    label: string;
  }[];
  series: {
    name: string;
    data: number[];
  }[];
};

export type ScreeningChartData = {
  criteriaData: ChartDataItem[];
  sectionData: ChartDataItem[];
  sectionACategories: {
    name: string;
    label: string;
  }[];
  sectionASeries: { name: string; data: number[] }[];
  sectionBCategories: {
    name: string;
    label: string;
  }[];
  sectionBSeries: { name: string; data: number[] }[];
  sectionCCategories: {
    name: string;
    label: string;
  }[];
  sectionCSeries: { name: string; data: number[] }[];
  sectionDCategories: {
    name: string;
    label: string;
  }[];
  sectionDSeries: { name: string; data: number[] }[];
};
