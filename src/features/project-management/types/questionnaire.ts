export type OptionSelectType = {
  id: number;
  label: string;
  description: string;
  value: EOptionSelect.FORM | EOptionSelect.MARKDOWN;
  isSelected: boolean;
};

export enum EOptionSelect {
  FORM = 'form',
  MARKDOWN = 'markdown',
}

export type QuestionnaireType = 'quantitative' | 'qualitative';

export type toggleOptionsType = {
  id: QuestionnaireType;
  label: string;
  type: EOptionSelect;
};

export type OptionChangeViewType = {
  id: string;
  label: string;
};

export enum EFrameworkType {
  DESK_RESEARCH,
  QUANTITATIVE,
  QUALITATIVE,
  OTHER,
}

export enum EDeskResearch {
  UPLOAD_FILE,
  RESEARCH,
  SUMMARIZED,
  SCORING,
}

export enum EQuantitative {
  QUESTIONNAIRE,
  SUMMARY,
  ANALYSIS,
  FILES,
  SCORING,
}

export type ConversationDataType = {
  id: string;
  order: number;
};
