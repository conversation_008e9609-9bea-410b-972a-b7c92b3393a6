import type { ItemFrameworkResponse } from '@/features/frameworks-templates/types';
import type { IFileResponse } from '@/shared/types/global';

export type CreateProjectResearch = {
  name: string;
  stepId: string;
  order: number;
  files?: ResearchTemplateCustomFile[];
  infos: {
    framework?: ItemFrameworkResponse | null;
    researchType?: ResearchFramework;
    template: IFileResponse[];
    files: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
  }[];
};

export type ResearchInfo = {
  template: IFileResponse[];
  researchType?: ResearchFramework;
  framework?: ItemFrameworkResponse | null;
  files?: IFileResponse[];
  otherTemplate?: string;
  frameWorkId: string;
  input?: string[];
  type?: ReportType;
  templateId?: string;
};

export type ResearchItem = {
  id: string;
  name: string;
  order: number;
  infos: ResearchInfo[];
  stepId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};

export type ResearchQueryParams = {
  itemsPerPage?: number;
  page?: number;
  searchValue?: string;
  stepId?: string;
  mark?: boolean;
};

export type GetResearchResponse = {
  items: ResearchItem[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type UpdateProjectResearch = {
  name: string;
  files?: ResearchTemplateCustomFile[];
  infos: {
    template: IFileResponse[];
    framework?: ItemFrameworkResponse | null;
    files?: IFileResponse[];
    otherTemplate?: string;
    frameWorkId: string;
    input?: string[];
    type?: ReportType;
    templateId?: string;
    researchType?: ResearchFramework;
  }[];
};

export enum ResearchFramework {
  DeskResearch,
  QuantitativeResearch,
  QualitativeResearch,
  ExploratoryResearch,
  DescriptiveResearch,
  ExplanatoryResearch,
  FieldResearch,
  ExperimentalResearch,
  Other,
}

export const frameworkOptions = [
  { value: ResearchFramework.DeskResearch, label: 'deskResearch', disable: false },
  { value: ResearchFramework.QuantitativeResearch, label: 'quantitative', disable: false },
  { value: ResearchFramework.QualitativeResearch, label: 'qualitative', disable: false },
  { value: ResearchFramework.ExploratoryResearch, label: 'exploratory', disable: true },
  { value: ResearchFramework.DescriptiveResearch, label: 'descriptive', disable: true },
  { value: ResearchFramework.ExplanatoryResearch, label: 'explanatory', disable: true },
  { value: ResearchFramework.FieldResearch, label: 'filed', disable: true },
  { value: ResearchFramework.ExperimentalResearch, label: 'experimental', disable: true },
  { value: ResearchFramework.Other, label: 'other', disable: true },
];

export const CONVERT_RESEARCH_FRAMEWORK_TO_LABEL: { [key: string]: string } = {
  [ResearchFramework.DeskResearch]: 'deskResearch',
  [ResearchFramework.QualitativeResearch]: 'qualitative',
  [ResearchFramework.QuantitativeResearch]: 'quantitative',
  [ResearchFramework.ExploratoryResearch]: 'exploratory',
  [ResearchFramework.DescriptiveResearch]: 'descriptive',
  [ResearchFramework.ExplanatoryResearch]: 'explanatory',
  [ResearchFramework.FieldResearch]: 'filed',
  [ResearchFramework.ExperimentalResearch]: 'experimental',
  [ResearchFramework.Other]: 'other',
};

// Report Type enum for document research
export enum ReportType {
  Report,
  PresentationDeck,
}

export const CONVERT_REPORT_TYPE_TO_LABEL: { [key: string]: string } = {
  [ReportType.Report]: 'report',
  [ReportType.PresentationDeck]: 'presentation',
};

export const reportTypeOptions = [
  { value: ReportType.Report, label: 'report', disable: false },
  { value: ReportType.PresentationDeck, label: 'presentation', disable: true },
];

export type ResearchTemplateCustomFile = {
  originalname: string;
  mimetype: string;
  size: string;
  url: string;
  key: string;
  bucket: string;
  provider: string;
  category: string;
};
