import type { EvaluationCriteria } from '../types/evaluation-form';
import { SectionType } from '../types/evaluation-form';

// Define the EvaluationFilter type here since we're removing the old store
export type EvaluationFilter = {
  criteriaType: 'All' | 'Qualitative' | 'Quantitative';
  confidenceLevel: 'All' | 'High' | 'Medium' | 'Low';
  searchQuery: string;
};

/**
 * Calculates the total weight of criteria in a section
 */
export const calculateSectionWeight = (criteria: EvaluationCriteria[]): number => {
  return criteria.reduce(
    (sum, criterion) => sum + Number.parseFloat(criterion.weight || '0'),
    0,
  );
};

/**
 * Calculates the total score of criteria in a section
 */
export const calculateSectionScore = (criteria: EvaluationCriteria[]): number => {
  return criteria.reduce(
    (sum, criterion) => sum + Number.parseFloat(criterion.convertedScore || '0'),
    0,
  );
};

/**
 * Formats a number to a fixed decimal string
 */
export const formatDecimal = (value: number, decimals = 2): string => {
  return value.toFixed(decimals);
};

/**
 * Updates a criterion with new values and calculates the converted score
 */
export const updateCriterionWithAnswer = (
  criterion: EvaluationCriteria,
  answer: string,
  selectedIndex?: number,
): EvaluationCriteria => {
  // Calculate the score (selectedIndex + 1 to convert from 0-based to 1-5 scale)
  const score = selectedIndex !== undefined ? selectedIndex + 1 : 0;
  const weight = Number.parseFloat(criterion.weight || '0');
  const convertedScore = (weight * score / 100).toFixed(2);

  return {
    ...criterion,
    answer,
    criteriaScore: score.toString(),
    convertedScore,
    selectedOptionIndex: selectedIndex,
  };
};

/**
 * Applies filters to a list of criteria
 */
export const applyFilters = (
  criteria: EvaluationCriteria[],
  filters: EvaluationFilter,
): EvaluationCriteria[] => {
  let filtered = [...criteria];

  // Filter by criteria type
  if (filters.criteriaType !== 'All') {
    filtered = filtered.filter(criterion =>
      criterion.criteriaType === filters.criteriaType,
    );
  }

  // Filter by confidence level
  if (filters.confidenceLevel !== 'All') {
    filtered = filtered.filter((criterion) => {
      // Handle case where confidence might not exist in the new store
      const confidenceStr = criterion.confidence || '0%';
      const confidenceValue = Number.parseInt(confidenceStr.replace('%', ''));

      switch (filters.confidenceLevel) {
        case 'High':
          return confidenceValue >= 70;
        case 'Medium':
          return confidenceValue >= 30 && confidenceValue < 70;
        case 'Low':
          return confidenceValue < 30;
        default:
          return true;
      }
    });
  }

  // Filter by search query
  if (filters.searchQuery) {
    const query = filters.searchQuery.toLowerCase();
    filtered = filtered.filter((criterion) => {
      const criteriaMatch = criterion.criteria.toLowerCase().includes(query);
      // Handle case where citation might not exist in the new store
      const citationMatch = criterion.citation
        ? criterion.citation.toLowerCase().includes(query)
        : false;

      return criteriaMatch || citationMatch;
    });
  }

  return filtered;
};

/**
 * Calculates the total score from all sections
 */
export const calculateTotalScore = (
  clientProfileSection: EvaluationCriteria[],
  financialCapacitySection: EvaluationCriteria[],
  collaborationSection: EvaluationCriteria[],
  growthPotentialSection: EvaluationCriteria[],
): number => {
  const sections = {
    clientProfileSection,
    financialCapacitySection,
    collaborationSection,
    growthPotentialSection,
  };

  return Object.values(sections).reduce((sum, section) => {
    return sum + section.reduce((acc, item) => {
      return acc + Number.parseFloat(item.convertedScore || '0');
    }, 0);
  }, 0);
};

/**
 * Calculates the total score from a store object (works with both old and new store formats)
 */
export const calculateTotalScoreFromStore = (
  store: Record<string, any>,
): number => {
  // For the new store format
  if (
    store[SectionType.CLIENT_PROFILE]
    && store[SectionType.FINANCIAL_CAPACITY]
    && store[SectionType.COLLABORATION]
    && store[SectionType.GROWTH_POTENTIAL]
  ) {
    return calculateTotalScore(
      store[SectionType.CLIENT_PROFILE],
      store[SectionType.FINANCIAL_CAPACITY],
      store[SectionType.COLLABORATION],
      store[SectionType.GROWTH_POTENTIAL],
    );
  }

  // For the old store format
  if (
    store.clientProfileSection
    && store.financialCapacitySection
    && store.collaborationSection
    && store.growthPotentialSection
  ) {
    return calculateTotalScore(
      store.clientProfileSection,
      store.financialCapacitySection,
      store.collaborationSection,
      store.growthPotentialSection,
    );
  }

  return 0;
};

/**
 * Determines the rank based on the score
 */
export const getRank = (score: number): string => {
  if (score >= 4) {
    return 'A';
  }
  if (score >= 3) {
    return 'B';
  }
  return 'C';
};

/**
 * Creates an update object for a specific section
 */
export const createSectionUpdateObject = (
  sectionType: SectionType,
  updatedCriteria: EvaluationCriteria[],
): Record<string, EvaluationCriteria[]> => {
  const updateObj: Record<string, EvaluationCriteria[]> = {};

  switch (sectionType) {
    case SectionType.CLIENT_PROFILE:
      updateObj[SectionType.CLIENT_PROFILE] = updatedCriteria;
      // For backward compatibility with old store
      updateObj.clientProfileSection = updatedCriteria;
      updateObj.evaluationCriteria = updatedCriteria; // Legacy support
      break;
    case SectionType.FINANCIAL_CAPACITY:
      updateObj[SectionType.FINANCIAL_CAPACITY] = updatedCriteria;
      // For backward compatibility with old store
      updateObj.financialCapacitySection = updatedCriteria;
      break;
    case SectionType.COLLABORATION:
      updateObj[SectionType.COLLABORATION] = updatedCriteria;
      // For backward compatibility with old store
      updateObj.collaborationSection = updatedCriteria;
      break;
    case SectionType.GROWTH_POTENTIAL:
      updateObj[SectionType.GROWTH_POTENTIAL] = updatedCriteria;
      // For backward compatibility with old store
      updateObj.growthPotentialSection = updatedCriteria;
      break;
  }

  return updateObj;
};
