import type { ChartDataItem, ScreeningChartData, ScreeningCriteria, ScreeningOutcomeData, SectionChartData } from '../types/screening-outcome';

/**
 * Maps the screening outcome data from API to chart-compatible format
 * @param data The screening outcome data from API
 * @returns Formatted data for charts
 */
export function mapScreeningOutcomeToChartData(data: ScreeningOutcomeData): ScreeningChartData {
  if (!data || !data.data) {
    throw new Error('Invalid screening outcome data');
  }

  // Extract sections
  // Extract sections with fallbacks to empty arrays
  const clientProfileSection = data.data.clientProfileSection || [];
  const financialCapacitySection = data.data.financialCapacitySection || [];
  const collaborationSection = data.data.collaborationSection || [];
  const growthPotentialSection = data.data.growthPotentialSection || [];

  // Calculate criteria proportions based on criteriaType
  const criteriaTypes = new Map<string, number>();

  // Count criteria by type across all sections
  [
    ...clientProfileSection,
    ...financialCapacitySection,
    ...collaborationSection,
    ...growthPotentialSection,
  ].forEach((criteria) => {
    const type = criteria.criteriaType;
    criteriaTypes.set(type, (criteriaTypes.get(type) || 0) + 1);
  });

  // Map criteria types to names
  const criteriaTypeNames = {
    1: 'Standard',
    2: 'Important',
    3: 'Extremely important',
  };

  const criteriaTypeNamesMultipleLanguage = {
    1: 'evaluationForm.standard',
    2: 'evaluationForm.important',
    3: 'evaluationForm.extremelyImportant',
  };

  // Create criteria proportion data
  const criteriaData: ChartDataItem[] = Array.from(criteriaTypes.entries())
    .map(([type, count]) => ({
      name: criteriaTypeNames[Number(type) as keyof typeof criteriaTypeNames] || `Type ${type}`,
      label: criteriaTypeNamesMultipleLanguage[Number(type) as keyof typeof criteriaTypeNames],
      amount: count,
    }))
    .sort((a, b) => {
      // Sort by importance (Extremely important first, then Important, then Standard)
      const order = { 'Extremely important': 1, 'Important': 2, 'Standard': 3 };
      return order[a.name as keyof typeof order] - order[b.name as keyof typeof order];
    });

  // Calculate section proportions based on total weights
  const calculateSectionWeight = (section: ScreeningCriteria[]): number => {
    return section.reduce((sum, criteria) => sum + Number.parseFloat(criteria.weight), 0);
  };

  const sectionWeights = {
    'Section A': calculateSectionWeight(clientProfileSection),
    'Section B': calculateSectionWeight(financialCapacitySection),
    'Section C': calculateSectionWeight(collaborationSection),
    'Section D': calculateSectionWeight(growthPotentialSection),
  };

  const sectionMultipleLanguage = {
    'Section A': 'evaluationForm.sectionA',
    'Section B': 'evaluationForm.sectionB',
    'Section C': 'evaluationForm.sectionC',
    'Section D': 'evaluationForm.sectionD',
  };

  const totalWeight = Object.values(sectionWeights).reduce((sum, weight) => sum + weight, 0);

  // Create section proportion data
  const sectionData: ChartDataItem[] = Object.entries(sectionWeights).map(([name, weight]) => ({
    name,
    label: sectionMultipleLanguage[name as keyof typeof sectionMultipleLanguage],
    amount: Math.round((weight / totalWeight) * 100),
  }));

  // Map section data to chart format
  const mapSectionToChartData = (section: ScreeningCriteria[]): SectionChartData => {
    const categories = section.map(item => ({ name: item.criteria, label: item.type }));
    const data = section.map(item => Number.parseInt(item.criteriaScore));

    return {
      categories,
      series: [{ name: 'evaluationForm.scoreChart', data }],
    };
  };

  // Create chart data for each section
  const sectionA = mapSectionToChartData(clientProfileSection);
  const sectionB = mapSectionToChartData(financialCapacitySection);
  const sectionC = mapSectionToChartData(collaborationSection);
  const sectionD = mapSectionToChartData(growthPotentialSection);

  return {
    criteriaData,
    sectionData,
    sectionACategories: sectionA.categories,
    sectionASeries: sectionA.series,
    sectionBCategories: sectionB.categories,
    sectionBSeries: sectionB.series,
    sectionCCategories: sectionC.categories,
    sectionCSeries: sectionC.series,
    sectionDCategories: sectionD.categories,
    sectionDSeries: sectionD.series,
  };
}
