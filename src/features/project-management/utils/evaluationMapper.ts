import type { EvaluationData, QAStrategistOutput } from '../types/evaluation';
import type { EvaluationCriteria } from '../types/evaluation-form';
import { PRE_COMPUTED_ANSWER_LISTS } from '../stores/project-workflow-store/slices/createEvaluationSlice';

export function mapEvaluationDataStore(data: EvaluationData, output: QAStrategistOutput): EvaluationData {
  const { initialEvaluationData, financialCapacitySection, collaborationSection, growthPotentialSection } = data;

  const getAnswerList = (type: string) => {
    return PRE_COMPUTED_ANSWER_LISTS[type] || [];
  };

  const getCriteriaScore = (type: string, value: string) => {
    const answerList = getAnswerList(type);
    const index = answerList.findIndex(item => item.value === value);
    return `${index + 1}`;
  };

  const mapperData = (initData: EvaluationCriteria[]) => {
    return initData.map((item) => {
      const strategistOutput = output[item.type as keyof QAStrategistOutput];
      const criteriaScore = getCriteriaScore(item.type, strategistOutput?.option);
      return {
        ...item,
        answer: strategistOutput?.option,
        confidence: ((+strategistOutput?.confidence) * 100).toString(),
        citation: strategistOutput?.sources?.join(','),
        criteriaScore,
        convertedScore: ((+item.weight * (+criteriaScore)) / 100).toFixed(2),
      };
    });
  };

  return {
    initialEvaluationData: mapperData(initialEvaluationData),
    financialCapacitySection: mapperData(financialCapacitySection),
    collaborationSection: mapperData(collaborationSection),
    growthPotentialSection: mapperData(growthPotentialSection),
  };
}
