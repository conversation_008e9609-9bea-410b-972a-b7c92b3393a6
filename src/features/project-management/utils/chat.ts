import type { RefObject } from 'react';
import type { MessageType } from '../types/chat';
import { ActionExecutionMessage, Role, TextMessage } from '@copilotkit/runtime-client-gql';
import type { Message } from '@copilotkit/runtime-client-gql';

export const ConvertMessageFromConversation = (messages: MessageType[]) => {
  return messages.map((msg) => {
    if (msg.type === 'ActionExecutionMessage') {
      return new ActionExecutionMessage({
        id: msg.id,
        name: msg.name,
        scope: msg.scope,
        arguments: msg.arguments,
        createdAt: msg.createdAt,
      });
    } else if (msg.type === 'ResultMessage') {
      return new TextMessage({
        id: msg.id,
        role: Role.Assistant,
        content: typeof msg.result === 'string' ? msg.result : JSON.stringify(msg.result),
        createdAt: msg.createdAt,
      });
    } else {
      return new TextMessage({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        createdAt: msg.createdAt,
      });
    }
  });
};

export async function handleSaveMessage(
  messages: Message[],
  lastSavedId: RefObject<string | null>,
  isInitialMessageRef: RefObject<boolean>,
  conversationId: string,
  saveMessage: (conversationId: string, data: TextMessage) => Promise<void>,
  FncCondition?: () => void,
) {
  if (messages.length === 0) {
    return;
  }

  let latest = messages[messages.length - 1] as TextMessage;
  if (!latest || latest.id === lastSavedId.current) {
    return;
  }
  if (latest.status.code !== 'Success') {
    return;
  }

  if (latest.isTextMessage() && !latest.content && latest.role === 'assistant') {
    latest = messages[messages.length - 2] as TextMessage;
  }

  if (latest.isActionExecutionMessage() || latest.isAgentStateMessage()) {
    return;
  }

  if (FncCondition) {
    FncCondition();
  }

  if (isInitialMessageRef.current) {
    return;
  }

  await saveMessage(conversationId, latest);
  return latest;
}
