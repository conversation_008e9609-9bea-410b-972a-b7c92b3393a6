// DirtyContext.tsx
import { createContext, use, useState } from 'react';

type DirtyContextType = {
  registerStep: (stepId: string, fn: () => boolean) => void;
  checkStepDirty: (stepId: string) => boolean;
  clearStep: (stepIndex: string) => void;
};

const DirtyContext = createContext<DirtyContextType>({
  registerStep: () => {},
  checkStepDirty: () => false,
  clearStep: () => {},
});

export const useDirty = () => use(DirtyContext);

export const DirtyStepProvider = ({ children }: { children: React.ReactNode }) => {
  const [checkFns, setCheckFns] = useState<Map<string, () => boolean>>(new Map());

  const registerStep = (stepId: string, fn: () => boolean) => {
    setCheckFns((prev) => {
      const newMap = new Map(prev);
      newMap.set(stepId, fn);
      return newMap;
    });
  };

  const checkStepDirty = (stepId: string) => {
    return checkFns.get(stepId)?.() ?? false;
  };

  const clearStep = (stepId: string) => {
    setCheckFns((prev) => {
      const newMap = new Map(prev);
      newMap.delete(stepId);
      return newMap;
    });
  };

  return (
    <DirtyContext value={{ registerStep, checkStepDirty, clearStep }}>
      {children}
    </DirtyContext>
  );
};
