import { z } from 'zod';
import { ProjectCampaignEnumVer2, ProjectStatusEnum, ProjectTypeVer2Enum } from '../types/project';

// Validation schema for creating a new project
export const createProjectSchema = (t: (key: string) => string) => {
  return z.object({
    clientName: z.string().min(1, t('nameRequired')),
    industry: z.string().optional(),
    type: z.nativeEnum(ProjectTypeVer2Enum),
    name: z.string().min(1, t('projectRequired')),
    campaign: z
      .nativeEnum(ProjectCampaignEnumVer2)
      .optional()
      .refine(val => val !== undefined, {
        message: t('campaignRequired'),
      }),
    description: z.string().optional(),
    startDate: z.string(),
    endDate: z.string().optional(),
    status: z.nativeEnum(ProjectStatusEnum).optional(),
    ownedId: z.string().optional(),
    memberIds: z.array(z.string()).optional(),
  });
};

export type CreateProjectPayload = z.infer<ReturnType<typeof createProjectSchema>>;
