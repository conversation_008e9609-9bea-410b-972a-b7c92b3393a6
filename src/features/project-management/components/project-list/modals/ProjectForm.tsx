'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import Input from '@/shared/components/form/input/InputField';
import TextArea from '@/shared/components/form/input/TextArea';
import Label from '@/shared/components/form/Label';
import Select from '@/shared/components/form/Select';
import SearchableMultiSelect from '@/shared/components/form/SearchableMultiSelect';
import { Button } from '@/shared/components/ui/button';
import { Calendar } from '@/shared/components/ui/calendar';
import { Modal } from '@/shared/components/ui/modal';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { ProjectStatusEnum, ProjectTypeVer2Enum } from '@/features/project-management/types/project';
import type { Project } from '@/features/project-management/types/project';
import { useTeamMembersForProject } from '@/features/project-management/hooks';
// import type { CreateProjectPayload } from '@/features/project-management/validation/project.validation';
import { useProjectUpdate } from '@/features/project-management/hooks/useProjectUpdate';
import type { z } from 'zod';
import { createProjectSchema } from '@/features/project-management/validation/project.validation';
import { vi } from 'date-fns/locale';
import { toast } from 'sonner';
import { CAMPAIGN_LIST_BY_TYPE } from '@/features/project-management/constants/project';

type ProjectFormModalType = {
  isOpen: boolean;
  project: Project;
  closeModal: () => void;
};

const ProjectFromModal: React.FC<ProjectFormModalType> = (
  {
    isOpen,
    project,
    closeModal,
  },
) => {
  const t = useTranslations('Project');

  const noticeTranslate = useTranslations('notice');

  const locale = useLocale();

  const messageTranslate = useTranslations('message');
  const { updateProject, isUpdating } = useProjectUpdate();
  const { teamMemberOptions, isLoading: isLoadingTeamMembers, handleSearch } = useTeamMembersForProject();
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [projectType, setProjectType] = useState<ProjectTypeVer2Enum>(ProjectTypeVer2Enum.BLUE_C);

  const [isOpenSearchMultiple, setIsOpenSearchMultiple] = useState<boolean>(false);

  // Convert memberIds to selected options format
  const defaultSelectedOptions = useMemo(() => {
    if (!project?.memberIds || !teamMemberOptions.length) {
      return [];
    }
    return teamMemberOptions.filter(option =>
      project.memberIds.includes(option.value),
    );
  }, [project?.memberIds, teamMemberOptions]);
  // Default form values
  const defaultValues = useMemo(() => {
    setProjectType(project?.type ?? ProjectTypeVer2Enum.BLUE_C);
    return (
      {
        // Project information
        name: project?.name ?? '',
        type: project?.type ?? ProjectTypeVer2Enum.BLUE_C,
        campaign: project?.campaign,
        description: project?.description ?? '',
        status: project?.status ?? ProjectStatusEnum.PLANNED,
        startDate: project?.startDate ?? new Date().toISOString(),
        endDate: project?.endDate ?? '',
        memberIds: project?.memberIds ?? [],
        // Client information
        clientName: project?.clientName ?? '',
        // address: project?.address ?? '',
        // taxCode: project?.taxCode ?? '',
        // contactPerson: project?.contactPerson ?? '',
        // tel: project?.tel ?? '',
        // email: project?.email ?? '',
        industry: project?.industry ?? '',
      }
    );
  }, [project]);
  const schema = createProjectSchema(messageTranslate);
  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues,
  });

  const resetForm = useCallback(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  // Reset form when project changes
  useEffect(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  const closeModalForm = () => {
    setIsEditMode(false);
    resetForm();
    closeModal();
  };

  const onSubmit = async (data: any) => {
    // Prepare the payload
    const projectPayload: any = {
      // Project information
      name: data.name,
      type: data.type,
      campaign: data.campaign,
      description: data.description || undefined,
      status: ProjectStatusEnum.PLANNED, // Default status
      startDate: data.startDate,
      endDate: data.endDate || undefined,
      memberIds: data.memberIds || [],

      // Client information
      clientName: data.clientName,
      // address: data.address,
      // taxCode: data.taxCode,
      // contactPerson: data.contactPerson,
      // tel: data.tel,
      // email: data.email,
      industry: data.industry || '',
    };

    try {
      await updateProject(projectPayload, project.id);
      toast.success(noticeTranslate('projectUpdateSuccess'));

      resetForm();
      closeModalForm();
    } catch (error) {
      toast.error(noticeTranslate('projectUpdateFail'));
      // Error is handled by the mutation hook
      console.error('Failed to create project:', error);
    }
  };

  const handleEditMode = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsEditMode(true);
  };

  const handleBackView = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    reset();
    setIsEditMode(false);
  };

  // const projectTypeOptions = [
  //   { value: ProjectTypeEnum.BRANDING.toString(), label: t('project_type_options.branding') },
  //   { value: ProjectTypeEnum.GENERAL_CONSULTING.toString(), label: t('project_type_options.general_consulting') },
  //   { value: ProjectTypeEnum.DIAGNOSTICS.toString(), label: t('project_type_options.diagnostics') },
  // ];

  const projectTypeOptions = [
    { value: ProjectTypeVer2Enum.BLUE_C, label: 'BlueC' },
    { value: ProjectTypeVer2Enum.MI_BRAND, label: 'MiBrand' },
    { value: ProjectTypeVer2Enum.MVV_ACADEMY, label: 'MVV Academy' },
    { value: ProjectTypeVer2Enum.SNP, label: 'SNP' },
  ];

  // const campaignOptions = [
  //   { value: ProjectCampaignEnum.CORPORATE.toString(), label: t('campaign_options.corporate') },
  //   { value: ProjectCampaignEnum.CRISIS_MANAGEMENT.toString(), label: t('campaign_options.crisis_management') },
  //   { value: ProjectCampaignEnum.EVENT.toString(), label: t('campaign_options.event') },
  //   { value: ProjectCampaignEnum.GR_ADVOCACY.toString(), label: t('campaign_options.gr_advocacy') },
  //   { value: ProjectCampaignEnum.IMC.toString(), label: t('campaign_options.imc') },
  //   { value: ProjectCampaignEnum.MARKET_RESEARCH.toString(), label: t('campaign_options.market_research') },
  //   { value: ProjectCampaignEnum.MEDIA_RELATION_PR.toString(), label: t('campaign_options.media_relation_pr') },
  //   { value: ProjectCampaignEnum.MI_BRAND_BRANDING.toString(), label: t('campaign_options.mi_brand_branding') },
  //   { value: ProjectCampaignEnum.PRODUCT_LAUNCH.toString(), label: t('campaign_options.product_launch') },
  //   { value: ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE.toString(), label: t('campaign_options.social_digital_corporate') },
  //   { value: ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT.toString(), label: t('campaign_options.social_digital_product') },
  //   { value: ProjectCampaignEnum.TVC_VIDEO_PRODUCTION.toString(), label: t('campaign_options.tvc_video_production') },
  // ];

  const handleChangeProjectType = (type: ProjectTypeVer2Enum) => {
    setProjectType(type);
    setValue('campaign', undefined as any);
  };

  const handleScrollForm = () => {
    if (isOpenSearchMultiple) {
      setIsOpenSearchMultiple(false);
    }
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {t('form_title_detail')}
            </h4>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            <div className="custom-scrollbar h-[450px] overflow-y-auto px-2 pb-3" onScroll={handleScrollForm}>
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                {/* Project Information Section */}
                <div className="col-span-2 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('project_section')}
                  </h5>
                </div>

                {/* Project Name */}
                <div className="col-span-2">
                  <Label htmlFor="name">
                    {t('project_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        disabled={!isEditMode}
                        id="name"
                        {...field}
                        placeholder={t('placeholder.project_name')}
                        type="text"
                        error={!!errors.name}
                        hint={errors.name?.message}
                      />
                    )}
                  />
                </div>

                {/* Project Type */}
                <div className="col-span-1">
                  <Label htmlFor="type">
                    {t('company')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="type"
                    render={({ field }) => (
                      <Select
                        disabled={!isEditMode}
                        options={projectTypeOptions}
                        defaultValue={field.value?.toString()}
                        onChange={(value) => {
                          field.onChange(Number(value));
                          handleChangeProjectType(Number(value));
                        }}
                        placeholder={t('placeholder.company')}
                        className="w-full"
                        bgDisabled="bg-neutral-50 text-neutral-400"
                      />
                    )}
                  />
                </div>

                {/* Campaign */}
                <div className="col-span-1">
                  <Label htmlFor="campaign">
                    {t('project_type')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="campaign"
                    render={({ field }) => (
                      <Select
                        disabled={!isEditMode}
                        options={CAMPAIGN_LIST_BY_TYPE[projectType]}
                        defaultValue={field?.value?.toString()}
                        onChange={value => field.onChange(Number(value))}
                        placeholder={t('placeholder.project_type')}
                        className="w-full"
                        bgDisabled="bg-neutral-50 text-neutral-400"
                        error={!!errors.campaign}
                        hint={errors.campaign?.message}
                      />
                    )}
                  />
                </div>

                {/* Team Members */}
                <div className="col-span-2">
                  <Controller
                    control={control}
                    name="memberIds"
                    render={({ field }) => (
                      <SearchableMultiSelect
                        label={t('team_members')}
                        options={teamMemberOptions}
                        defaultSelected={defaultSelectedOptions}
                        onChange={field.onChange}
                        onSearch={handleSearch}
                        disabled={isLoadingTeamMembers || !isEditMode}
                        placeholder={t('placeholder.select_team_members')}
                        searchPlaceholder={t('placeholder.search_team_members')}
                        isLoading={isLoadingTeamMembers}
                        backgroundColor="bg-neutral-50 text-neutral-400"
                        positionView="fixed"
                        isOpenSearch={isOpenSearchMultiple}
                        isOpenSearchFnc={setIsOpenSearchMultiple}
                      />
                    )}
                  />
                </div>

                {/* Description */}
                <div className="col-span-2">
                  <Label htmlFor="description">
                    {t('description')}
                  </Label>
                  <Controller
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <TextArea
                        {...field}
                        placeholder={t('placeholder.description')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                        disabled={!isEditMode}
                        rows={3}
                      />
                    )}
                  />
                </div>

                {/* Start Date */}
                <div className="col-span-1">
                  <Label htmlFor="startDate">
                    {t('start_date')}
                  </Label>
                  <Controller
                    control={control}
                    name="startDate"
                    render={({ field }) => (
                      <Popover>
                        <PopoverTrigger
                          disabled={!isEditMode}
                          className={`w-full flex items-center justify-between px-3 py-2 text-sm border border-gray-200 rounded-md dark:border-gray-700 ${!isEditMode ? 'bg-neutral-50 text-neutral-400' : ''}`}
                          id="start-date"
                        >
                          {field.value ? format(new Date(field.value), 'PPP', locale === 'vi' ? { locale: vi } : undefined) : <span className="text-gray-500">{t('placeholder.start_date')}</span>}
                          <CalendarIcon className="h-4 w-4 opacity-50" />
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-100000" align="start">
                          <Calendar
                            mode="single"
                            localeLocal={locale}
                            selected={field.value ? new Date(field.value) : undefined}
                            onSelect={(date: Date | undefined) => field.onChange(date ? date.toISOString() : '')}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </div>

                {/* End Date */}
                <div className="col-span-1">
                  <Label htmlFor="endDate">
                    {t('end_date')}
                  </Label>
                  <Controller
                    control={control}
                    name="endDate"
                    render={({ field, formState }) => {
                      const startDate = formState.defaultValues?.startDate;
                      return (
                        <Popover>
                          <PopoverTrigger
                            disabled={!isEditMode}
                            className={`w-full flex items-center justify-between px-3 py-2 text-sm border border-gray-200 rounded-md dark:border-gray-700 ${!isEditMode ? 'bg-neutral-50 text-neutral-400' : ''}`}
                            id="end-date"
                          >
                            {field.value ? format(new Date(field.value), 'PPP', locale === 'vi' ? { locale: vi } : undefined) : <span className="text-gray-500">{t('placeholder.end_date')}</span>}
                            <CalendarIcon className="h-4 w-4 opacity-50" />
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0 z-100000" align="start">
                            <Calendar
                              mode="single"
                              localeLocal={locale}
                              selected={field.value ? new Date(field.value) : undefined}
                              onSelect={(date: Date | undefined) => field.onChange(date ? date.toISOString() : '')}
                              initialFocus
                              disabled={date => startDate ? date < new Date(startDate) : false}
                            />
                          </PopoverContent>
                        </Popover>
                      );
                    }}
                  />
                </div>

                {/* Client Information Section */}
                <div className="col-span-2 mt-4 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('client_section')}
                  </h5>
                </div>

                {/* Client's Name */}
                <div className="col-span-2">
                  <Label htmlFor="clientName">
                    {t('client_name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="clientName"
                    render={({ field }) => (
                      <Input
                        id="clientName"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.client_name')}
                        type="text"
                        error={!!errors.clientName}
                        hint={errors.clientName?.message}
                      />
                    )}
                  />
                </div>

                {/* Address */}
                {/* <div className="col-span-1">
                  <Label htmlFor="address">
                    {t('address')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="address"
                    render={({ field }) => (
                      <Input
                        id="address"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.address')}
                        type="text"
                        error={!!errors.address}
                        hint={errors.address?.message}
                      />
                    )}
                  />
                </div> */}

                {/* Tax Code */}
                {/* <div className="col-span-1">
                  <Label htmlFor="taxCode">
                    {t('tax_code')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="taxCode"
                    render={({ field }) => (
                      <Input
                        id="taxCode"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.tax_code')}
                        type="text"
                        error={!!errors.taxCode}
                        hint={errors.taxCode?.message}
                      />
                    )}
                  />
                </div> */}

                {/* Contact Person */}
                {/* <div className="col-span-1">
                  <Label htmlFor="contactPerson">
                    {t('contact_person')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="contactPerson"
                    render={({ field }) => (
                      <Input
                        id="contactPerson"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.contact_person')}
                        type="text"
                        error={!!errors.contactPerson}
                        hint={errors.contactPerson?.message}
                      />
                    )}
                  />
                </div> */}

                {/* Tel */}
                {/* <div className="col-span-1">
                  <Label htmlFor="tel">
                    {t('tel')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="tel"
                    render={({ field }) => (
                      <Input
                        id="tel"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.tel')}
                        type="tel"
                        error={!!errors.tel}
                        hint={errors.tel?.message}
                      />
                    )}
                  />
                </div> */}

                {/* Email */}
                {/* <div className="col-span-1">
                  <Label htmlFor="email">
                    {t('email')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="email"
                    render={({ field }) => (
                      <Input
                        id="email"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.email')}
                        type="email"
                        error={!!errors.email}
                        hint={errors.email?.message}
                      />
                    )}
                  />
                </div> */}

                {/* Industry */}
                <div className="col-span-2">
                  <Label htmlFor="industry">
                    {t('industry')}
                  </Label>
                  <Controller
                    control={control}
                    name="industry"
                    render={({ field }) => (
                      <Input
                        id="industry"
                        {...field}
                        disabled={!isEditMode}
                        placeholder={t('placeholder.industry')}
                        type="text"
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              {!isEditMode
                ? (
                    <Button onClick={e => handleEditMode(e)}>
                      { t('edit_button') }
                    </Button>
                  )
                : (
                    <React.Fragment>
                      <Button variant="outline" onClick={handleBackView}>
                        { t('back_button') }
                      </Button>
                      <Button variant="outline" onClick={closeModalForm} disabled={isUpdating}>
                        {t('cancel_button')}
                      </Button>
                      <Button type="submit" disabled={isUpdating}>
                        {isUpdating ? t('updating_button') : t('update_button')}
                      </Button>
                    </React.Fragment>
                  )}
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
};

export default ProjectFromModal;
