'use client';

import { Card, CardContent } from '@/shared/components/ui/card';

const ProjectCardSkeleton = () => {
  return (
    <>
      <Card className="group overflow-hidden shadow-none transition-all duration-300 h-full">
        <CardContent className="px-5">
          {/* Header with title and status */}
          <div className="flex flex-wrap justify-between items-start gap-3 mb-4">
            <div>
              <div className="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex items-center gap-2 mt-1">
                <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-5">
            <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
          </div>

          {/* Timeline */}
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="flex-1 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default ProjectCardSkeleton;
