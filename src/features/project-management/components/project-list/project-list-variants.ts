import { cva } from 'class-variance-authority';

/**
 * Project List Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list container variants are defined using the cva utility for consistent styling.
 */
export const projectListContainerVariants = cva(
  'flex flex-col h-[calc(100vh-77px)]',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Error Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list error container variants are defined using the cva utility for consistent styling.
 */
export const projectListErrorContainerVariants = cva(
  'text-center py-8 flex flex-col items-center',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Error Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list error icon variants are defined using the cva utility for consistent styling.
 */
export const projectListErrorIconVariants = cva(
  'h-10 w-10 mb-2',
  {
    variants: {
      variant: {
        default: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Error Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list error message variants are defined using the cva utility for consistent styling.
 */
export const projectListErrorMessageVariants = cva(
  'mb-4',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Empty Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list empty message variants are defined using the cva utility for consistent styling.
 */
export const projectListEmptyMessageVariants = cva(
  'text-center py-8',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Loading Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list loading message variants are defined using the cva utility for consistent styling.
 */
export const projectListLoadingMessageVariants = cva(
  'text-sm',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
