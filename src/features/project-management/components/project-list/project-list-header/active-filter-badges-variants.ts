import { cva } from 'class-variance-authority';

/**
 * Active Filter Badges Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The active filter badges container variants are defined using the cva utility for consistent styling.
 */
export const activeFilterBadgesContainerVariants = cva(
  'flex flex-wrap gap-2 mt-4',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Active Filter Badge Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The active filter badge button variants are defined using the cva utility for consistent styling.
 */
export const activeFilterBadgeButtonVariants = cva(
  'h-4 w-4 p-0 ml-1 hover:bg-transparent',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Active Filter Reset Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The active filter reset button variants are defined using the cva utility for consistent styling.
 */
export const activeFilterResetButtonVariants = cva(
  'size-8 rounded-full',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Active Filter Reset Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The active filter reset icon variants are defined using the cva utility for consistent styling.
 */
export const activeFilterResetIconVariants = cva(
  'size-5',
  {
    variants: {
      variant: {
        default: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
