'use client';

import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import Input from '@/shared/components/form/input/InputField';
import { useDebounce } from '@/shared/hooks/useDebounce';
import { useProjectFilters } from '../../../hooks';

/**
 * ProjectSearch component
 *
 * Simple implementation with:
 * - Local state for immediate UI feedback
 * - Debounced updates to the search state
 * - No circular dependencies
 * - Accepts isLoading prop from parent instead of using useProjectList
 */
export default function ProjectSearch() {
  const t = useTranslations('Project');
  const { searchValue, updateSearchValue } = useProjectFilters();

  // Track if we're handling an update from user input
  const isUserInput = useRef(false);

  // Track the previous search value to detect external changes
  const prevSearchValue = useRef(searchValue);

  // Local state for the input value
  const [inputValue, setInputValue] = useState(searchValue);

  // Create a debounced version of the input value
  const debouncedValue = useDebounce(inputValue, 500);

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    isUserInput.current = true;
    setInputValue(e.target.value);
  }, []);

  // Update search value when debounced value changes (only from user input)
  useEffect(() => {
    // Only update if this change came from user input
    if (isUserInput.current && debouncedValue !== searchValue) {
      updateSearchValue(debouncedValue);
      isUserInput.current = false;
    }
  }, [debouncedValue, searchValue, updateSearchValue]);

  // Function to sync input with external search value changes
  const syncInputWithExternalChanges = useCallback(() => {
    // Check if search value changed externally
    if (searchValue !== prevSearchValue.current && !isUserInput.current) {
      setInputValue(searchValue);
    }

    // Update previous search value
    prevSearchValue.current = searchValue;
  }, [searchValue]);

  // Sync input with external search value changes
  useEffect(() => {
    syncInputWithExternalChanges();
  }, [syncInputWithExternalChanges]);

  return (
    <div className="flex-grow text-right relative">
      <Input
        type="text"
        id="search"
        placeholder={t('search_projects')}
        value={inputValue}
        onChange={handleSearchChange}
        className="md:max-w-[400px]"
      />
    </div>
  );
}
