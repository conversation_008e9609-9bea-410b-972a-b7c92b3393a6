'use client';

import type { VariantProps } from 'class-variance-authority';
import { format } from 'date-fns';
import { X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { PROJECT_CAMPAIGN_LABEL, PROJECT_STATUS_LABEL, PROJECT_TYPE_LABEL } from '@/features/project-management/constants';
import { useProjectFilters } from '@/features/project-management/hooks';
import { Badge } from '@/shared/components/ui/badge';
import { Button } from '@/shared/components/ui/button';
import { TrashIcon } from '@/shared/icons';
import { cn } from '@/shared/utils/utils';
import {
  activeFilterBadgeButtonVariants,
  activeFilterBadgesContainerVariants,
  activeFilterResetButtonVariants,
  activeFilterResetIconVariants,
} from './active-filter-badges-variants';
import type { FilterState } from '@/features/project-management/types/project';

type ActiveFilterBadgesProps = {
  onFilterRemove?: (filterType: keyof FilterState) => void;
  resetFilters?: () => void;
  variant?: VariantProps<typeof activeFilterBadgesContainerVariants>['variant'];
};

/**
 * ActiveFilterBadges Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays badges for active filters with the ability to remove them.
 */
export default function ActiveFilterBadges({
  onFilterRemove,
  resetFilters,
  variant = 'default',
}: ActiveFilterBadgesProps) {
  const { filters, searchValue, hasActiveFilters, clearSearchValue } = useProjectFilters();
  const t = useTranslations('Project');

  if (!hasActiveFilters) {
    return null;
  }

  const handleRemoveFilter = (filterType: keyof FilterState) => {
    // Call the callback if provided
    if (onFilterRemove) {
      onFilterRemove(filterType);
    }
  };

  const handleResetFilter = () => {
    if (resetFilters) {
      resetFilters();
    }
  };

  return (
    <div className={cn(activeFilterBadgesContainerVariants({ variant }))}>
      {filters.status !== 'All' && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('status')}
            :
            {t(PROJECT_STATUS_LABEL[filters.status])}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => handleRemoveFilter('status')}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_status_filter')}</span>
          </Button>
        </Badge>
      )}

      {filters.type !== 'All' && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('type')}
            :
            {t(PROJECT_TYPE_LABEL[filters.type])}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => handleRemoveFilter('type')}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_type_filter')}</span>
          </Button>
        </Badge>
      )}

      {filters.campaign !== 'All' && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('campaign')}
            :
            {t(PROJECT_CAMPAIGN_LABEL[filters.campaign])}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => handleRemoveFilter('campaign')}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_campaign_filter')}</span>
          </Button>
        </Badge>
      )}

      {searchValue !== '' && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('search')}
            :
            {searchValue}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => clearSearchValue()}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_search_filter')}</span>
          </Button>
        </Badge>
      )}

      {filters.startDate && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('start_date')}
            :
            {format(filters.startDate, 'MMM d, yyyy')}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => handleRemoveFilter('startDateRange')}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_start_date_filter')}</span>
          </Button>
        </Badge>
      )}

      {filters.endDate && (
        <Badge variant="outline" className="flex items-center gap-1 px-3 py-1.5">
          <span>
            {t('end_date')}
            :
            {format(filters.endDate, 'MMM d, yyyy')}
          </span>
          <Button
            variant="ghost"
            size="icon"
            className={cn(activeFilterBadgeButtonVariants({ variant }))}
            onClick={() => handleRemoveFilter('endDateRange')}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">{t('remove_end_date_filter')}</span>
          </Button>
        </Badge>
      )}

      {hasActiveFilters && (
        <Button
          variant="ghost"
          size="sm"
          className={cn(activeFilterResetButtonVariants({ variant }))}
          onClick={() => handleResetFilter()}
        >
          <TrashIcon className={cn(activeFilterResetIconVariants({ variant }))} />
        </Button>
      )}
    </div>
  );
}
