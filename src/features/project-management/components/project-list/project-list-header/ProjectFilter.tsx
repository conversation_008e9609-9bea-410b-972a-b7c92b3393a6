'use client';

import type { VariantProps } from 'class-variance-authority';
import Select from '@/shared/components/form/Select'; // Using the custom Select for better performance
import { Calendar } from '@/shared/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { cn } from '@/shared/utils/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useProjectFilters } from '../../../hooks';
import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../../../types/project';
import {
  projectFilterLabelVariants,
  projectFilterPlaceholderVariants,
  projectFilterTriggerVariants,
} from './project-filter-variants';

type ProjectFilterProps = {
  variant?: VariantProps<typeof projectFilterLabelVariants>['variant'];
};

/**
 * ProjectFilter Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides filter controls for project listing.
 */
export default function ProjectFilter({ variant = 'default' }: ProjectFilterProps = {}) {
  const t = useTranslations('Project');
  const { filters, updateFilter } = useProjectFilters();

  // Create options arrays for select components
  const statusOptions = [
    { value: 'All', label: t('all_statuses') },
    { value: ProjectStatusEnum.PLANNED.toString(), label: t('status_options.planned') },
    { value: ProjectStatusEnum.IN_PROGRESS.toString(), label: t('status_options.in_progress') },
    { value: ProjectStatusEnum.COMPLETED.toString(), label: t('status_options.completed') },
    { value: ProjectStatusEnum.ON_HOLD.toString(), label: t('status_options.on_hold') },
  ];

  const typeOptions = [
    { value: 'All', label: t('all_types') },
    { value: ProjectTypeEnum.BRANDING.toString(), label: t('project_type_options.branding') },
    { value: ProjectTypeEnum.GENERAL_CONSULTING.toString(), label: t('project_type_options.general_consulting') },
    { value: ProjectTypeEnum.DIAGNOSTICS.toString(), label: t('project_type_options.diagnostics') },
  ];

  const campaignOptions = [
    { value: ProjectCampaignEnum.CORPORATE.toString(), label: t('campaign_options.corporate') },
    { value: ProjectCampaignEnum.CRISIS_MANAGEMENT.toString(), label: t('campaign_options.crisis_management') },
    { value: ProjectCampaignEnum.EVENT.toString(), label: t('campaign_options.event') },
    { value: ProjectCampaignEnum.GR_ADVOCACY.toString(), label: t('campaign_options.gr_advocacy') },
    { value: ProjectCampaignEnum.IMC.toString(), label: t('campaign_options.imc') },
    { value: ProjectCampaignEnum.MARKET_RESEARCH.toString(), label: t('campaign_options.market_research') },
    { value: ProjectCampaignEnum.MEDIA_RELATION_PR.toString(), label: t('campaign_options.media_relation_pr') },
    { value: ProjectCampaignEnum.MI_BRAND_BRANDING.toString(), label: t('campaign_options.mi_brand_branding') },
    { value: ProjectCampaignEnum.PRODUCT_LAUNCH.toString(), label: t('campaign_options.product_launch') },
    { value: ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE.toString(), label: t('campaign_options.social_digital_corporate') },
    { value: ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT.toString(), label: t('campaign_options.social_digital_product') },
    { value: ProjectCampaignEnum.TVC_VIDEO_PRODUCTION.toString(), label: t('campaign_options.tvc_video_production') },
  ];

  // Handler for status change
  const handleStatusChange = (value: string) => {
    updateFilter('status', value === 'All' ? 'All' : Number(value) as ProjectStatusEnum);
  };

  // Handler for type change
  const handleTypeChange = (value: string) => {
    updateFilter('type', value === 'All' ? 'All' : Number(value) as ProjectTypeEnum);
  };

  // Handler for campaign change
  const handleCampaignChange = (value: string) => {
    updateFilter('campaign', value === 'All' ? 'All' : Number(value) as ProjectCampaignEnum);
  };

  // Handler for start date selection
  const handleStartDateSelect = (date: Date | undefined) => {
    updateFilter('startDate', date);
  };

  // Handler for end date selection
  const handleEndDateSelect = (date: Date | undefined) => {
    updateFilter('endDate', date);
  };

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm">{t('filter_projects')}</h3>

      {/* Status Filter */}
      <div className="space-y-1">
        <label htmlFor="status-select" className={cn(projectFilterLabelVariants({ variant }))}>
          {t('status')}
        </label>
        <Select
          options={statusOptions}
          onChange={handleStatusChange}
          defaultValue={filters.status.toString()}
          placeholder={t('placeholder.status')}
          className="w-full"
        />
      </div>

      {/* Type Filter */}
      <div className="space-y-1">
        <label htmlFor="type-select" className={cn(projectFilterLabelVariants({ variant }))}>
          {t('project_type')}
        </label>
        <Select
          options={typeOptions}
          onChange={handleTypeChange}
          defaultValue={filters.type.toString()}
          placeholder={t('placeholder.project_type')}
          className="w-full"
        />
      </div>

      {/* Campaign Filter */}
      <div className="space-y-1">
        <label htmlFor="campaign-select" className={cn(projectFilterLabelVariants({ variant }))}>
          {t('campaign')}
        </label>
        <Select
          options={campaignOptions}
          onChange={handleCampaignChange}
          defaultValue={filters.campaign.toString()}
          placeholder={t('placeholder.campaign')}
          className="w-full"
        />
      </div>

      {/* Date Filters */}
      <div className="space-y-1">
        <label htmlFor="start-date" className={cn(projectFilterLabelVariants({ variant }))}>
          {t('start_date')}
        </label>
        <Popover>
          <PopoverTrigger className={cn(projectFilterTriggerVariants({ variant }))} id="start-date">
            {filters.startDate ? format(filters.startDate, 'PPP') : <span className={cn(projectFilterPlaceholderVariants({ variant }))}>{t('placeholder.start_date')}</span>}
            <CalendarIcon className="h-4 w-4 opacity-50" />
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={filters.startDate}
              onSelect={handleStartDateSelect}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="space-y-1">
        <label htmlFor="end-date" className={cn(projectFilterLabelVariants({ variant }))}>
          {t('end_date')}
        </label>
        <Popover>
          <PopoverTrigger className={cn(projectFilterTriggerVariants({ variant }))} id="end-date">
            {filters.endDate ? format(filters.endDate, 'PPP') : <span className={cn(projectFilterPlaceholderVariants({ variant }))}>{t('placeholder.end_date')}</span>}
            <CalendarIcon className="h-4 w-4 opacity-50" />
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={filters.endDate}
              onSelect={handleEndDateSelect}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
