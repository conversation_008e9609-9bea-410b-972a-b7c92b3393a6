'use client';

import type { VariantProps } from 'class-variance-authority';
import type { FilterState } from '../../../types/project';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { cn } from '@/shared/utils/utils';
import { FilterIcon } from 'lucide-react';
import { useState } from 'react';
import { useProjectFilters } from '../../../hooks';
import NewProjectForm from '../NewProjectForm';
import ActiveFilterBadges from './ActiveFilterBadges';
import {
  projectListHeaderContainerVariants,
  projectListHeaderFilterButtonVariants,
  projectListHeaderFilterIndicatorVariants,
  projectListHeaderTitleVariants,
} from './project-list-header-variants';
import ProjectFilter from './ProjectFilter';
import ProjectSearch from './ProjectSearch';
import { useTranslations } from 'next-intl';

type ProjectListHeaderProps = {
  variant?: VariantProps<typeof projectListHeaderContainerVariants>['variant'];
  isLoading?: boolean;
};

/**
 * ProjectListHeader Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides header controls for project listing including search and filters.
 */
export default function ProjectListHeader({ variant = 'default' }: ProjectListHeaderProps = {}) {
  const t = useTranslations('main');

  const { hasActiveFilters, removeFilter, resetFilters } = useProjectFilters();

  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  // Handle filter removal from ActiveFilterBadges
  const handleFilterRemove = (filterType: keyof FilterState) => {
    removeFilter(filterType);
  };

  const handleResetFilters = () => {
    resetFilters();
  };

  return (
    <div className={cn(projectListHeaderContainerVariants({ variant }))}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className={cn(projectListHeaderTitleVariants({ variant }))}>{t('project')}</h2>

        <div className="flex flex-1 md:flex-row items-center gap-4 max-w-full ml-auto">
          <div className="w-full">
            <div className="flex items-center gap-4 w-full">
              {/* Search */}
              <ProjectSearch />

              {/* Filters Popup */}
              <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                <PopoverTrigger className={cn(projectListHeaderFilterButtonVariants({ variant }))}>
                  <FilterIcon className="size-4" />
                  {hasActiveFilters && (
                    <span className={cn(projectListHeaderFilterIndicatorVariants({ variant }))}></span>
                  )}
                </PopoverTrigger>
                <PopoverContent className="w-80 p-4">
                  <ProjectFilter />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Wrap the NewProjectForm with PermissionGuard */}
          <PermissionGuard permission={Permission.CREATE_PROJECT}>
            <NewProjectForm />
          </PermissionGuard>
        </div>
      </div>

      {/* Active filter badges */}
      {hasActiveFilters && (
        <ActiveFilterBadges
          onFilterRemove={handleFilterRemove}
          resetFilters={handleResetFilters}
        />
      )}
    </div>
  );
}
