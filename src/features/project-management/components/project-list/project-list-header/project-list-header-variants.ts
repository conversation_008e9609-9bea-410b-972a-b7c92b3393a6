import { cva } from 'class-variance-authority';

/**
 * Project List Header Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list header container variants are defined using the cva utility for consistent styling.
 */
export const projectListHeaderContainerVariants = cva(
  'p-4',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Header Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list header title variants are defined using the cva utility for consistent styling.
 */
export const projectListHeaderTitleVariants = cva(
  'text-xl font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Header Filter Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list header filter button variants are defined using the cva utility for consistent styling.
 */
export const projectListHeaderFilterButtonVariants = cva(
  'flex items-center justify-center size-11 text-sm font-medium border rounded-lg relative',
  {
    variants: {
      variant: {
        default: 'bg-background border-border hover:bg-muted',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project List Header Filter Indicator Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project list header filter indicator variants are defined using the cva utility for consistent styling.
 */
export const projectListHeaderFilterIndicatorVariants = cva(
  'absolute -top-1 -right-1 h-3 w-3 rounded-full border-2',
  {
    variants: {
      variant: {
        default: 'bg-yellow-500 border-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
