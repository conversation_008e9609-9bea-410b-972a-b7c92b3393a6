import { cva } from 'class-variance-authority';

/**
 * Project Filter Label Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project filter label variants are defined using the cva utility for consistent styling.
 */
export const projectFilterLabelVariants = cva(
  'text-xs font-medium',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Filter Trigger Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project filter trigger variants are defined using the cva utility for consistent styling.
 */
export const projectFilterTriggerVariants = cva(
  'w-full flex items-center justify-between px-3 py-2 text-sm border rounded-md',
  {
    variants: {
      variant: {
        default: 'border-border',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Project Filter Placeholder Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The project filter placeholder variants are defined using the cva utility for consistent styling.
 */
export const projectFilterPlaceholderVariants = cva(
  '',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
