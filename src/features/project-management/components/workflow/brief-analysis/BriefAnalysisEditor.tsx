'use client';

import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import Editor from '@/shared/components/ui/editor/editor';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { useCoAgent } from '@copilotkit/react-core';
import { useImperativeHandle, useState } from 'react';

type BriefAnalysisEditorType = {
  content?: string;
  ref?: any;
};

const BriefAnalysisEditor: React.FC<BriefAnalysisEditorType> = ({ content, ref }) => {
  const [markdownData, setMarkdownData] = useState<string>('');

  const { state: _state } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const handleSubmitForm = () => {
    // setCoAgentsState({
    //   ...state,

    // });

    return markdownData;
  };

  useImperativeHandle(ref, () => ({
    handleSubmitForm,
  }));

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setMarkdownData(markdown);
  };
  return (
    <Editor value={content} onChange={handleChangeEditor} />
  );
};

export default BriefAnalysisEditor;
