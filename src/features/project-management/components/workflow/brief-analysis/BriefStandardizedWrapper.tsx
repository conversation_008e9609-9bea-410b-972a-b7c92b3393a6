'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react';
import { useCoAgent, useCopilotAction, useCopilotChat, useCopilotMessagesContext } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep, useCurrentTask, useProjectInfo, useProjectName, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EDescriptionCopilotkit, EEndpointApiCopilotkit, ENameStateAgentCopilotkit, ETaskNameCopilot } from '@/shared/enums/global';
import BaseBriefAnalysis from './BaseBriefAnalysis';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useParams } from 'next/navigation';
import type { ProjectCampaignEnumVer2, TemplateFiles } from '@/features/project-management/types/project';
import { ETypeFile } from '@/features/project-management/types/project';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { htmlToMarkdownVer2, markdownToHTMLToDocFile, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useLocale, useTranslations } from 'next-intl';
import { useChatBoxMessage, useChatBoxSetOnSubmitCallback } from '@/features/project-management/stores/chatbox-store';
import { useSaveMessageAI } from '@/features/project-management/hooks/useSaveMessageAI';
import { useMessageGetList } from '@/features/project-management/hooks/useMessageGetList';
import type { TextMessage } from '@copilotkit/runtime-client-gql';
import { ConvertMessageFromConversation, handleSaveMessage } from '@/features/project-management/utils/chat';
import type { MessageType } from '@/features/project-management/types/chat';
import AIResult from '../common/AIResult';

type BriefAnalysisResponse = {
  infos: { value: string }[];
  isGenerate: boolean;
  model: string;
};

const BriefStandardizedWrapper: React.FC = () => {
  const t = useTranslations('workflow');

  const locale = useLocale();

  const [isShowEditButton, _setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnumVer2 | null>(null);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [isShowReGen, setIsShowReGen] = useState(false);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const titleConfirm = t('common.titleConfirmChange');

  const titleUnSave = t('common.titleUnSave');

  const descriptionUnSave = t('common.descriptionUnSave');

  const descriptionConfirm = t('common.descriptionConfirm');

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const projectName = useProjectName();

  const currentStepId = currentStep?.id;

  const project = useProjectInfo();

  const { data: briefStandardized } = useGetInfoDetail<BriefAnalysisResponse, any>(currentStep?.id ?? '');

  const { data: templates } = useGetListTemplates();

  const params = useParams<{ id: string }>();

  const lastMessage = useChatBoxMessage();

  const { reset } = useCopilotChat();

  const { mutateAsync: saveMessageAI } = useSaveMessageAI();

  const lastSavedId = useRef<string | null>(null);

  const isInitialMessageRef = useRef<boolean>(true);

  const { messages, setMessages } = useCopilotMessagesContext();

  const { data: messageList } = useMessageGetList(briefStandardized?.conversation[0]?.id ?? '');

  const setOnSubmitCallback = useChatBoxSetOnSubmitCallback();

  const abortControllerRef = useRef<AbortController | null>(null);

  // Ref to prevent double-clicking on next step button
  const isSubmittingRef = useRef<boolean>(false);

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  useEffect(() => {
    if (project) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(project.campaign);
    }
  }, [project]);

  useEffect(() => {
    if (templates) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  // Function to handle when a message is submitted in ChatBox
  const handleChatBoxSubmit = useCallback(() => {
    isInitialMessageRef.current = false;
  }, []);

  // Register the callback with the chatbox store
  useEffect(() => {
    setOnSubmitCallback(handleChatBoxSubmit);

    // Cleanup: remove the callback when component unmounts
    return () => {
      setOnSubmitCallback(null);
    };
  }, [setOnSubmitCallback, handleChatBoxSubmit]);

  const saveMessage = useCallback(async (conversationId: string, data: TextMessage) => {
    await saveMessageAI({
      conversationId,
      data,
    });
  }, []);

  useEffect(() => {
    const conversationId = briefStandardized?.conversation[0]?.id ?? '';

    const CheckConversationIdExist = () => {
      if (!conversationId) {
        // eslint-disable-next-line no-useless-return
        return;
      }
    };

    const latest = handleSaveMessage(
      messages,
      lastSavedId,
      isInitialMessageRef,
      conversationId,
      saveMessage,
      CheckConversationIdExist,
    );

    latest.then(res => lastSavedId.current = res?.id ?? '',
    );
  }, [messages, briefStandardized, saveMessage]);

  useEffect(() => {
    reset();
    if (messageList && messageList.items.length) {
      const messages = [...messageList.items];
      const dataMessage = (messages.reverse().flatMap(d => d.data[0]) ?? []) as MessageType[];
      setMessages(ConvertMessageFromConversation(dataMessage));
    }
  }, [messageList, setMessages, reset]);

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const { state } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  const saveDataFromAI = async (markdown: string, status: EStatusTask = EStatusTask.IN_PROGRESS) => {
    // convert same format data
    const html = await markdownToHtmlVer2(markdown);
    const markDownConvert = htmlToMarkdownVer2(html);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markDownConvert }],
          model: modelAISelected,
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status });
    }
  };

  const getAnalysisBriefData = async (data: any) => {
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.ANALYSIS }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const brief = res.data.result;

      updateMarkdownToState(brief);
      saveDataFromAI(brief);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      reset();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (briefStandardized && briefStandardized?.stepInfo.length) {
      const markdown = briefStandardized.stepInfo[0]?.infos[0]?.value;
      const isReGen = briefStandardized.stepInfo[0]?.isGenerate;
      const model = briefStandardized?.stepInfo[0]?.model ?? EValueModelAI.GPT;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowReGen(isReGen ?? false);

      updateMarkdownToState(markdown ?? '');
      // if (currentStep?.status !== EStatusTask.COMPLETED) {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(false);
      // } else {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(true);
      // }
    } else {
      if (briefStandardized?.stepInfoPrevious.length) {
        const briefFile = briefStandardized?.stepInfoPrevious[0].infos[0];
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowReGen(false);
        if (!templateFile.length) {
          return;
        }
        const data = {
          project_id: params.id,
          llm: modelAISelected,
          answer_brief_output: briefFile.value,
          ...templateFile.reduce((result, template) => {
            if (template.type === ETypeFile.BRIEF_TEMPLATE) {
              result.template_brief_url = [
                ...(result.template_brief_url || []),
                ...getFile([template.file], true),
              ];
            }
            if (template.type === ETypeFile.BRIEF_QUESTION) {
              result.question_brief_url = [
                ...(result.question_brief_url || []),
                ...getFile([template.file], true),
              ];
            }
            return result;
          }, {} as any),
        };
        getAnalysisBriefData(data);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [briefStandardized, templateFile]);

  const handleReGenBrief = async () => {
    if (!currentStepId) {
      return;
    }
    setIsLoading(true);
    updateStatus(currentStepId, EStatusTask.IN_PROGRESS);
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    await updateQuestionAnswer({ stepInfos: [] }, currentStepId);
  };

  useEffect(() => {
    const briefAnalysisState = state[ENameStateAgentCopilotkit.ANALYSIS];
    if (briefAnalysisState && briefAnalysisState.brief_analysis_output && briefAnalysisState.brief_analysis_process && briefAnalysisState.brief_analysis_process === 'done') {
      updateMarkdownToState(briefAnalysisState.brief_analysis_output);
    }
  }, [state]);

  const compareMarkdown = (form?: string) => {
    const markdownInitial = briefStandardized?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === (form || markdownCurrent);
  };

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const handleUpdateBriefAnalysis = async () => {
    const projectId = project?.id ?? '';
    const conversationId = briefStandardized?.conversation[0]?.id ?? '';

    const data = {
      project_id: projectId,
      conversation_id: conversationId,
      llm: modelAISelected,
      task_name: ETaskNameCopilot.BRIEF_ANALYSIS,
      instructions: lastMessage,
      original_content: markdown,
    };

    const baseUrl = window.location.origin;
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
      method: 'POST',
      body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.EDIT_CONTENT }),
      signal: abortControllerRef.current.signal,
    });

    const res = await response.json();

    const { result } = res.data;

    const { explain } = result;

    const { output } = result;

    await saveDataFromAI(output, EStatusTask.COMPLETED);
    return explain;
  };

  useCopilotAction({
    name: 'updateStandardizedBrief',
    description: EDescriptionCopilotkit.STANDARD_ANALYSIS,
    parameters: [],
    handler: async () => {
      return await handleUpdateBriefAnalysis();
    },
    render: ({ result }) => {
      return <AIResult result={result} />;
    },
  });

  const handleFinishStep = async (form?: string) => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: form ?? markdown }],
          model: modelAISelected,
        },
      ],
    };

    await mutateAsync({ id: currentStepId, status: EStatusTask.COMPLETED, select: 'all', isGenerate: true, stepIds: [], stepInfoIds: [] });

    await updateQuestionAnswer(payload, currentStepId);

    _setIsSaved(true);

    clearStep(currentStepId);
  };

  const handleApprove = async () => {
    if (!currentStepId || isSubmittingRef.current) {
      return;
    }

    // Set submitting flag to prevent double-clicking
    isSubmittingRef.current = true;

    setIsEditMode(false);

    if (currentStep.status !== EStatusTask.COMPLETED) {
      handleFinishStep();
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && currentTask.status !== EStatusTask.COMPLETED) {
      await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }
    console.log('>>>> click');
    completeStep(currentStepId);
  };

  const handleEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    if (!currentStepId) {
      return;
    }
    const isChanged = compareMarkdown(markdown);

    const isChangedModel = modelAIDefault !== modelAISelected;

    _setIsSaved(isChanged || !isChanged);
    registerStep(currentStepId, () => !isChanged || isChangedModel);
  };

  const discardChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setIsClickUnSaved(false);
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setMarkdown(form);
    setIsEditMode(false);
  };

  const handleConfirmPopUp = () => {
    if (isClickUnSaved) {
      setForm(markdown);
      setIsEditMode(false);
      setIsShowModal(false);
      return;
    }
    clearStep(currentStepId ?? '');
    handleFinishStep(form);
    setMarkdown(form);
    setIsEditMode(false);
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(markdown);
    await downloadMDToFile(html, projectName, nameStep[locale]!);
  };

  const isChangedModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAISelected, modelAIDefault]);

  const handleChangeModel = (data: string) => {
    setModelAISelected(data);

    const isChanged = compareMarkdown(markdown);

    const isChangedModel = modelAIDefault !== data;

    _setIsSaved(isChanged || !isChanged);
    registerStep(currentStepId!, () => !isChanged || isChangedModel);
  };

  return (
    <>
      <BaseBriefAnalysis
        isLoading={isLoading}
        markdown={markdown}
        form={form}
        isEditMode={isEditMode}
        isShowEditButton={isShowEditButton}
        modelAIDefault={modelAIDefault}
        isShowButtonReGenDropdown={isShowReGen || isChangedModel}
        onEditToggle={toggleEditMode}
        onConfirmChange={confirmChange}
        onDiscardChange={discardChange}
        onEditorChange={handleEditorChange}
        onApprove={handleApprove}
        onReGen={handleReGenBrief}
        onDownloadFile={handleDownloadFile}
        onChangeModel={handleChangeModel}
      />

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => { }}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => { }}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default BriefStandardizedWrapper;
