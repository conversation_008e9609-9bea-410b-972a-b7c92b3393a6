'use client';

import type { IFileResponse } from '@/shared/types/global';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';
import { http } from '@/core/http/http';
import { Env } from '@/core/config/Env';
import { useTranslations } from 'next-intl';

type FileUploadProps = {
  onFilesChange: (files: IFileResponse[]) => void;
  initialFile?: IFileResponse[];
  isDisable?: boolean;
  background?: string;
};

const FileUpload: React.FC<FileUploadProps> = ({ onFilesChange, initialFile, background, isDisable = false }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [fileUrls, setFileUrls] = useState<IFileResponse[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [urlInput, setUrlInput] = useState<string>('');

  const t = useTranslations('workflow');

  const setInitialFile = () => {
    if (initialFile?.length) {
      const saveFile = () => {
        setFileUrls(initialFile);
      };

      saveFile();
    }
  };

  useEffect(() => {
    setInitialFile();
  }, [initialFile]);

  const handleUploadFile = async (files: File[]) => {
    if (!files.length) {
      return Promise.all([]);
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const uploadPromises = files.map((file, index) => {
        const formData = new FormData();
        formData.append('file', file);

        return http.post<IFileResponse>({
          url: '/files/upload',
          data: formData,
          options: {
            headers: { 'Content-Type': 'multipart/form-data' },
            onUploadProgress: (progressEvent) => {
              if (progressEvent.total) {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setUploadProgress((prev) => {
                  const totalProgress = (prev * index + percentCompleted) / (index + 1);
                  return Math.round(totalProgress);
                });
              }
            },
          },
        });
      });
      return await Promise.all(uploadPromises);
    } catch (error) {
      toast.error(t('common.uploadFileFail'));
      throw error;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      try {
        const newFiles = [...files, ...acceptedFiles];
        setFiles(newFiles);

        const fileResponses = await handleUploadFile(acceptedFiles);
        const file = fileResponses.map(response => response.data);
        const newFileResponses = [...fileUrls, ...file].filter(f => !!f);
        setFileUrls(newFileResponses);
        // Pass both files and URLs to parent component
        onFilesChange(newFileResponses);
      } catch (error) {
        toast.error(t('common.uploadFileFail'));
        throw error;
      }
    },
    [files, fileUrls, onFilesChange],
  );

  const handleUrlUpload = () => {
    if (!urlInput.trim()) {
      toast.error(t('common.validateUrlFile'));
      return;
    }

    try {
      // eslint-disable-next-line no-new
      new URL(urlInput);
      const urlFileResponse: IFileResponse = {
        originalname: urlInput,
        key: urlInput,
        mimeType: 'url',
        filename: urlInput,
        url: urlInput,
        _id: `url_${Date.now()}`,
      };

      const newFileUrls = [...fileUrls, urlFileResponse];
      setFileUrls(newFileUrls);
      onFilesChange(newFileUrls);
      setUrlInput(''); // Clear input after adding
      toast.success(t('common.successProcessAdd'));
    } catch (error) {
      console.log(error);
      toast.error(t('common.validateUrlFile'));
    }
  };

  const removeFile = (index: number) => {
    const newFiles = [...files];
    const newFileUrls = [...fileUrls];

    newFiles.splice(index, 1);
    newFileUrls.splice(index, 1);

    setFiles(newFiles);
    setFileUrls(newFileUrls);
    onFilesChange(newFileUrls);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'application/pdf': [],
      'application/msword': [],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [],
      'application/vnd.ms-excel': [],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
      'application/vnd.ms-powerpoint': [],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': [],
    },
    disabled: isUploading || isDisable,
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors ${
          isDragActive && !isDisable
            ? 'border-primary bg-primary/5'
            : 'border-border hover:border-primary/70'
        } ${isUploading || isDisable ? 'opacity-50 cursor-not-allowed' : ''}  ${background}`}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center text-center">
          {isUploading
            ? (
                <div className="flex flex-col items-center w-full">
                  <svg className="animate-spin h-10 w-10 text-primary mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p className="text-sm text-foreground mb-2">
                    {t('common.uploadingFile')}
                    {uploadProgress}
                    %
                  </p>
                  <div className="w-full bg-muted rounded-full h-2.5">
                    <div
                      className="bg-primary h-2.5 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    >
                    </div>
                  </div>
                </div>
              )
            : (
                <>
                  <svg
                    className="w-10 h-10 mb-3 text-muted-foreground"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    >
                    </path>
                  </svg>
                  <p className="mb-2 text-sm text-foreground">
                    <span className="font-medium">
                      {t('common.clickUpload')}
                    </span>
                    {' '}
                    {t('common.textDescriptionDragAndDrop')}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {t('common.typeFile')}
                  </p>
                </>
              )}
        </div>

      </div>

      {/* URL Upload Section */}
      <div className="mt-4">
        <p className="text-sm text-muted-foreground mb-2">
          {t('common.textAddURL')}
        </p>
        <div className="flex gap-2">
          <input
            type="text"
            value={urlInput}
            onChange={e => setUrlInput(e.target.value)}
            placeholder={t('common.inputPlaceholder')}
            className="flex-1 px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isUploading || isDisable}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleUrlUpload();
              }
            }}
          />
          <button
            type="button"
            onClick={handleUrlUpload}
            disabled={isUploading || isDisable || !urlInput.trim()}
            className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t('common.buttonUpload')}
          </button>
        </div>
      </div>

      {fileUrls.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-foreground mb-2">
            {t('common.titleFiles')}
            (
            {fileUrls.length}
            )
          </h4>
          <ul className="space-y-2">
            {fileUrls.map((file, index) => (
              <li
                key={`${file.originalname}-${index}`}
                className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
              >
                <div className="flex items-center">
                  {file.id?.includes('url')
                    ? (
                        <svg
                          className="w-5 h-5 mr-2 text-muted-foreground"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                          />
                        </svg>
                      )
                    : (
                        <svg
                          className="w-5 h-5 mr-2 text-muted-foreground"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          >
                          </path>
                        </svg>
                      )}
                  <span className="text-sm text-foreground truncate max-w-xs">
                    {file.originalname}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {file && (
                    <a
                      href={file.id?.includes('url') ? file.url : `${Env.NEXT_PUBLIC_API_SERVER}/public/${file.key}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                      </svg>
                    </a>
                  )}
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="text-destructive hover:text-destructive/80"
                    disabled={isUploading || isDisable}
                  >
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      >
                      </path>
                    </svg>
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
