'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

import { PageList } from '@/shared/components/page-list';
import { ResearchCard, ResearchCardSkeleton } from './BussinessReSearchCard';
import { ResearchForm } from './BussinessReSearchForm';
import type { ResearchFormData } from './BussinessReSearchForm';
import { useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useProjectResearchCreate } from '@/features/project-management/hooks/useProjectResearchCreate';
import type { CreateProjectResearch, ResearchItem, ResearchQueryParams, UpdateProjectResearch } from '@/features/project-management/types/research';
import { useProjectResearchInfiniteQuery } from '@/features/project-management/hooks/useProjectResearchInfiniteQuery';
import { useInView } from 'react-intersection-observer';
import { useProjectResearchUpdate } from '@/features/project-management/hooks/useProjectResearchUpdate';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectResearchDelete } from '@/features/project-management/hooks/useProjectResearchDelete';
import ResearchDetailView from '../5_client_assessment/ResearchFrameworkWrapper';
import { EFrameworkType } from '@/features/project-management/types/questionnaire';
import { toast } from 'sonner';
import { useChatBoxHide, useToggleShowChatBox } from '@/features/project-management/stores/chatbox-store';

export function BussinessResearchList() {
  const [searchValue, setSearchValue] = useState('');
  const [editingItem, setEditingItem] = useState<ResearchItem | null>(null);
  const [viewResearch, setViewResearch] = useState<'list' | 'detail'>('list');
  const [id, setId] = useState<string>('');
  const [stepFormId, setStepFormId] = useState<string>('');
  const [nameTemplate, setNameTemplate] = useState<string>('');
  const [type, setType] = useState<EFrameworkType | null>(null);

  const currentStep = useCurrentStep();
  const currentTask = useCurrentTask();
  const hide = useChatBoxHide();
  const toggleShowChatBox = useToggleShowChatBox();

  const currentStepId = currentStep?.id;
  const { createProjectResearch } = useProjectResearchCreate();
  const { updateResearchItem } = useProjectResearchUpdate();
  const { deleteResearchItem } = useProjectResearchDelete();

  const { mutateAsync } = useUpdateStatusStep();

  const {
    updateStatus,
  } = useWorkflowActions();

  const t = useTranslations('Project');

  const workflowTranslate = useTranslations('workflow');

  // const router = useRouter();
  // const searchParams = useSearchParams();

  // Query parameters for research data
  const [queryParams, setQueryParams] = useState<ResearchQueryParams>({
    itemsPerPage: 10,
    searchValue: '',
    stepId: currentStepId,
  });

  // Infinite query for research data
  const {
    allResearchItems,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    invalidate,
  } = useProjectResearchInfiniteQuery(queryParams);

  // Intersection observer for infinite scroll
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '100px',
  });

  // Trigger fetch next page when in view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Update query params when search changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setQueryParams(prev => ({
        ...prev,
        searchValue,
      }));
    }, 300); // Debounce search

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  // Actions
  const handleCreateResearch = async (formData: ResearchFormData) => {
    const { name, template, framework, files, otherTemplate, frameWorkId, type, templateId, customFiles } = formData;
    const payload: CreateProjectResearch = {
      name,
      stepId: currentStepId ?? '',
      order: currentStep?.order ?? 0,
      files: customFiles,
      infos: [
        {
          template,
          framework,
          researchType: type,
          files: files || [],
          otherTemplate,
          frameWorkId,
          templateId,
        },
      ],
    };

    try {
      await createProjectResearch(payload);
      mutateAsync({ id: currentStepId ?? '', status: EStatusTask.COMPLETED });
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
      updateStatus(currentStepId ?? '', EStatusTask.COMPLETED);
      toast.success(workflowTranslate('research.createResearchSuccess'));

      invalidate();
    } catch (error) {
      toast.error(workflowTranslate('research.createResearchFail'));

      console.log(error);
    }
  };

  const handleEdit = (item: ResearchItem) => {
    setEditingItem(item);
  };

  const handleUpdateResearch = async (updatedItem: ResearchFormData) => {
    try {
      if (editingItem) {
        const { name, template, framework, files, otherTemplate, frameWorkId, type, templateId, customFiles } = updatedItem;

        const payload: UpdateProjectResearch = {
          name,
          files: customFiles,
          infos: [
            {
              template,
              framework,
              researchType: type,
              files: files || [],
              otherTemplate,
              frameWorkId,
              templateId,
            },
          ],
        };

        try {
          await updateResearchItem(editingItem.id, payload);
          // Invalidate and refetch the list to show the updated item
          invalidate();
          toast.success(workflowTranslate('research.updateResearchSuccess'));
          setEditingItem(null); // Close edit mode
        } catch (error) {
          toast.error(workflowTranslate('research.updateResearchFail'));
          console.log(error);
        }
      }
    } catch (error) {
      console.error('Error updating research:', error);
    }
  };

  useEffect(() => {
    if (viewResearch === 'detail') {
      toggleShowChatBox(true);
    } else {
      toggleShowChatBox(false);
      hide();
    }
  }, [viewResearch, toggleShowChatBox, hide]);

  const handleRetry = () => {
    refetch();
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteResearchItem(id);
      if (allResearchItems.length === 1 && currentTask?.status !== EStatusTask.PENDING) {
        mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.PENDING });
        updateStatus(currentTask?.id ?? '', EStatusTask.PENDING, true);
      }
      toast.success(workflowTranslate('research.deleteResearchSuccess'));
    } catch (err) {
      // Error is already handled in the hook with toast
      toast.error(workflowTranslate('research.deleteResearchFail'));
      console.error('Delete failed:', err);
    }
  };

  const handleSelectCard = (item: ResearchItem) => {
    setId(item.stepId);
    setStepFormId(item.id);
    setNameTemplate(item!.infos[0]!.otherTemplate as string);
    setType(((item!.infos[0]!.researchType as unknown) as EFrameworkType) ?? EFrameworkType.DESK_RESEARCH);
    setViewResearch('detail');
  };

  const onBackDashboard = () => {
    setViewResearch('list');
    setId('');
  };

  return (
    viewResearch === 'list'
      ? (
          <div className="bg-background">
            <PageList
              // Built-in header features
              description={workflowTranslate('businessForm.description')}
              showTotal={true}
              totalCount={allResearchItems.length}

              // Search and filter
              showSearch={true}
              searchValue={searchValue}
              onSearchChange={setSearchValue}
              searchPlaceholder={t('search_research_placeholder', { fallback: 'Search research analysis' })}
              showFilter={false}
              onFilterClick={() => console.log('Filter clicked')}

              // Create new functionality
              showCreateNew={true}
              createNewCard={(
                <ResearchForm
                  onCreateResearch={handleCreateResearch}
                  onUpdateResearch={handleUpdateResearch}
                  initialData={editingItem || undefined}
                  isEdit={!!editingItem}
                  onClose={() => setEditingItem(null)}
                />
              )}

              // State
              isLoading={isLoading}
              isError={!!error}
              isEmpty={false} // ✅ Never show empty state when we have create new card

              // Infinite scroll
              hasNextPage={hasNextPage}
              isFetchingNextPage={isFetchingNextPage}
              onLoadMore={fetchNextPage}

              // Actions
              onRetry={handleRetry}

              // Styling
              variant="default"
              gridCols={3}
              skeletonComponent={<ResearchCardSkeleton />}
              skeletonCount={6}
              emptyMessage={searchValue ? `${workflowTranslate('businessForm.noResearchFound')} "${searchValue}"` : workflowTranslate('businessForm.noResearch')}
              loadingMessage={workflowTranslate('businessForm.loadingMore')}
              className=""
            >
              {allResearchItems.map(item => (
                <ResearchCard
                  key={item.id}
                  item={item}
                  onEdit={handleEdit}
                  length={allResearchItems.length}
                  onDelete={handleDelete}
                  onSelectCard={handleSelectCard}
                />
              ))}

              {/* Intersection observer trigger for infinite scroll */}
              {hasNextPage && (
                <div ref={ref} className="h-10 flex items-center justify-center">
                  {isFetchingNextPage && <div className="text-sm text-gray-500">{workflowTranslate('common.loading')}</div>}
                </div>
              )}
            </PageList>
          </div>
        )
      : (
          <ResearchDetailView
            stepFormId={stepFormId}
            id={id}
            type={type}
            nameTemplate={nameTemplate}
            onBackDashboard={onBackDashboard}
          />
        )
  );
}
