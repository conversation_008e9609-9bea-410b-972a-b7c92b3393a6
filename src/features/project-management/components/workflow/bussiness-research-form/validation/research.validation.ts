import { ResearchFramework } from '@/features/project-management/types/research';
import { z } from 'zod';

// Create research schema
export const createResearchSchema = z.object({
  name: z.string().min(1, 'Research name is required').max(100, 'Research name must be less than 100 characters'),
  type: z.nativeEnum(ResearchFramework),
  framework: z.any().refine(val => val !== undefined && val !== null && val !== '', 'Research framework is required'),
  template: z.any().refine(val => val !== undefined && val !== null && val !== '', 'Research template is required'),
});

// Type for create research payload
export type CreateResearchPayload = z.infer<typeof createResearchSchema>;
