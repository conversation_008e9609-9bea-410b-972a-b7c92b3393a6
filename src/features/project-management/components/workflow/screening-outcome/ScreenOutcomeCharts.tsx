'use client';

import type { OverallScore } from '../../../types/evaluation-form';
import type { ScreeningCriteria, ScreeningOutcomeData } from '../../../types/screening-outcome';
import { SectionType } from '../../../types/evaluation-form';
import React, { useEffect, useMemo } from 'react';
import HorizontalBarChart from '@/shared/components/charts/bar/HorizontalBarChart';
import { DonutChart } from '@/shared/components/charts/pie/DonutChart';
import { useEvaluationActions, useOverallScore } from '../../../stores/project-workflow-store';
import { mapScreeningOutcomeToChartData } from '../../../utils/screeningOutcomeMapper';
import { useTranslations } from 'next-intl';

// Adapter function to convert OverallScore to ScreeningOutcomeData
const adaptOverallScoreToScreeningData = (score: OverallScore): ScreeningOutcomeData => {
  // Convert each section's data to ensure all required fields are present
  const convertSection = (section: any[]): ScreeningCriteria[] => {
    return section.map(item => ({
      ...item,
      // Ensure required fields have default values if they're undefined
      confidence: item.confidence || '0',
      citation: item.citation || '',
    }));
  };

  return {
    score: score.score,
    percentile: score.percentile,
    rank: score.rank,
    data: {
      clientProfileSection: convertSection(score.data.clientProfileSection),
      financialCapacitySection: convertSection(score.data.financialCapacitySection),
      collaborationSection: convertSection(score.data.collaborationSection),
      growthPotentialSection: convertSection(score.data.growthPotentialSection),
    },
  };
};

export default function ScreenOutcomeCharts() {
  const t = useTranslations('workflow');

  // Get data from the evaluation store
  const overallScore = useOverallScore();
  const { getFinalOverallScore } = useEvaluationActions();

  // Calculate the final score when the component mounts and whenever it's shown
  useEffect(() => {
    getFinalOverallScore();
  }, [getFinalOverallScore]);

  // Force a recalculation every second to ensure we have the latest data
  useEffect(() => {
    const intervalId = setInterval(() => {
      getFinalOverallScore();
    }, 1000);

    return () => clearInterval(intervalId);
  }, [getFinalOverallScore]);

  // Memoize the chart data to prevent unnecessary re-renders
  const chartData = useMemo(() => {
    // If store has data, map it to chart format
    if (overallScore && overallScore.data
      && overallScore.data[SectionType.CLIENT_PROFILE].length > 0) {
      try {
        // Use the adapter to convert the data to the expected format
        const adaptedData = adaptOverallScoreToScreeningData(overallScore);
        const dataMap = mapScreeningOutcomeToChartData(adaptedData);

        const getMultipleLanguageCategories = (data: { name: string; label: string }[]) => {
          return data.map(d => t(`evaluationForm.${d.label}`));
        };

        const getMultipleLanguageSeries = (data: { name: string; data: number[] }[]) => {
          return data.map(d => ({
            ...d,
            name: t(d.name),
          }));
        };

        const data = {
          ...dataMap,
          criteriaData: dataMap.criteriaData.map(d => ({
            ...d,
            label: t(d.label),
          })),

          sectionData: dataMap.sectionData.map(d => ({
            ...d,
            label: t(d.label),
          })),

          sectionACategories: getMultipleLanguageCategories(dataMap.sectionACategories),
          sectionBCategories: getMultipleLanguageCategories(dataMap.sectionBCategories),
          sectionCCategories: getMultipleLanguageCategories(dataMap.sectionCCategories),
          sectionDCategories: getMultipleLanguageCategories(dataMap.sectionDCategories),

          sectionASeries: getMultipleLanguageSeries(dataMap.sectionASeries),
          sectionBSeries: getMultipleLanguageSeries(dataMap.sectionBSeries),
          sectionCSeries: getMultipleLanguageSeries(dataMap.sectionCSeries),
          sectionDSeries: getMultipleLanguageSeries(dataMap.sectionDSeries),
        };
        return data;
      } catch (error) {
        console.error('Error mapping screening outcome data:', error);
      }
    }

    // Fallback data if no data is provided or mapping fails
    // Criteria Proportion Data
    const criteriaData = [
      { name: ('evaluationForm.extremelyImportant'), amount: 15 },
      { name: ('evaluationForm.important'), amount: 23 },
      { name: ('evaluationForm.standard'), amount: 62 },
    ];

    // Section Proportion Data
    const sectionData = [
      { name: ('evaluationForm.sectionA'), amount: 23 },
      { name: ('evaluationForm.sectionB'), amount: 23 },
      { name: ('evaluationForm.sectionC'), amount: 23 },
      { name: ('evaluationForm.sectionD'), amount: 23 },
    ];

    // Section A - Customer Profile Data
    const sectionACategories = [
      'Business Type',
      'Industry & ICP Fit',
      'Revenue Size',
      'Annual Marketing Budget',
      'Contract Type',
    ];
    const sectionASeries = [
      {
        name: 'Score',
        data: [5, 3, 4, 1, 2],
      },
    ];

    // Section B - Financial Capacity & Collaboration history Data
    const sectionBCategories = [
      'Payment History',
      'History of working with MVV Group',
      'Number of Project/Year',
      'Access to Decision Makers',
      'Decision Making Process',
    ];
    const sectionBSeries = [
      {
        name: 'Score',
        data: [5, 3, 4, 1, 5],
      },
    ];

    // Section C - Collaboration & Working Process Data
    const sectionCCategories = [
      'Decision-Making Process Complexity',
      'Marketing Maturity',
      'Information Sharing Readiness',
      'Project Collaboration Initiative',
      'Working Culture Fit',
    ];
    const sectionCSeries = [
      {
        name: 'Score',
        data: [5, 4, 2, 1, 5],
      },
    ];

    // Section D - Development Potential & Long-term Collaboration
    const sectionDCategories = [
      'Vision & Long-Term Commitment',
      'Multi-service Cooperation Potential',
      'Cross-Selling Potential',
      'Industry Influence',
      'Reference & Business Opportunity Potential',
    ];
    const sectionDSeries = [
      {
        name: 'Score',
        data: [5, 4, 3, 1, 5],
      },
    ];

    return {
      criteriaData,
      sectionData,
      sectionACategories,
      sectionASeries,
      sectionBCategories,
      sectionBSeries,
      sectionCCategories,
      sectionCSeries,
      sectionDCategories,
      sectionDSeries,
    };
  }, [overallScore.score]);

  return (
    <div className="space-y-8">
      <div className="rounded-2xl border border-border bg-background p-5 sm:p-6">

        {/* First row - Pie Charts side by side */}
        <div className="mb-12 grid grid-cols-1 gap-10 md:grid-cols-2">
          <div className="flex justify-center">
            {/* Criteria Proportion Pie Chart */}
            <div style={{ width: '400px', overflow: 'visible' }}>
              <DonutChart
                label={t('scoringOutcome.criteria')}
                data={chartData.criteriaData}
              />
            </div>
          </div>

          <div className="flex justify-center">
            {/* Section Proportion Pie Chart */}
            <div style={{ width: '400px', overflow: 'visible' }}>
              <DonutChart
                label={t('scoringOutcome.section')}
                data={chartData.sectionData}
              />
            </div>
          </div>
        </div>

        {/* Second row - Bar Charts in a 2x2 grid */}
        <div className="grid grid-cols-1 gap-x-10 gap-y-10 md:grid-cols-2">
          {/* Section A - Customer Profile */}
          <div>
            <p className="mb-4 font-medium">
              {t('evaluationForm.sectionATitle')}
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionASeries}
                categories={chartData.sectionACategories}
                colors={['#0079FF']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
                className="horizontal-hidden-tooltip"
              />
            </div>
          </div>

          {/* Section C - Collaboration & Working Process */}
          <div>
            <p className="mb-4 font-medium">
              {t('evaluationForm.sectionCTitle')}
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionCSeries}
                categories={chartData.sectionCCategories}
                colors={['#22D3EE']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
                className="horizontal-hidden-tooltip"
              />
            </div>
          </div>

          {/* Section B - Financial Capacity & Collaboration history */}
          <div>
            <p className="mb-4 font-medium">
              {t('evaluationForm.sectionBTitle')}
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionBSeries}
                categories={chartData.sectionBCategories}
                colors={['#FB923C']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
                className="horizontal-hidden-tooltip"
              />
            </div>
          </div>

          {/* Section D - Development Potential & Long-term Collaboration */}
          <div>
            <p className="mb-4 font-medium">
              {t('evaluationForm.sectionDTitle')}
            </p>
            <div className="pl-4">
              <HorizontalBarChart
                series={chartData.sectionDSeries}
                categories={chartData.sectionDCategories}
                colors={['#805CDB']}
                height={240}
                maxValue={5}
                showDataLabels={true}
                barWidth={60}
                className="horizontal-hidden-tooltip"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
