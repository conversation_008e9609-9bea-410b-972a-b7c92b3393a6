'use client';

import { useCoAgent } from '@copilotkit/react-core';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import ScreeningOutcomeMetrics from './ScreeningOutcomeMetrics';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useCurrentStep, useCurrentTask, useEvaluationActions, useOverallScore, useScoreDetail, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import type { ScoreDetail } from '@/features/project-management/types/evaluation';
import ScreenOutcomeCharts from './ScreenOutcomeCharts';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { stateRouteAgent } from '@/shared/types/global';
import type { assessmentStateFlow } from '@/features/project-management/types/agent';
import { GET_POSITION_BY_TYPE } from '@/features/project-management/constants/evaluation';
import type { OverallScore } from '@/features/project-management/types/evaluation-form';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { useParams } from 'next/navigation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import MessageWarning from '../common/MessageWarning';
import SelectModelAI from '../common/SelectModelAI';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';

export default function ScreeningOutcomeWrapper() {
  const t = useTranslations('workflow');

  const isVisible = useChatBoxVisible();

  const [_isLoadingData, setIsLoadingData] = useState<boolean>(true);

  const [isCallData, setIsCallData] = useState<boolean>(false);

  const [dataAnalysis, setDataAnalysis] = useState<any | null>(null);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const { mutateAsync } = useUpdateStatusStep();

  const { state: _stateAssessmentState } = useCoAgent<stateRouteAgent<assessmentStateFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {},
  });

  const overallScore = useOverallScore();

  const params = useParams<{ id: string }>();

  const {
    updateInitialEvaluationData,
    updateScoreDetail,
  } = useEvaluationActions();

  const scoreDetailStore = useScoreDetail();

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const { data: outComeData } = useGetInfoDetail<any, ScoreDetail>(currentStep?.id ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const queryClient = useQueryClient();

  const abortControllerRef = useRef<AbortController | null>(null);

  // Ref to prevent double-clicking on next step button
  const isSubmittingRef = useRef<boolean>(false);

  const hideLoadingComponent = () => {
    const setStatusLoading = () => {
      setIsLoadingData(false);
    };
    setStatusLoading();
  };

  const isChangeModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAISelected, modelAIDefault]);

  const saveDataFiveT = async (data: any) => {
    const payload = {
      stepInfos: Object.values(data).map((item: any, index: number) => ({
        order: index,
        infos: [{ ...item }],
        model: modelAISelected,
      })),
    };

    await updateQuestionAnswer(payload, currentStep?.id ?? '');
  };

  const setValueFiveTStateAgent = (overallScore: OverallScore) => {
    const { data } = overallScore;
    const items = Object.values(data).flat();
    return items.reduce((acc, i) => {
      const positionKey = GET_POSITION_BY_TYPE[i.type as keyof typeof GET_POSITION_BY_TYPE];
      acc[positionKey] = { ...i, id: positionKey.toString() };
      return acc;
    }, {} as Record<string, typeof items[number]>);
  };

  const getInfoAnalysis = async (data: any) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.ASSESSMENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const analysis = res.data.result;
      setDataAnalysis(analysis);
      const { engagement_strategy, risk_assessment, priority_score } = analysis;
      const { priorityScore, totalScore } = priority_score;
      await saveDataFiveT({ dimensions: { priorityScore: totalScore, dimensionScore: priorityScore }, listOfSuggestion: engagement_strategy, listRisk: risk_assessment });
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentStep?.id ?? '', EStatusTask.COMPLETED);
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);

      hideLoadingComponent();
    } catch (error: any) {
      // toast.error(error.message, {
      //   duration: 3000,
      // });
      console.error(error);
    }
  };

  useEffect(() => {
    if (overallScore.score && outComeData && !outComeData.stepInfo.length && !isCallData) {
      const five_t_input = setValueFiveTStateAgent(overallScore);
      const data = {
        project_id: params.id,
        five_t_input,
        llm: modelAISelected,
      };

      getInfoAnalysis(data);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsCallData(true);
    } else if ((outComeData && outComeData.stepInfo.length)) {
      const model = outComeData.stepInfo[0].model;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);

      hideLoadingComponent();
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsCallData(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [overallScore.score, outComeData]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (scoreDetailStore) {
      updateInitialEvaluationData(scoreDetailStore as any);
    }
  }, [scoreDetailStore]);

  // TODO: enhance it later
  useEffect(() => {
    if (outComeData && outComeData.stepInfoPrevious) {
      const data = { stepInfo: outComeData.stepInfoPrevious };
      updateScoreDetail(data as any);
    }
  }, [outComeData, updateScoreDetail]);

  // FIXME: update later when using Copilotkit
  // useEffect(() => {
  //   const assessmentState = stateAssessmentState[ENameStateAgentCopilotkit.ASSESSMENT];
  //   if ((assessmentState && assessmentState.client_assessment_process && assessmentState.client_assessment_process === 'done')
  //     || (outComeData && outComeData.stepInfo.length)
  //   ) {
  //     hideLoadingComponent();
  //   }
  // }, [stateAssessmentState, outComeData]);

  const handleSubmit = () => {
    if (isSubmittingRef.current) {
      return;
    }

    // Set submitting flag to prevent double-clicking
    isSubmittingRef.current = true;

    try {
      if (currentStep?.status !== EStatusTask.COMPLETED) {
        mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
        updateStatus(currentStep?.id ?? '', EStatusTask.COMPLETED);
      }

      if (currentTask && currentTask.status !== EStatusTask.COMPLETED) {
        mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
        updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
      }
      completeStep(currentStep?.id ?? '');
    } finally {
      // Reset submitting flag
      isSubmittingRef.current = false;
    }
  };

  const handleReGen = async () => {
    if (!currentStep) {
      return;
    }

    setIsLoadingData(true);
    setIsCallData(false);

    await mutateAsync({
      id: currentStep.id,
      status: EStatusTask.COMPLETED,
      stepIds: [currentStep.id],
      select: 'all',
      stepInfoIds: [],
      stepInfoIdsGenerate: [],
    });

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', currentStep.id], type: 'all' });
  };

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  return (
    _isLoadingData
      ? (
          <div className=" h-full p-4 md:p-6 ">
            <ProjectCardSkeleton />

            <MessageWarning />

          </div>
        )
      : (
          <div className="relative">

            { !isVisible && (
              <SelectModelAI
                onChangeModel={handleChangeModelAI}
                defaultValue={modelAIDefault}
                disable={false}
                isShowReGenButton={isChangeModel}
                onReGen={handleReGen}
              />
            )}

            <div className="space-y-7 p-4 md:p-6">
              <ScreeningOutcomeMetrics data={dataAnalysis} />
              <ScreenOutcomeCharts />
            </div>

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText={t('common.completeAndNext')}
              showPrevious={false}
            />
          </div>
        )
  );
}
