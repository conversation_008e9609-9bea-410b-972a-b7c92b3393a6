import { ModelAISelection } from '@/features/project-management/constants/modelAI';
import Select from '@/shared/components/form/Select';
import { Button } from '@/shared/components/ui/button';
import { RefreshCcw } from 'lucide-react';

type SelectModelAIType = {
  onChangeModel: (value: string) => void;
  defaultValue: string;
  disable: boolean;
  isShowReGenButton?: boolean;
  onReGen?: () => void;
  top?: string;
  right?: string;
  position?: string;
};

const SelectModelAI: React.FC<SelectModelAIType> = (
  {
    onChangeModel,
    onReGen,
    defaultValue,
    disable,
    isShowReGenButton,
    top,
    right,
    position = 'fixed',
  },
) => {
  const handleReGen = () => {
    if (onReGen) {
      onReGen();
    }
  };

  return (
    <div className={`${position} ${top || 'top-40'} ${right || 'right-6'} bg-white z-10 flex items-center gap-3`}>

      {isShowReGenButton && (
        <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
          <RefreshCcw className="h-5 w-5 " />
        </Button>
      )}

      <Select
        options={ModelAISelection}
        onChange={onChangeModel}
        defaultValue={defaultValue}
        isHiddenPlaceHolder={true}
        disabled={disable}
      />
    </div>

  );
};

export default SelectModelAI;
