type AIResultType = {
  result: string;
};
const AIResult: React.FC<AIResultType> = ({ result }) => {
  return (
    <div className="relative py-2 px-4 rounded-2xl rounded-tl-sm max-w-[80%] text-sm leading-relaxed bg-white border border-neutral-200 shadow-sm text-neutral-600">
      <div className="font-medium text-pink-600 mb-1">MVV AI</div>
      {
        result
        || (
          <div className="flex items-center gap-2 p-1">
            <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce"></div>
          </div>
        )
      }
    </div>
  );
};

export default AIResult;
