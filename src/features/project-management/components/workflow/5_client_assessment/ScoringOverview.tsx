import { useMemo } from 'react';
import { Brain } from 'lucide-react';

type ScoringOverviewType = {
  score: number;
  openModal: () => void;
};

const ScoringOverview: React.FC<ScoringOverviewType> = ({ score, openModal }) => {
  const getColor = useMemo(() => {
    if (score > 70) {
      return 'text-emerald-500';
    } else if (score > 50) {
      return 'text-amber-500';
    } else {
      return 'text-rose-500';
    }
  }, [score]);

  return (
    <>
      <div onClick={openModal} className="relative h-[32px] rounded-[16px] animated-border-gradient w-26 cursor-pointer">
        <div className={`bg-white absolute top-1/2 left-1/2 h-[24px] -translate-x-1/2 -translate-y-1/2  
          rounded-[16px] min-w-24 text-nowrap flex items-center justify-center ${getColor}`}
        >
          <Brain className="h-4 w-4 mr-1" />
          {' '}
          {score}
          {' '}
          / 100
        </div>
      </div>

    </>
  );
};

export default ScoringOverview;
