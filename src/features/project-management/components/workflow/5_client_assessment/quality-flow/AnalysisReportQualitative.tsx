import type { IFileResponse } from '@/shared/types/global';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { But<PERSON> } from '@/shared/components/ui/button';
import { CircleCheckIcon } from '@/shared/icons';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { EDescriptionCopilotkit, EEndpointApiCopilotkit, ETaskNameCopilot } from '@/shared/enums/global';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import FileUpload from '../../initial-screening-form/FileUpload';
import QuestionnaireAnalysis from '../../discovery-questionnaire/QuestionnaireAnalysis';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import type { QualitativeTypeRef } from './QualitativeQuestionnaireWrapper';
import MessageWarning from '../../common/MessageWarning';
import { useTranslations } from 'next-intl';
import { useCopilotAction } from '@copilotkit/react-core';
import AIResult from '../../common/AIResult';
import { useChatBoxMessage } from '@/features/project-management/stores/chatbox-store';
import { useProjectInfo } from '@/features/project-management/stores/project-workflow-store';

type AnalysisReportQualitativeType = {
  summary: string;
  templates: IFileResponse[];
  report: string;
  id: string;
  stepId: string;
  isAnalysis: boolean;
  isFinish: boolean;
  evaluationFramework: string;
  fileUpload: IFileResponse[];
  idQuality?: string;
  idScoring?: string;
  isLoading: boolean;
  modelSelected: string;
  modelDefault: string;
  selectedType: string;
  conversationIdAnalysis: string;
  setIsLoading: (status: boolean) => void;
  setIsAnalysis: (status: boolean) => void;
  setScoringData: (data: ScoringReportDataType | null) => void;
  ref?: React.Ref<QualitativeTypeRef>;

};

const AnalysisReportQualitative: React.FC<AnalysisReportQualitativeType> = ({
  summary,
  templates,
  report,
  id,
  stepId,
  isAnalysis,
  isFinish,
  evaluationFramework,
  fileUpload,
  idQuality,
  idScoring,
  ref,
  isLoading,
  modelSelected,
  modelDefault,
  selectedType,
  conversationIdAnalysis,
  setIsLoading,
  setIsAnalysis,
  setScoringData,
}) => {
  const t = useTranslations('workflow');

  const [reportData, setReportData] = useState<string>(() => report);

  const [files, setFiles] = useState<IFileResponse[]>(() => [...fileUpload]);

  const [initialFile, _setInitialFile] = useState<IFileResponse[]>(() => [...fileUpload]);

  const [isSaved, _setIsSaved] = useState(true);

  const [isShowModal, setIsShowModal] = useState(false);

  const { registerStep, clearStep } = useDirty();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  const titleConfirm = t('common.titleConfirmChange');

  const descriptionConfirm = t('common.descriptionConfirm');
  const params = useParams<{ id: string }>();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const abortControllerRef = useRef<AbortController | null>(null);

  const currentModelSelected = useRef<string>(modelSelected);

  const project = useProjectInfo();

  const lastMessage = useChatBoxMessage();

  useEffect(() => {
    currentModelSelected.current = modelSelected;
  }, [modelSelected]);

  const saveDataFromAI = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 7,
          type: EQuantitative.ANALYSIS,
          infos: [
            { form: markdown, status, isFinish: true },
          ],
          model: currentModelSelected.current,
        },

      ],
    };

    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      select: 'all',
      isGenerate: true,
      stepIds: [],
      stepInfoIds: [],
    });

    _setIsSaved(true);

    clearStep(stepId);

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);

    const isChangedFiled = !compareObjectArray(initialFile, uploadedFiles);

    const modelChanged = modelDefault !== modelSelected;
    _setIsSaved(!(isChangedFiled) || !modelChanged);

    registerStep(stepId, () => (isChangedFiled || modelChanged));
  }, []);

  const saveFileUpload = async () => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 0,
          type: EQuantitative.FILES,
          model: currentModelSelected.current,
          infos: [{
            files: files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
          }],
        },
      ],
    };

    await updateQuestionAnswer(payload, stepId);
  };

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
      llm: currentModelSelected.current,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EQuantitative.SCORING,
            order: 8,
            infos: [{ value: data }],
            model: currentModelSelected.current,
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const isChanged = useMemo(() => {
    if (initialFile.length === 0 && files.length === 0) {
      return true;
    }
    return !compareObjectArray(initialFile, files);
  }, [initialFile, files]);

  const isChangedModel = useMemo(() => {
    return modelDefault !== modelSelected;
  }, [modelDefault, modelSelected]);

  const saveDataInfos = async () => {
    if (isChanged || isChangedModel) {
      await saveFileUpload();
    }
    const payload = {
      project_id: params.id,
      llm: currentModelSelected.current,
      questionnaire_summary: summary,
      research_template_url: [...getFile(templates.length ? templates.filter(t => t.category === 'report') : [], true)],
      additional_info_url: [...getFile(files, true)],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.REPORT_QUALITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const data = res.data.result;
      await getScoringContent(data);
      setReportData(data);
      setIsLoading(false);
      saveDataFromAI(data, EStatusTask.IN_PROGRESS);
    } catch (error) {
      console.error(error);
    }
  };

  const handleGetAnalysis = async () => {
    const isChanged = !compareObjectArray(initialFile, files);

    const modelChanged = modelDefault !== modelSelected;

    clearStep(stepId);
    _setIsSaved(false);
    if ((isChanged || modelChanged) && reportData) {
      setIsShowModal(true);
      return;
    }

    if (!isChanged && reportData) {
      return;
    }
    setIsLoading(true);
    setIsAnalysis(true);
    await saveDataInfos();
  };

  useImperativeHandle(ref, () => {
    return {
      onReGen: async () => {
        setIsLoading(true);
        setReportData('');
        await saveFileUpload();
        await saveDataInfos();
      },
    };
  }, []);

  const handleConfirmPopUp = async () => {
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [idQuality ?? '', idScoring ?? ''],
      select: 'all',
      isGenerate: true,
    });
    saveDataInfos ();
    setIsLoading(true);
    setReportData('');
    clearStep(stepId);
    setIsAnalysis(true);
    setIsShowModal(false);
    setScoringData(null);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleViewAnalysis = () => {
    setFiles(initialFile);
    setIsAnalysis(true);
  };

  const handleEnhanceQuantityAnalysis = async () => {
    const projectId = project?.id ?? '';

    const data = {
      project_id: projectId,
      llm: currentModelSelected.current,
      conversation_id: conversationIdAnalysis,
      task_name: ETaskNameCopilot.QUALITY_ANALYSIS,
      instructions: lastMessage,
      original_content: reportData,
    };

    const baseUrl = window.location.origin;
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
      method: 'POST',
      body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.EDIT_CONTENT }),
      signal: abortControllerRef.current.signal,
    });

    const res = await response.json();

    const { result } = res.data;

    const { explain } = result;

    const { output } = result;

    await getScoringContent(output);

    await saveDataFromAI(output, EStatusTask.COMPLETED);
    return explain;
  };

  useCopilotAction({
    name: 'updateQualityAnalysis',
    description: EDescriptionCopilotkit.EDIT_QUALITY_ANALYSIS,
    available: selectedType === 'analysis' ? 'enabled' : 'disabled',
    parameters: [],
    handler: async () => {
      return await handleEnhanceQuantityAnalysis();
    },
    render: ({ result }) => {
      return <AIResult result={result} />;
    },
  });

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const onCompleteMarkdown = (markdown: string) => {
    clearStep(stepId);
    saveDataFromAI(markdown, EStatusTask.COMPLETED);
  };

  return (
    <>
      { isLoading
        ? (
            <React.Fragment>
              <ProjectCardSkeleton />
              <MessageWarning />
            </React.Fragment>
          )
        : !isAnalysis
            ? (
          // <div className="w-full mt-4 bg-gray-50 rounded-xl border border-bg-gray-100 flex gap-3 flex-col items-center justify-between py-12">
          //   <div>
          //     Generate analysis for this questionnaire?
          //   </div>

          //   <div className="text-xs text-gray-500">
          //     Click Confirm to start the analysis generation for this questionnaire.
          //   </div>

                // </div>
                <>
                  <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} />
                  <div className="flex gap-2 w-full items-center justify-center">
                    {reportData && (
                      <Button variant="outline" onClick={handleViewAnalysis} className="mt-4">
                        {t('common.viewAnalysis')}
                      </Button>
                    )}

                    <Button disabled={!isChanged && !isChangedModel} onClick={handleGetAnalysis} className="mt-4">
                      <CircleCheckIcon className="h-5 w-5" />
                      {t('common.confirm')}
                    </Button>
                  </div>
                </>
              )
            : (
                <QuestionnaireAnalysis
                  stepId={stepId}
                  data={reportData}
                  isHiddenBackButton={true}
                  isHiddenApproveButton={isFinish}
                  isFinish={isFinish}
                  onBack={() => {}}
                  onSubmit={onCompleteMarkdown}
                />
              )}

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titleConfirm}
        description={descriptionConfirm}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default AnalysisReportQualitative;
