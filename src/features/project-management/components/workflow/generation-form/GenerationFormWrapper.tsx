'use client';

import { ETypeStep } from '@/features/project-management/types/workflow';
import ProjectScopingGeneration from './ProjectScopingGeneration';
import QuotationRatingGeneration from './QuotationAndRateCardGeneration';

const GenerationFormWrapper: React.FC<{ type: ETypeStep }> = ({ type }) => {
  return type === ETypeStep.GENERATED ? <ProjectScopingGeneration /> : <QuotationRatingGeneration />;
};
export default GenerationFormWrapper;
