import { ReportType } from '@/features/project-management/types/research';
import { z } from 'zod';

// Create research schema
export const createResearchSchema = z.object({
  name: z.string().min(1, 'Research name is required').max(100, 'Research name must be less than 100 characters'),
  reportInput: z.array(z.string()).optional().default([]),
  reportType: z.nativeEnum(ReportType),
  template: z.any().refine(val => val !== undefined && val !== null && val !== '', 'Research template is required'),
  framework: z.any().refine(val => val !== undefined && val !== null && val !== '', 'Research framework is required'),
});

// Type for create research payload
export type CreateResearchPayload = z.infer<typeof createResearchSchema>;
