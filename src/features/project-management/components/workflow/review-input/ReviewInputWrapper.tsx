import { ETypeStep } from '@/features/project-management/types/workflow';
import ProjectScopingReviewInput from './ProjectScopingReviewInput';
import QuotationRatingReviewInput from './QuotationRatingReviewInput';

type ReviewInputWrapperProps = {
  type: ETypeStep;
};

const ReviewInputWrapper: React.FC<ReviewInputWrapperProps> = ({ type }) => {
  return type === ETypeStep.REVIEW_1
    ? <ProjectScopingReviewInput />
    : <QuotationRatingReviewInput />;
};

export default ReviewInputWrapper;
