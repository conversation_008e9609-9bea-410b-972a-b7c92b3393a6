'use client';
import { CopilotChat } from '@copilotkit/react-ui';
import React from 'react';
import { useChatBoxSubmit, useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import { AssistantAvatar, UserAvatar } from './ChatAvatar';
import { useTranslations } from 'next-intl';
import { INSTRUCTION_AI_PROMPT } from '@/shared/enums/global';

/**
 * ChatBox component that displays a chat interface
 *
 * The visibility is controlled by the useChatBoxVisibility hook
 */
export function ChatBox() {
  const t = useTranslations('workflow');
  // Get the visibility state from the custom hook
  const isVisible = useChatBoxVisible();

  const submit = useChatBoxSubmit();

  // If not visible, don't render the component
  if (!isVisible) {
    return null;
  }

  return (
    <CopilotChat
      className=""
      instructions={INSTRUCTION_AI_PROMPT}
      labels={{
        title: t('AI.title'),
        initial: t('AI.initial'),
        placeholder: t('AI.placeholder'),
      }}
      UserMessage={UserAvatar}
      AssistantMessage={AssistantAvatar}
      onSubmitMessage={msg => submit(msg)}
    />
  );
}
