'use client';

import { MessageSquareIcon } from 'lucide-react';
import React from 'react';
import { Button } from '@/shared/components/ui/button';
import { useChatBoxToggle, useChatBoxVisible } from '@/features/project-management/stores/chatbox-store/selectors/useChatbox';

type ChatBoxToggleButtonProps = {
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
};

/**
 * A button component that toggles the visibility of the ChatBox
 *
 * This can be placed anywhere in the application to provide access to the ChatBox
 */
export function ChatBoxToggleButton({
  className = '',
  variant = 'outline',
  size = 'icon',
}: ChatBoxToggleButtonProps) {
  // Get the visibility state and toggle method from the custom hook
  const isVisible = useChatBoxVisible();
  const toggle = useChatBoxToggle();

  return (
    <Button
      onClick={toggle}
      variant={variant}
      size={size}
      className={className}
      aria-label={isVisible ? 'Close chat' : 'Open chat'}
      title={isVisible ? 'Close chat' : 'Open chat'}
    >
      <MessageSquareIcon className="h-4 w-4" />
    </Button>
  );
}

export default ChatBoxToggleButton;
