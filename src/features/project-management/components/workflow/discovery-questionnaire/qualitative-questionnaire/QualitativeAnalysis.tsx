'use client';

import React from 'react';
import QuestionnaireAnalysis from '../QuestionnaireAnalysis';

type QualitativeAnalysisType = {
  markdown: string;
  isHidden?: boolean;
  onBack: () => void;
  stepId: string;
  onSubmit: (markdown: string) => void;
  onConfirm: (markdown: string) => void;
};

const QualitativeAnalysis: React.FC<QualitativeAnalysisType> = ({
  markdown,
  isHidden,
  stepId,
  onSubmit,
  onBack,
  onConfirm,
}) => {
  const handleSubmitData = (data: string) => {
    onSubmit(data);
  };

  return (
    <div className="w-full relative">

      <QuestionnaireAnalysis
        stepId={stepId}
        data={markdown}
        isHiddenApproveButton={isHidden}
        onSubmit={handleSubmitData}
        onBack={onBack}
        onConfirm={onConfirm}
      />
    </div>

  );
};

export default QualitativeAnalysis;
