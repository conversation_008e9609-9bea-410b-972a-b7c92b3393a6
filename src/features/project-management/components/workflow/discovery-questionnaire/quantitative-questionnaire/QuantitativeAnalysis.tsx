'use client';
import { But<PERSON> } from '@/shared/components/ui/button';
import { CircleCheckIcon } from '@/shared/icons';
import { useParams } from 'next/navigation';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { useEffect, useRef, useState } from 'react';
import { useCurrentStep } from '@/features/project-management/stores/project-workflow-store';
import type { TypeStepInfosRes } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import QuestionnaireAnalysis from '../QuestionnaireAnalysis';
import { useProjectStatisticRaw } from '@/features/project-management/hooks/useProjectStatisticRaw';

type QuantitativeAnalysisType = {
  markdown: string;
  isLoading: boolean;
  isAnalysis: boolean;
  stepInfos: TypeStepInfosRes[];
  idQuestionnaire: string;
  stepId: string;
  setIsLoading: (status: boolean) => void;
  setIsAnalysis: (status: boolean) => void;
  saveStepInfos: (stepInfos: TypeStepInfosRes[]) => void;
};

const QuantitativeAnalysis: React.FC<QuantitativeAnalysisType> = ({
  markdown: dataMarkdown,
  isLoading,
  isAnalysis,
  stepInfos,
  idQuestionnaire,
  stepId,
  setIsLoading,
  setIsAnalysis,
  saveStepInfos,
}) => {
  const params = useParams<{ id: string }>();

  const [markdown, setMarkdown] = useState<string>('');

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id;

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const isHidden = stepInfos?.find(info => info?.order === 0)?.infos[0]?.isSaved ?? false;

  const { refetch } = useProjectStatisticRaw(idQuestionnaire);

  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setMarkdown(dataMarkdown);
  }, [dataMarkdown]);

  const saveDataFromAI = async (markdown: string, isSaved: boolean) => {
    const stepInfosData = [
      ...stepInfos.filter(infos => infos.order === 1),
      {
        order: 0,
        infos: [{ value: markdown, isSaved }],
        model: '',
      },
    ];

    const payload = {
      stepInfos: stepInfosData,
    };

    await updateQuestionAnswer(payload, currentStepId ?? '');

    saveStepInfos(stepInfosData);
  };

  const handleGetRawData = async () => {
    const { data } = await refetch();
    return data;
  };

  const handleGetAnalysis = async () => {
    setIsLoading(true);
    setIsAnalysis(true);
    const data = await handleGetRawData();
    const payload = {
      project_id: params.id,
      quantity_questionnaire_survey: { data },
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCOPE }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const markdown = res.data.result;
      setMarkdown(markdown);
      saveDataFromAI(markdown, false);
      setIsLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleSubmitData = async (data: string) => {
    setMarkdown(data);

    saveDataFromAI(markdown, true);
  };

  return (
    isLoading
      ? (
          <div className="mt-4 w-full">
            Loading...
            <ProjectCardSkeleton />
          </div>
        )
      : (
          isAnalysis

            ? (
                <div className="mt-4 relative">
                  <QuestionnaireAnalysis
                    stepId={stepId}
                    data={markdown}
                    isHiddenApproveButton={isHidden}
                    onBack={() => setIsAnalysis(false)}
                    onSubmit={handleSubmitData}
                  />
                </div>
              )

            : (
                <div className="w-full mt-4 bg-gray-50 rounded-xl border border-bg-gray-100 flex gap-3 flex-col items-center justify-between py-12">
                  <div>
                    Generate analysis for this questionnaire?
                  </div>

                  <div className="text-xs text-gray-500">
                    The questionnaire status will be changed to
                    {' '}
                    <span className="font-medium text-black">Closed</span>
                    {' '}
                    once click
                    {' '}
                    <span className="font-medium text-black">Confirm</span>
                    .
                  </div>

                  <Button onClick={handleGetAnalysis}>
                    <CircleCheckIcon className="h-5 w-5" />
                    Confirm
                  </Button>

                </div>
              )
        )

  );
};

export default QuantitativeAnalysis;
