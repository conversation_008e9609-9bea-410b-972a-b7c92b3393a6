import HorizontalBarChart from '@/shared/components/charts/bar/HorizontalBarChart';

type DataProps = {
  title: string;
  percentage: number;
};

type HorizontalBarChartSurveyType = {
  title: string;
  data: DataProps[];
};
const HorizontalBarChartInfo: React.FC<HorizontalBarChartSurveyType> = ({ title, data }) => {
  const series = [{
    name: 'Percentage',
    data: (data ?? []).map(d => d.percentage),
  }];

  const categories = (data ?? []).map(d => d.title);

  return (
    <>
      <p className="text-gray-500 mb-1">
        {title}
        :
      </p>
      <div className="flex justify-center items-center">
        <HorizontalBarChart
          series={series}
          categories={categories}
          colors={['#0079FF']}
          height={240}
          showDataLabels={true}
          barWidth={60}
        />
      </div>
    </>
  );
};

export default HorizontalBarChartInfo;
