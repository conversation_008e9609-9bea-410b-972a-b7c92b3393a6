import { PieChart } from '@/shared/components/charts/pie/PieChart';
import { useState } from 'react';

type DataProps = {
  title: string;
  percentage: number;
};

type PieChartSurveyType = {
  title: string;
  data: DataProps[];
};

type DataChart = {
  name: string;
  amount: number;
};
const PieChartSurveyInfo: React.FC<PieChartSurveyType> = ({ title, data }) => {
  const [dataChart, _setDataChart] = useState<DataChart[]>(() => (data ?? []).map(d => ({
    name: d.title,
    amount: d.percentage,
  })));

  return (
    <>
      <p className="text-gray-500 mb-1">
        {title}
        :
      </p>
      <div className="flex justify-center items-center pie-chart">
        <PieChart
          label={title}
          data={dataChart}
        />
      </div>
    </>
  );
};

export default PieChartSurveyInfo;
