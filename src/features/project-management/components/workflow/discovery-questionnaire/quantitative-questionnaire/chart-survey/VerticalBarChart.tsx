import BarChartOne from '@/shared/components/charts/bar/BarChartOne';
import { GLOBAL_COLOR } from '@/shared/constants/color';
import { useMemo } from 'react';

type DataChart = {
  title: string;
  percentage: number;
  aspect?: string;
};

type VerticalBarChartType = {
  title: string;
  legendLabel: string[] | undefined;
  data: DataChart[];
};
const VerticalBarChartInfo: React.FC<VerticalBarChartType> = ({ title, legendLabel, data }) => {
  const categories = useMemo(
    () => data.map(d => d.title),
    [data],
  );

  const series = useMemo(() => [
    {
      name: '',
      data: data.map(d => d.percentage),
    },
  ], [data]);

  const initDataMap = useMemo(() => {
    const dataMap: Map<string, string> = new Map();
    const labelMap: Map<string, string> = new Map();
    let i = 0;
    legendLabel?.forEach((label) => {
      if (!dataMap.has(label)) {
        dataMap.set(label, GLOBAL_COLOR[i] ?? '#0062FF');
        labelMap.set(GLOBAL_COLOR[i] ?? '#0062FF', label);
        i++;
      }
    });

    return { colorsMap: dataMap, labelMap };
  }, [legendLabel]);

  const colors = useMemo(() => {
    const { colorsMap } = initDataMap;
    if (!colorsMap.size) {
      return [];
    }
    return data.reduce((init, d) => {
      init.push(colorsMap.get(d?.aspect ?? '') ?? '');
      return init;
    }, [] as string[]);
  }, [data, initDataMap]);

  return (
    <>
      <p className="text-gray-500 mb-1">
        {title}
        :
      </p>
      <div className="flex justify-center items-center min-h-[500px]">
        <BarChartOne
          colors={colors}
          categories={categories}
          series={series}
          height={450}
          labelMap={initDataMap.labelMap}
        />
      </div>
      <div className="flex flex-wrap gap-3 justify-center">
        {
          (legendLabel ?? []).map((label, index) => (
            <div className="flex items-center gap-1" key={index}>
              <div style={{ background: GLOBAL_COLOR[index] ?? '#000' }} className="h-5 w-5 rounded-lg"></div>
              <div>{label}</div>
            </div>
          ))
        }
      </div>
    </>
  );
};

export default VerticalBarChartInfo;
