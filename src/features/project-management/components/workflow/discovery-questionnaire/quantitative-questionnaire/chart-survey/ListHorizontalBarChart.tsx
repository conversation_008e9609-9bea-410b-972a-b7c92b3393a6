import type { SubQuestionsStatisticType } from '@/features/project-management/types/workflow';
import HorizontalBarChart from '@/shared/components/charts/bar/HorizontalBarChart';
import { GLOBAL_COLOR } from '@/shared/constants/color';

type DataSubQuestions = {
  title: string;
  percentage: number;
};

type ListHorizontalBarChartSurveyType = {
  title: string;
  subQuestions: SubQuestionsStatisticType[] | any;
};
const ListHorizontalBarChartInfo: React.FC<ListHorizontalBarChartSurveyType> = ({ title, subQuestions }) => {
  const series = (data: DataSubQuestions[]) => {
    return [{
      name: 'Percentage',
      data: (data ?? []).map(d => d.percentage),
    }];
  };

  const categories = (data: DataSubQuestions[]) => (data ?? []).map(d => d.title);

  return (
    <>
      <p className="text-gray-500 mb-1">
        {title}
        :
      </p>
      {
        (subQuestions ?? []).map((sub: any, index: number) => (
          <div key={index} className="flex flex-col justify-center items-center mt-2">
            <p className="m-0 p-0">{sub.title}</p>
            <HorizontalBarChart
              series={series(sub.statitics)}
              categories={categories(sub.statitics)}
              colors={[GLOBAL_COLOR[index] ?? '#0062FF']}
              height={240}
              showDataLabels={true}
            />
          </div>

        ))
      }

    </>
  );
};

export default ListHorizontalBarChartInfo;
