'use client';
import type { TypeStepInfosRes } from '@/features/project-management/types/evaluation';
import QuantitativeAnalysis from './QuantitativeAnalysis';
import ChartSurveyWrapper from './chart-survey/ChartSurveyWrapper';

type QuantitativeSelectionType = 'questionnaire' | 'summary' | 'analysis';

type QuantitativeQuestionnaireWrapperType = {
  isLoading: boolean;
  isAnalysis: boolean;
  markdown: string;
  type: QuantitativeSelectionType;
  stepInfos: TypeStepInfosRes[];
  idQuestionnaire: string;
  stepId: string;
  saveStepInfos: (stepInfos: TypeStepInfosRes[]) => void;
  setIsLoading: (status: boolean) => void;
  setIsAnalysis: (status: boolean) => void;
};

const QuantitativeQuestionnaireWrapper: React.FC<QuantitativeQuestionnaireWrapperType> = ({
  type,
  markdown,
  isLoading,
  isAnalysis,
  stepInfos,
  idQuestionnaire,
  stepId,
  setIsLoading,
  setIsAnalysis,
  saveStepInfos,
}) => {
  return (
    <div className="w-full relative">

      { type === 'summary' && <ChartSurveyWrapper setIsLoading={() => {}} idQuestionnaire={idQuestionnaire} />}
      { type === 'analysis'
        && (
          <QuantitativeAnalysis
            stepId={stepId}
            markdown={markdown}
            isLoading={isLoading}
            isAnalysis={isAnalysis}
            stepInfos={stepInfos}
            idQuestionnaire={idQuestionnaire}
            setIsLoading={setIsLoading}
            setIsAnalysis={setIsAnalysis}
            saveStepInfos={saveStepInfos}
          />
        )}
    </div>

  );
};

export default QuantitativeQuestionnaireWrapper;
