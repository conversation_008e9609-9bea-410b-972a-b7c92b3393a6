/* eslint-disable react-hooks-extra/no-direct-set-state-in-use-effect */
import { EOptionSelect } from '@/features/project-management/types/questionnaire';
import type { QuestionnaireType, toggleOptionsType } from '@/features/project-management/types/questionnaire';
import { But<PERSON> } from '@/shared/components/ui/button';
import { useEffect, useState } from 'react';
import QuantitativeQuestionnaireWrapper from './quantitative-questionnaire/QuantiativeQuestionnaireWrapper';
import { useCurrentStep, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { discoveryQuestionnaire, documentFileUpload } from '@/features/project-management/types/project';
import { DocumentIcon, PieChartIcon } from '@/shared/icons';
import QualitativeQuestionnaireWrapper from './qualitative-questionnaire/QualitativeQuestionnaireWrapper';
import type { TypeStepInfosRes } from '@/features/project-management/types/evaluation';
import type { IFileResponse } from '@/shared/types/global';
import { Tooltip } from '@/shared/components/ui/tooltip';

type QuantitativeSelectionType = 'summary' | 'analysis';

const TooltipShowInValid = () => {
  return (
    <div className="w-full">
      Please save the data analysis before proceeding.
    </div>
  );
};

const ReportWrapper: React.FC = () => {
  const toggleOptions = [
    { id: 'quantitative' as QuestionnaireType, label: 'Quantitative Questionnaire', type: EOptionSelect.FORM },
    { id: 'qualitative' as QuestionnaireType, label: 'Qualitative Questionnaire', type: EOptionSelect.MARKDOWN },
  ];
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [isLoadingQuality, setIsLoadingQuality] = useState<boolean>(true);

  const [isAnalysis, setIsAnalysis] = useState<boolean>(false);

  const [isAnalysisQuality, setIsAnalysisQuality] = useState<boolean>(false);

  const [selectedType, setSelectedType] = useState<QuestionnaireType>('quantitative');

  const [optionList, setOptionList] = useState<toggleOptionsType[]>(toggleOptions);

  const [selectedTypeQuantity, setSelectedTypeQuantity] = useState<QuantitativeSelectionType>('summary');

  const [markdown, setMarkdown] = useState<string>('');

  const [markdownQuality, setMarkdownQuality] = useState<string>('');

  const [stepInfos, setStepInfos] = useState<TypeStepInfosRes[]>([]);

  const [files, setFiles] = useState<IFileResponse[]>([]);

  const [isValid, setIsValid] = useState<boolean>(false);

  const [idQuestionnaire, setIdQuestionnaire] = useState<string>('');

  const currentStep = useCurrentStep();

  const workflow = useWorkflowTasks();

  const currentStepId = currentStep?.id;

  const idStepUpload = workflow[4]?.children[0]?.id;

  const { data: discoveryQuestionnaire } = useGetInfoDetail<any, discoveryQuestionnaire>(currentStepId ?? '');

  const { data: responseDataUpload } = useGetInfoDetail<documentFileUpload, any>(idStepUpload ?? '');

  useEffect(() => {
    if (responseDataUpload && responseDataUpload.stepInfo.length) {
      const documentFiles = responseDataUpload?.stepInfo[0]?.infos[0];
      const typeSelected = documentFiles?.typeSelected ?? [];

      const listFilter = toggleOptions.filter(o => typeSelected.includes(o.type));
      setOptionList(listFilter);

      setSelectedType(listFilter[0]?.id ?? 'quantitative');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [responseDataUpload]);

  useEffect(() => {
    if (discoveryQuestionnaire && discoveryQuestionnaire.stepInfo.length) {
      setIsLoading(false);

      setIsLoadingQuality(false);

      const findMarkdownData = (order: number) => {
        return discoveryQuestionnaire.stepInfo.find(t => t.order === order);
      };
      if (findMarkdownData(0)) {
        const markdown = findMarkdownData(0)?.infos[0]?.value;
        setIsAnalysis(true);
        setMarkdown(markdown);
      }

      if (findMarkdownData(1)) {
        const infos = findMarkdownData(1)?.infos[0];
        const markdown = infos?.value;
        const files = infos?.files;

        setFiles(files);
        setIsAnalysisQuality(true);
        setMarkdownQuality(markdown);
      }

      setStepInfos(discoveryQuestionnaire.stepInfo);
    } else {
      if (discoveryQuestionnaire?.stepInfoPrevious.length) {
        setIsLoading(false);

        setIsLoadingQuality(false);
      }
    }
    if (discoveryQuestionnaire?.stepInfoPrevious.length) {
      const infosForm = discoveryQuestionnaire?.stepInfoPrevious?.find(step => step.order === 1);
      if (infosForm) {
        const form = infosForm.infos[0]?.form;
        setIdQuestionnaire(form?.id);
      }
    }
  }, [discoveryQuestionnaire]);

  useEffect(() => {
    if (stepInfos.length) {
      const isSaved = stepInfos.every(i => i.infos[0]?.isSaved);
      setIsValid(isSaved && optionList.length === stepInfos.length);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [stepInfos]);

  const handleSelectType = (option: toggleOptionsType) => {
    setSelectedType(option.id);
  };

  const onSaveAndNextStep = () => {};
  return (
    <div className="relative p-4  md:p-6 pb-0 md:pb-0">
      {/* Toggle Button */}
      <div className="flex items-center justify-between sticky top-0 py-2 right-4 z-100 bg-white">
        <div className="flex p-1 bg-gray-100 rounded-lg w-fit">
          {optionList.map(option => (
            <button
              key={option.id}
              type="button"
              onClick={() => handleSelectType(option)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${selectedType === option.id
                ? 'bg-white text-black shadow-sm'
                : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>

        {isValid
          ? (
              <Button type="button" onClick={onSaveAndNextStep}>
                Save & Next
              </Button>
            )
          : (
              <Tooltip position="left" content={<TooltipShowInValid />}>
                <Button disabled type="button">
                  Save & Next
                </Button>
              </Tooltip>
            )}

      </div>

      {selectedType === 'quantitative'
        ? (
            <>
              <div className="flex items-center w-full  sticky top-14 bg-white z-100">
                <div onClick={() => setSelectedTypeQuantity('summary')} className={`flex-1 cursor-pointer flex items-center justify-center py-4 gap-1.5 border-b ${selectedTypeQuantity === 'summary' ? 'border-gray-500' : 'border-gray-100'}`}>
                  <PieChartIcon className="h-5 w-5 " />
                  Summary
                </div>

                <div onClick={() => setSelectedTypeQuantity('analysis')} className={`flex-1 cursor-pointer flex items-center justify-center py-4 gap-1.5 border-b ${selectedTypeQuantity === 'analysis' ? 'border-gray-500' : 'border-gray-100'}`}>
                  <DocumentIcon className="h-5 w-5 " />
                  Analysis
                </div>
              </div>
              <div className="w-full relative">
                <QuantitativeQuestionnaireWrapper
                  stepId=""
                  stepInfos={stepInfos}
                  type={selectedTypeQuantity}
                  markdown={markdown}
                  isLoading={isLoading}
                  isAnalysis={isAnalysis}
                  idQuestionnaire={idQuestionnaire}
                  setIsLoading={setIsLoading}
                  setIsAnalysis={setIsAnalysis}
                  saveStepInfos={setStepInfos}
                />
              </div>
            </>
          )
        : (
            <div className="relative">
              <QualitativeQuestionnaireWrapper
                stepId=""
                stepInfos={stepInfos}
                markdown={markdownQuality}
                fileResponse={files}
                isAnalysis={isAnalysisQuality}
                loading={isLoadingQuality}
                onLoading={setIsLoadingQuality}
                setIsAnalysis={setIsAnalysisQuality}
                saveStepInfos={setStepInfos}
              />
            </div>
          )}

    </div>
  );
};

export default ReportWrapper;
