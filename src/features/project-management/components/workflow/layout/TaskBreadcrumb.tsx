import { ChevronLeftIcon, ChevronRightIcon, HomeIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React from 'react';
import { Button } from '@/shared/components/ui/button';
import { useChatBoxToggle, useChatBoxVisible, useShowChatBox } from '@/features/project-management/stores/chatbox-store';
import { useProjectName } from '@/features/project-management/stores/project-workflow-store';

const TaskBreadcrumb: React.FC = (

) => {
  // Use translations
  const t = useTranslations('TaskWorkflow');
  const isVisible = useChatBoxVisible();
  const isShowChatBox = useShowChatBox();
  const toggle = useChatBoxToggle();

  const name = useProjectName();

  return (
    <div className="flex flex-wrap items-center justify-between gap-3 mb-0 p-4">
      <div className="flex items-center gap-2">
        {/* MVV Home link with arrow */}
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <Link href="/dashboard/projects" className="flex items-center hover:text-gray-900 dark:hover:text-white transition-colors">
            <HomeIcon className="h-4 w-4 mr-1" />
            <span>{t('mvv_home')}</span>
          </Link>
          <ChevronRightIcon className="h-4 w-4 mx-2" />
          <span className="text-gray-800 dark:text-gray-300">{name}</span>
        </div>

      </div>

      {/* Show toggle button in breadcrumb when chat is not visible */}
      {isShowChatBox && (!isVisible && (
        <Button
          onClick={toggle}
          variant="secondary"
          size="sm"
          className="flex items-center gap-1 animate-slide-left"
        >
          <ChevronLeftIcon className="h-4 w-4" />
          {t('chat_ai')}
        </Button>
      ))}
    </div>
  );
};

export default TaskBreadcrumb;
