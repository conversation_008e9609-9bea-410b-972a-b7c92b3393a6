'use client';

import { Skeleton } from '@/shared/components/ui/skeleton';
import React from 'react';

/**
 * Skeleton component for the workflow when it's loading
 * Displays a loading skeleton that mimics the structure of the workflow
 */
const WorkflowSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-12 h-full border-t border-border">
      {/* Right Sidebar with task list skeleton */}
      <div className="col-span-1 md:col-span-3">
        <div className="h-full bg-card border-r border-border">
          <div className="w-full h-full">
            <div className="w-full">
              <div className="divide-y divide-border">
                {/* Task items skeletons */}
                {[...Array.from({ length: 4 })].map((_, index) => (
                  <div key={index} className="px-5 py-4">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex-1 mr-2">
                        <Skeleton className="h-5 w-3/4 mb-2" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                      <div className="flex-shrink-0">
                        <Skeleton className="h-6 w-6 rounded-full" />
                      </div>
                    </div>

                    {/* Step skeletons - only show for first task */}
                    {index === 0 && (
                      <div className="pl-4 mt-2 space-y-2">
                        {[...Array.from({ length: 3 })].map((_, stepIndex) => (
                          <div key={stepIndex} className="flex items-center">
                            <Skeleton className="h-3 w-3 rounded-full mr-2" />
                            <Skeleton className="h-4 w-2/3" />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area skeleton */}
      <div className="col-span-1 p-4 md:p-6 overflow-y-auto lg:col-span-6">
        <Skeleton className="h-8 w-2/3 mb-6" />

        {/* Form skeleton */}
        <div className="space-y-6">
          {[...Array.from({ length: 5 })].map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="h-4 w-1/4" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}

          {/* Buttons skeleton */}
          <div className="pt-6 flex justify-end space-x-4">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </div>

      {/* Left sidebar skeleton */}
      <div className="col-span-1 overflow-y-auto lg:col-span-3">
        <div className="h-full bg-card border-l border-border">
          <div className="p-4">
            <Skeleton className="h-6 w-1/2 mb-4" />
            <div className="space-y-4">
              {[...Array.from({ length: 3 })].map((_, index) => (
                <div key={index} className="flex">
                  <Skeleton className="h-10 w-10 rounded-full mr-3" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-16 w-full rounded-md" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowSkeleton;
