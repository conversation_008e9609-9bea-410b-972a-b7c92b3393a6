'use client';

import type {
  WorkflowStep,

  // Task
} from '../../../types/workflow';
// import type { Step as WorkflowTask } from '../../../types/step';
import { useThemeStyles } from '@/shared/hooks/useThemeStyles';
import { cn } from '@/shared/utils/utils';
import React, { useState } from 'react';
import { toast } from 'sonner';

import { EStatusTask } from '../../../types/workflow';
import TaskStatusIndicator from './TaskStatusIndicator';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowStore, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { saveCurrentStepInLocalStorage } from '@/features/project-management/utils/workflowUtils';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useLocale, useTranslations } from 'next-intl';

/**
 * TaskList Component
 *
 * This component displays a list of tasks and their steps.
 * It uses the theme system for consistent styling.
 */
const TaskList: React.FC = () => {
  const locale = useLocale();

  const t = useTranslations('workflow');
  // Use the theme styles hook
  const { classesObj } = useThemeStyles();

  const workflow = useWorkflowTasks();
  const currentTask = useCurrentTask();
  const currentStep = useCurrentStep();

  const workflowStore = useWorkflowStore();

  const {
    updateStatus,
  } = useWorkflowActions();

  const { checkStepDirty, clearStep } = useDirty();

  const [showDialog, setShowDialog] = useState<boolean>(false);

  const [isTaskSelected, setIsTaskSelected] = useState<boolean>(false);

  const [parentSelected, setParentSelected] = useState<WorkflowStep | null>(null);

  const [itemSelected, setItemSelected] = useState<WorkflowStep | null> (null);

  const titlePopup = t('common.titleConfirmChange');

  const descriptionPopUp = t('common.messageLeave');

  if (!workflow) {
    return [];
  }

  const handleCheckAllPreviousTaskIsFinish = (item: WorkflowStep): boolean => {
    const index = workflow.findIndex(i => item.id === i.id);
    if (index === -1) {
      return false;
    }
    const previousTasks = workflow.slice(0, index);
    return previousTasks.every(task => task.status === EStatusTask.COMPLETED);
  };
  // Handle task selection

  const nextTaskSelect = (item: WorkflowStep) => {
    if (item.children.length > 0) {
      const firstStep = item.children[0];
      updateStatus(item.id, item.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : EStatusTask.COMPLETED, true);
      if (firstStep) {
        updateStatus(firstStep.id, firstStep.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : EStatusTask.COMPLETED);
        saveCurrentStepInLocalStorage(item.id, firstStep?.id ?? '');
        workflowStore?.setState({
          currentTask: item,
          currentStep: firstStep,
        });
      } else {
        // Fallback to the task itself if the first step is undefined
        workflowStore?.setState({
          currentTask: item,
          currentStep: null,
        });
      }
    } else {
      // If the task has no steps, select the task itself
      // Do nothing as per task-workflow implementation
    }
  };

  const handleTaskSelect = (item: WorkflowStep) => {
    // If the task has steps, select the first step

    if (item.id === currentTask?.id) {
      return;
    }

    const valid = handleCheckAllPreviousTaskIsFinish(item);

    if (!valid) {
      toast.error(t('common.errorValidStep'));
      return;
    }
    setItemSelected(item);
    setIsTaskSelected(true);
    const isDirty = checkStepDirty(currentStep?.id ?? '');

    if (isDirty) {
      setShowDialog(true);
      return;
    }
    nextTaskSelect(item);
  };

  const nextStepSelect = (item: WorkflowStep, parentTask: WorkflowStep) => {
    const valid = handleCheckAllPreviousTaskIsFinish(parentTask);
    // Get the index of the current step in the parent task's steps array
    const stepIndex = parentTask.children.findIndex(step => step.id === item.id);

    const updateStatusStep = () => {
      updateStatus(parentTask.id, parentTask.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : EStatusTask.COMPLETED, true);
      updateStatus(item.id, item.status !== EStatusTask.COMPLETED ? EStatusTask.IN_PROGRESS : EStatusTask.COMPLETED);
    };

    // If it's the first step (index 0), allow selection regardless of status
    if (stepIndex === 0 && valid) {
      saveCurrentStepInLocalStorage(parentTask.id, item?.id ?? '');

      updateStatusStep();
      workflowStore?.setState({
        currentTask: parentTask,
        currentStep: item,
      });
      return;
    }

    // For subsequent steps, check if the previous step is completed
    const previousStep = parentTask.children[stepIndex - 1];

    if (previousStep && previousStep.status === EStatusTask.COMPLETED && valid) {
      updateStatusStep();

      // Previous step is completed, allow selection of this step
      saveCurrentStepInLocalStorage(parentTask.id, item?.id ?? '');
      workflowStore?.setState({
        currentTask: parentTask,
        currentStep: item,
      });
    } else {
      toast.error(t('common.errorValidStep'));
    }
  };

  // Handle step selection
  const handleStepSelect = (item: WorkflowStep) => {
    // Find the task that contains this step
    const parentTask = workflow.find(task =>
      task.children.some(step => step.id === item.id),
    );

    if (!parentTask) {
      console.error('Parent task not found for step:', item);
      toast.error(t('common.taskNotFound'));
      return;
    }

    setItemSelected(item);
    setParentSelected(parentTask);
    const isDirty = checkStepDirty(currentStep?.id ?? '');

    if (isDirty) {
      setShowDialog(true);
      return;
    }
    nextStepSelect(item, parentTask);
  };

  const resetDataState = () => {
    setShowDialog(false);
    setItemSelected(null);
    setParentSelected(null);
    setIsTaskSelected(false);
  };

  const handleConfirmPopUp = () => {
    if (itemSelected) {
      if (isTaskSelected) {
        nextTaskSelect(itemSelected);
      } else {
        nextStepSelect(itemSelected, parentSelected!);
      }
      resetDataState();
      clearStep(itemSelected.id);
    }
  };

  const handleCancelPopUp = () => {
    resetDataState();
  };

  // Helper function to check if an item is selected
  const isSelected = (item: WorkflowStep) => {
    if (!currentTask) {
      return false;
    }

    if ('steps' in item) {
      // Item is a task
      return currentTask.id === item.id;
    } else {
      // Item is a step
      return currentStep?.id === (item as WorkflowStep).id;
    }
  };

  // Theme-aware classes
  const containerClasses = classesObj(
    'divide-y',
    {
      light: 'divide-border',
      dark: 'divide-border',
    },
  );

  const getItemClasses = (item: WorkflowStep) => {
    return cn(
      'cursor-pointer',
      classesObj(
        '',
        {
          light: 'hover:bg-[#F5F5F5]',
          dark: 'hover:bg-[#F5F5F5]',
        },
      ),
      isSelected(item) && classesObj(
        '',
        {
          light: 'hover:bg-[#F5F5F5] bg-[#F5F5F5]',
          dark: 'hover:bg-[#F5F5F5] bg-[#F5F5F5]',
        },
      ),
    );
  };

  const getItemChildrenClasses = (item: WorkflowStep) => {
    return cn(
      'cursor-pointer',
      classesObj(
        '',
        {
          light: 'hover:bg-[#FFF8E1]',
          dark: 'hover:bg-[#FFF8E1]',
        },
      ),
      isSelected(item) && classesObj(
        '',
        {
          light: 'hover:bg-[#FFF8E1] bg-[#FFF8E1]',
          dark: 'hover:bg-[#FFF8E1] bg-[#FFF8E1]',
        },
      ),
    );
  };

  const stepContentClasses = classesObj(
    'px-5 py-4 text-start pl-10',
    {
      light: 'text-muted-foreground',
      dark: 'text-muted-foreground',
    },
  );

  return (
    <div className={containerClasses}>
      {workflow.map(task => (
        <React.Fragment key={task.id}>
          <div
            onClick={() => handleTaskSelect(task)}
            className={getItemClasses(task)}
          >
            <div className="px-5 py-4 text-start font-medium text-foreground dark:text-[#425986] ">
              <div className="flex items-center justify-between w-full">
                <div
                  className="truncate mr-2"
                  title={`${task.name[locale]}`}
                >
                  {task.order + 1}
                  .
                  {' '}
                  {task.name[locale]}
                </div>
                <div className="flex-shrink-0">
                  <TaskStatusIndicator status={task.status} showText={false} />
                </div>
              </div>
            </div>
          </div>

          {/* Render steps */}
          {task.children.map(step => (
            <div
              key={step.id}
              onClick={() => handleStepSelect(step)}
              className={getItemChildrenClasses(step)}
            >
              <div className={stepContentClasses}>
                <div className="flex items-center justify-between w-full">
                  <div
                    className="truncate mr-2"
                    title={`${step.name[locale]}`}
                  >
                    {`${task.order + 1}.${step.order + 1}. ${step.name[locale]}`}
                  </div>

                  <div className="flex-shrink-0">
                    <TaskStatusIndicator status={step.status} showText={false} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </React.Fragment>
      ))}

      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.leaveAnyway')}
        cancelText={t('common.stay')}
      />
    </div>
  );
};

export default TaskList;
