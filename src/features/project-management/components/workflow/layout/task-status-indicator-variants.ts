import { cva } from 'class-variance-authority';
import { EStatusTask } from '@/features/project-management/types/workflow';

/**
 * Task Status Indicator Dot Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The task status indicator dot variants are defined using the cva utility for consistent styling.
 */
export const taskStatusIndicatorDotVariants = cva(
  'w-3 h-3 rounded-full',
  {
    variants: {
      status: {
        [EStatusTask.COMPLETED]: 'bg-success-500',
        [EStatusTask.IN_PROGRESS]: 'bg-info-500',
        [EStatusTask.PENDING]: 'bg-muted',
        [EStatusTask.FAILED]: 'bg-destructive-500',
      },
    },
    defaultVariants: {
      status: EStatusTask.PENDING,
    },
  },
);

/**
 * Task Status Indicator Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The task status indicator text variants are defined using the cva utility for consistent styling.
 */
export const taskStatusIndicatorTextVariants = cva(
  'text-xs font-medium',
  {
    variants: {
      status: {
        [EStatusTask.COMPLETED]: 'text-success dark:text-success',
        [EStatusTask.IN_PROGRESS]: 'text-info dark:text-info',
        [EStatusTask.PENDING]: 'text-muted-foreground dark:text-muted-foreground',
        [EStatusTask.FAILED]: 'text-destructive dark:text-destructive',
      },
    },
    defaultVariants: {
      status: EStatusTask.PENDING,
    },
  },
);
