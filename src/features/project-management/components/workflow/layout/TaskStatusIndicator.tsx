'use client';

import React from 'react';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { cn } from '@/shared/utils/utils';
import {
  taskStatusIndicatorDotVariants,
  taskStatusIndicatorTextVariants,
} from './task-status-indicator-variants';

type TaskStatusIndicatorProps = {
  status: EStatusTask;
  showText?: boolean;
};

/**
 * TaskStatusIndicator Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a visual indicator of a task's status with an optional text label.
 */
const TaskStatusIndicator: React.FC<TaskStatusIndicatorProps> = ({
  status,
  showText = false,
}) => {
  const getTooltipText = () => {
    switch (status) {
      case EStatusTask.COMPLETED:
        return 'Completed';
      case EStatusTask.IN_PROGRESS:
        return 'In Progress';
      case EStatusTask.FAILED:
        return 'Failed';
      default:
        return 'Pending';
    }
  };

  const tooltipText = getTooltipText();

  return (
    <div className="flex items-center gap-2" title={tooltipText}>
      <div className={cn(taskStatusIndicatorDotVariants({ status }))}></div>
      {showText && (
        <span className={cn(taskStatusIndicatorTextVariants({ status }))}>
          {tooltipText}
          {' '}
          {status}
        </span>
      )}
    </div>
  );
};

export default TaskStatusIndicator;
