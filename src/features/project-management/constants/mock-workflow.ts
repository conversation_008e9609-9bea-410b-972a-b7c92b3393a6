// import type { Step, Workflow } from '../types/workflow';
// import { EOrderStep } from '../types/step';
// import { EStatusTask, ETypeStep } from '../types/workflow';

// export const mockWorkflow: Workflow = {
//   id: 'wf-001',
//   name: 'Client Onboarding Workflow',
//   description: 'Process for evaluating and onboarding new clients',
//   tasks: [
//     {
//       id: '1',
//       name: 'Initial Client Screening & Scoring',
//       description: 'Evaluate and score potential clients based on predefined criteria',
//       status: EStatusTask.PENDING,
//       position: 0,
//       steps: [
//         {
//           id: '1.1',
//           name: 'Initial Screening Form',
//           description: 'Initial Screening Form',
//           status: EStatusTask.PENDING,
//           type: ETypeStep.FORM,
//           position: 0,
//         },
//         {
//           id: '1.2',
//           name: 'Score calculation',
//           description: 'Score calculation',
//           status: EStatusTask.PENDING,
//           type: ETypeStep.EVALUATION,
//           position: 1,
//         },
//         {
//           id: '1.3',
//           name: 'Screening Outcome',
//           description: 'Screening Outcome',
//           status: EStatusTask.PENDING,
//           type: ETypeStep.OUTCOME,
//           position: 2,
//         },
//       ],
//     },
//     {
//       id: '2',
//       name: 'Automated Client Brief/RFP Analysis',
//       description: 'Analyze client briefs and RFPs using automated tools',
//       status: EStatusTask.PENDING,
//       position: 1,
//       steps: [
//         // {
//         //   id: '2.1',
//         //   name: 'Document Upload',
//         //   description: 'Upload client brief or RFP documents',
//         //   status: EStatusTask.PENDING,
//         //   type: ETypeStep.FORM,
//         //   position: 0,
//         // },
//         // {
//         //   id: '2.2',
//         //   name: 'Document Analysis',
//         //   description: 'Automated analysis of uploaded documents',
//         //   status: EStatusTask.PENDING,
//         //   type: ETypeStep.ANALYSIS,
//         //   position: 1,
//         // },
//       ],
//     },
//     // Additional tasks would be defined here
//   ],
// };

// const mockInitialScreeningAndScoreSteps: Step[] = [
//   {
//     id: '1.1',
//     name: 'Initial Screening Form',
//     description: 'Initial Screening Form',
//     status: EStatusTask.PENDING,
//     type: ETypeStep.FORM,
//     position: 0,
//   },
//   {
//     id: '1.2',
//     name: 'Score calculation',
//     description: 'Score calculation',
//     status: EStatusTask.PENDING,
//     type: ETypeStep.EVALUATION,
//     position: 1,
//   },
//   {
//     id: '1.3',
//     name: 'Screening Outcome',
//     description: 'Screening Outcome',
//     status: EStatusTask.PENDING,
//     type: ETypeStep.OUTCOME,
//     position: 2,
//   },
// ];

// export const GET_OPTION_SUB_STEPS_MAP: { [key: number]: Step[] } = {
//   [EOrderStep.INITIAL_SCREENING]: mockInitialScreeningAndScoreSteps,
// };

export const mockQuestionAnswer = [
  {
    question: 'What type of business entity is the client, and is their industry aligned with our Ideal Customer Profile?',
    answer: 'The client is a joint stock company operating in the technology sector.',
  },
  {
    question: 'What is the client’s estimated revenue and annual marketing budget?',
    answer: '500k$ and right now no budget for marketing...',
  },
  {
    question: 'Has the client ever worked with our company? If so, how frequently and how would you rate the quality of that cooperation?',
    answer: 'We’ve run three distinct campaigns over the past 18 months. The initial onboarding process took longer than expected.',
  },
  {
    question: 'Is the client easy to reach and quick to make decisions?',
    answer: 'Direct emails usually receive very slow responses. Strategy approvals typically take around 5 business days.',
  },
  {
    question: 'How effectively does the client collaborate during the project/work process?',
    answer: 'They share all necessary data upfront and actively participate in weekly check-ins, but they do not consistently provide clear feedback.',
  },
  {
    question: 'Notes',
    answer: 'The company demonstrates a clear long-term vision, has a sustainable development orientation and wishes to expand multi-service cooperation such as brand consulting, digital communications and internal training. In addition, the client has a wide network of relationships in the industry, potentially creating opportunities for introduction or cross-cooperation with other strategic partners.',
  },
];
