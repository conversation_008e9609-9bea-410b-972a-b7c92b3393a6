export type IOptionsItem = {
  value: string;
  label: string;
};

export const businessTypeOptions = [
  { value: 'sme', label: 'SME (small, individual)' },
  { value: 'spc', label: 'Small private company' },
  { value: 'domestic_corporation', label: 'Domestic corporation' },
  { value: 'mc', label: 'Multinational corporation' },
  { value: 'plc', label: 'Publicly listed corporation' },
];

export const industryAndICPFitOptions = [
  { value: 'oicp', label: 'Outside ICP' },
  { value: 'lr', label: 'Loosely related' },
  { value: 'wbds', label: 'Within ICP but different segment' },
  { value: 'rcs', label: 'Right ICP, correct segment' },
  { value: 'rsf', label: 'Right ICP, strategic focus' },
];

export const revenueScaleOptions = [
  {
    value: '1_000_billion_vnd',
    label: '<1,000 billion VND',
  },
  {
    value: '1_000_2_000_billion_vnd',
    label: '1,000-2,000 billion VND',
  },
  {
    value: '2_000_4_000_billion_vnd',
    label: '2,000-4,000 billion VND',
  },
  {
    value: '4_000_6_000_billion_vnd',
    label: '4,000-6,000 billion VND',
  },
  {
    value: '6_000_billion_vnd',
    label: '>6,000 billion VND',
  },
];

export const annualMarketingBudgetOptions = [
  {
    value: '20_billion_vnd',
    label: '<20 billion VND',
  },
  {
    value: '20_50_billion_vnd',
    label: '20-50 billion VND',
  },
  {
    value: '50_100_billion_vnd',
    label: '50-100 billion VND',
  },
  {
    value: '100_200_billion_vnd',
    label: '100-200 billion VND',
  },
  {
    value: '200_billion_vnd',
    label: '>200 billion VND',
  },
];
export const contractTypeOptions = [
  {
    value: 'project_based_pitch',
    label: 'Project-based pitch',
  },
  {
    value: 'short_term_contract',
    label: 'Short-term contract',
  },
  {
    value: 'long_term_contract_6_12_months',
    label: 'Long-term contract (6-12 months)',
  },
  {
    value: 'agency_of_record_aor',
    label: 'Agency of Record (AOR)',
  },
  {
    value: 'long_term_exclusive',
    label: 'Long-term exclusive',
  },
];
export const paymentHistoryOptions = [
  {
    value: 'often_30_days_late',
    label: 'Often >30 days late',
  },
  {
    value: '15_30_days_late',
    label: '15-30 days late',
  },
  {
    value: 'occasionally_late_pays_within_15_days',
    label: 'Occasionally late, pays within 15 days',
  },
  {
    value: 'always_on_time',
    label: 'Always on time',
  },
  {
    value: 'on_time_willing_to_pay_in_advance',
    label: 'On time, willing to pay in advance',
  },
];
export const workingHistoryWithMVVGroupOptions = [
  {
    value: 'conflicts_occurred',
    label: 'Conflicts occurred',
  },
  {
    value: 'poor_collaboration',
    label: 'Poor collaboration',
  },
  {
    value: 'average_collaboration_or_no_prior_cooperation',
    label: 'Average collaboration or no prior cooperation',
  },
  {
    value: 'good_collaboration',
    label: 'Good collaboration',
  },
  {
    value: 'excellent_collaboration_many_successful_projects',
    label: 'Excellent collaboration, many successful projects',
  },
];
export const projectFrequencyOptions = [
  {
    value: '1_project_year',
    label: '<1 project/year',
  },
  {
    value: '1_2_projects_year',
    label: '1-2 projects/year',
  },
  {
    value: '3_5_projects_year',
    label: '3-5 projects/year',
  },
  {
    value: '6_10_projects_year',
    label: '6-10 projects/year',
  },
  {
    value: '10_projects_year',
    label: '>10 projects/year',
  },
];
export const accessToDecisionMakersOptions = [
  {
    value: 'no_access',
    label: 'No access',
  },
  {
    value: 'very_difficult_access',
    label: 'Very difficult access',
  },
  {
    value: 'occasionally_via_intermediaries',
    label: 'Occasionally via intermediaries',
  },
  {
    value: 'easily_accessible_when_needed',
    label: 'Easily accessible when needed',
  },
  {
    value: 'direct_and_frequent_access',
    label: 'Direct and frequent access',
  },
];
export const decisionMakingSpeedOptions = [
  {
    value: 'very_slow_2_months',
    label: 'Very slow (>2 months)',
  },
  {
    value: 'slow_1_2_months',
    label: 'Slow (1-2 months)',
  },
  {
    value: 'average_2_4_weeks',
    label: 'Average (2-4 weeks)',
  },
  {
    value: 'fast_1_2_weeks',
    label: 'Fast (1-2 weeks)',
  },
  {
    value: 'very_fast_1_week',
    label: 'Very fast (<1 week)',
  },
];
export const complexityOfDecisionProcessOptions = [
  {
    value: '12_months_many_layers_of_approval',
    label: '>12 months, many layers of approval',
  },
  {
    value: '9_12_months',
    label: '9-12 months',
  },
  {
    value: '6_9_months',
    label: '6-9 months',
  },
  {
    value: '3_6_months',
    label: '3-6 months',
  },
  {
    value: '3_months_one_approval_level',
    label: '<3 months, one approval level',
  },
];
export const marketingTeamStrengthOptions = [
  {
    value: 'no_team',
    label: 'No team',
  },
  {
    value: 'weak_team',
    label: 'Weak team',
  },
  {
    value: 'basic_team',
    label: 'Basic team',
  },
  {
    value: 'good_team_but_lacks_strategy',
    label: 'Good team but lacks strategy',
  },
  {
    value: 'strong_team_ready_to_execute_strategy',
    label: 'Strong team, ready to execute strategy',
  },
];
export const willingnessToShareInformationOptions = [
  {
    value: 'does_not_share',
    label: 'Does not share',
  },
  {
    value: 'limited_sharing',
    label: 'Limited sharing',
  },
  {
    value: 'basic_sharing_campaign_reports',
    label: 'Basic sharing (campaign reports)',
  },
  {
    value: 'good_sharing_dashboards_kpis',
    label: 'Good sharing (dashboards, KPIs)',
  },
  {
    value: 'excellent_sharing_real_time_tracking_crm',
    label: 'Excellent sharing (real-time tracking, CRM)',
  },
];

export const proActivenessInProjectCollaborationOptions = [
  {
    value: 'not_proactive_causes_delays',
    label: 'Not proactive, causes delays',
  },
  {
    value: 'slow_responses',
    label: 'Slow responses',
  },
  {
    value: 'on_time_delivery',
    label: 'On-time delivery',
  },
  {
    value: 'proactively_supportive',
    label: 'Proactively supportive',
  },
  {
    value: 'very_proactive_quick_responses_excellent_support',
    label: 'Very proactive, quick responses, excellent support',
  },
];
export const culturalFitOptions = [
  {
    value: 'major_differences_prone_to_conflict',
    label: 'Major differences, prone to conflict',
  },
  {
    value: 'moderate_differences',
    label: 'Moderate differences',
  },
  {
    value: 'some_similarities',
    label: 'Some similarities',
  },
  {
    value: 'fairly_aligned_easy_to_collaborate',
    label: 'Fairly aligned, easy to collaborate',
  },
  {
    value: 'very_aligned_mutually_supportive',
    label: 'Very aligned, mutually supportive',
  },
];
export const longTermVisionCommitmentOptions = [
  {
    value: 'no_commitment',
    label: 'No commitment',
  },
  {
    value: 'short_term_commitment_1_year',
    label: 'Short-term commitment (<1 year)',
  },
  {
    value: 'commitment_of_1_2_years',
    label: 'Commitment of 1-2 years',
  },
  {
    value: 'commitment_of_2_3_years',
    label: 'Commitment of 2-3 years',
  },
  {
    value: 'commitment_over_3_years',
    label: 'Commitment over 3 years',
  },
];
export const potentialForMultiServiceOptions = [
  {
    value: 'no_need',
    label: 'No need',
  },
  {
    value: 'small_need_1_service',
    label: 'Small need (1 service)',
  },
  {
    value: 'interest_in_2_services',
    label: 'Interest in 2 services',
  },
  {
    value: 'interest_in_multi_service_collaboration',
    label: 'Interest in multi-service collaboration',
  },
  {
    value: 'potential_to_use_entire_ecosystem_of_services',
    label: 'Potential to use entire ecosystem of services',
  },
];
export const crossSellUpSellPotentialOptions = [
  {
    value: 'no_potential',
    label: 'No potential',
  },
  {
    value: 'low_opportunity',
    label: 'Low opportunity',
  },
  {
    value: 'some_opportunities',
    label: 'Some opportunities',
  },
  {
    value: 'multiple_complementary_services',
    label: 'Multiple complementary services',
  },
  {
    value: 'high_growth_potential',
    label: 'High growth potential',
  },
];
export const industryInfluenceOptions = [
  {
    value: 'no_influence',
    label: 'No influence',
  },
  {
    value: 'minor_influence',
    label: 'Minor influence',
  },
  {
    value: 'influence_in_a_niche',
    label: 'Influence in a niche',
  },
  {
    value: 'broad_industry_influence',
    label: 'Broad industry influence',
  },
  {
    value: 'iconic_customer_industry_leader',
    label: 'Iconic customer, industry leader',
  },
];
export const referralCapabilityOptions = [
  {
    value: 'no_ability',
    label: 'No ability',
  },
  {
    value: 'low_referral_capability',
    label: 'Low referral capability',
  },
  {
    value: 'occasionally_refers',
    label: 'Occasionally refers',
  },
  {
    value: 'actively_refers',
    label: 'Actively refers',
  },
  {
    value: 'key_customer_creates_many_new_opportunities',
    label: 'Key customer, creates many new opportunities',
  },
];

export const CONVERT_TYPE_TO_GET_OPTION_ITEMS: { [key: string]: IOptionsItem[] } = {
  businessType: businessTypeOptions,
  industryICPFit: industryAndICPFitOptions,
  revenueScale: revenueScaleOptions,
  annualMarketingBudget: annualMarketingBudgetOptions,
  contractType: contractTypeOptions,
  paymentHistory: paymentHistoryOptions,
  workingHistoryWithMVVGroup: workingHistoryWithMVVGroupOptions,
  projectFrequency: projectFrequencyOptions,
  accessToDecisionMakers: accessToDecisionMakersOptions,
  decisionMakingSpeed: decisionMakingSpeedOptions,
  complexityOfDecisionProcess: complexityOfDecisionProcessOptions,
  marketingTeamStrength: marketingTeamStrengthOptions,
  willingnessToShareInformation: willingnessToShareInformationOptions,
  proActivenessInProjectCollaboration: proActivenessInProjectCollaborationOptions,
  culturalFit: culturalFitOptions,
  longTermVisionCommitment: longTermVisionCommitmentOptions,
  potentialForMultiService: potentialForMultiServiceOptions,
  industryInfluence: industryInfluenceOptions,
  referralCapability: referralCapabilityOptions,
  crossCellAndUpCell: crossSellUpSellPotentialOptions,
};
