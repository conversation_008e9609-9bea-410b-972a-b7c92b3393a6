export const DATA_5T = {
  priority_score_output: {
    dimensions: [
      {
        dimension_name: 'Finance',
        score: 6.0,
        weight: 0.35,
        weighted_score: 2.1,
        comment: 'High revenue and budget, highest priority.',
      },
      {
        dimension_name: 'Brand',
        score: 7.0,
        weight: 0.25,
        weighted_score: 1.75,
        comment: 'Strong influence, enhances reputation.',
      },
      {
        dimension_name: 'Chemistry',
        score: 6.0,
        weight: 0.20,
        weighted_score: 1.2,
        comment: 'Strong relationship, easy collaboration.',
      },
      {
        dimension_name: 'Expertise',
        score: 8.0,
        weight: 0.15,
        weighted_score: 1.2,
        comment: 'Professional processes, worth learning from.',
      },
      {
        dimension_name: 'Social Impact',
        score: 7.0,
        weight: 0.05,
        weighted_score: 0.35,
        comment: 'Creates social value, aligns with CSR.',
      },
    ],
    total_score: 6.60,
  },
  engagement_output:
  {
    engagement_strategy: [
      {
        dimension: 'Money',
        action: 'Increase project frequency through smaller service packages',
        kpi: 'Increase project frequency to 3 projects / year in 6 months',
      },
      {
        dimension: 'Brand',
        action: 'Explore referral opportunities from the client',
        kpi: '1 PR article in 6 months',
      },
      {
        dimension: 'Chemistry',
        action: 'Organize workshops to improve communication and coordination',
        kpi: 'Raise coordination score to 4',
      },
      {
        dimension: 'Expertise',
        action: 'Document best practices from marketing and decision - making processes',
        kpi: 'Apply 1 best practice',
      },
      {
        dimension: 'Social Impact',
        action: 'Propose small CSR for testing',
        kpi: 'Implement 1 CSR campaign in 12 months',
      },
    ],
  },
  risk_output: {

    risk_assessment: [
      {
        risk_name: 'Unspecified Contract Type',
        justification: 'The contract type is not clearly defined, which may lead to ambiguities in engagement terms and deliverables.',
      },
      {
        risk_name: 'Payment Delays',
        justification: 'The QA evaluation suggests that the client occasionally pays within 15 days, indicating a risk of delayed payments.',
      },
      {
        risk_name: 'Unclear Revenue Scale',
        justification: 'The client’s revenue scale is estimated at 500k$, but its impact on financial stability and project financing is uncertain.',
      },
      {
        risk_name: 'Limited Working History',
        justification: 'The client has an average collaboration history with MVV Group, implying limited proven effectiveness in past partnerships.',
      },
      {
        risk_name: 'Project Frequency Uncertainty',
        justification: 'Although the client shows moderate engagement, the specified 3 - 5 projects per year suggests variable engagement levels.',
      },
      {
        risk_name: 'Lack of Cultural Fit Information',
        justification: 'Cultural fit is not explicitly specified, which might affect collaboration dynamics.',
      },
      {
        risk_name: 'Marketing Budget Constraints',
        justification: 'The client currently has no budget for marketing, posing risks in brand visibility and market penetration.',
      },
      {
        risk_name: 'Vague Decision Process Complexity',
        justification: 'Decision process complexity is not specified, potentially influencing project delays or execution challenges.',
      },
      {
        risk_name: 'Cross - sell and Up - sell Uncertainty',
        justification: 'Limited information on cross - sell and up - sell potential implies unpredictability in expanding service offerings.',
      },
    ],
  },
};

export const QUESTION_MOCK_LIST = [{
  id: '1',
  name: 'businessType',
  question:
    'What type of business entity is the client, and is their industry aligned with our Ideal Customer Profile?',
  answer: '',
}, {
  id: '2',
  name: 'marketingBudget',
  question: `What is the client's estimated revenue and annual marketing budget?`,
  answer: '',
}, {
  id: '3',
  name: 'previousWork',
  question:
    'Has the client ever worked with our company? If so, how frequently and how would you rate the quality of that cooperation?',
  answer: '',
}, {
  id: '4',
  name: 'decisionMaking',
  question: 'Is the client easy to reach and quick to make decisions?',
  answer: '',
}, {
  id: '5',
  name: 'collaboration',
  question:
    'How effectively does the client collaborate during the project/work process?',
  answer: '',
}, {
  id: '6',
  name: 'note',
  question: 'Notes',
  answer: '',
}];
