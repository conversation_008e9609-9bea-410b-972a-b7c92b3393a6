export type ProjectStatus = 'Pending' | 'In Progress' | 'Done';

export type ProjectCategory = 'Development' | 'Design' | 'Marketing' | 'Research' | 'Operations';

export type TeamMember = {
  id: string;
  name: string;
  avatar: string;
  role: string;
};

export const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: '/images/users/user-01.jpg',
    role: 'Project Manager',
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: '/images/users/user-02.jpg',
    role: 'Lead Developer',
  },
  {
    id: '3',
    name: '<PERSON>',
    avatar: '/images/users/user-03.jpg',
    role: 'UX Designer',
  },
  {
    id: '4',
    name: '<PERSON>',
    avatar: '/images/users/user-04.jpg',
    role: 'Marketing Specialist',
  },
  {
    id: '5',
    name: '<PERSON>',
    avatar: '/images/users/user-05.jpg',
    role: 'Data Analyst',
  },
];
