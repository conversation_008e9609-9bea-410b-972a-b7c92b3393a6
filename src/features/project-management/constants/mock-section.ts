import type { EvaluationCriteria } from '../types/evaluation-form';

export const initialEvaluationData: EvaluationCriteria[] = [
  {
    id: '1',
    criteria: 'Business type',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'businessType',
  },
  {
    id: '2',
    criteria: 'Industry & ICP fit',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '2',
    weight: '7.69',
    criteriaScore: '',
    convertedScore: '',
    type: 'industryICPFit',
  },
  {
    id: '3',
    criteria: 'Revenue size',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'revenueScale',
  },
  {
    id: '4',
    criteria: 'Annual marketing budget',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '3',
    weight: '11.54',
    criteriaScore: '',
    convertedScore: '',
    type: 'annualMarketingBudget',
  },
  {
    id: '5',
    criteria: 'Contract type',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'contractType',
  },
];

export const financialCapacitySection: EvaluationCriteria[] = [
  {
    id: '1',
    criteria: 'Payment History',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'paymentHistory',
  },
  {
    id: '2',
    criteria: 'History of Working with MVV Group',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'workingHistoryWithMVVGroup',
  },
  {
    id: '3',
    criteria: 'Number of Project/Year',
    answer: '',
    confidence: '0',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'projectFrequency',
  },
  {
    id: '4',
    criteria: 'Access to Decision Makers',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '3',
    weight: '11.54',
    criteriaScore: '',
    convertedScore: '',
    type: 'accessToDecisionMakers',
  },
  {
    id: '5',
    criteria: 'Decision Making Process',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'decisionMakingSpeed',
  },
];

export const collaborationSection: EvaluationCriteria[] = [
  {
    id: '1',
    criteria: 'Complexity of Decision Process',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'complexityOfDecisionProcess',
  },
  {
    id: '2',
    criteria: 'Marketing Team Strength',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '2',
    weight: '7.69',
    criteriaScore: '',
    convertedScore: '',
    type: 'marketingTeamStrength',
  },
  {
    id: '3',
    criteria: 'Willingness to Share Information',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'willingnessToShareInformation',
  },
  {
    id: '4',
    criteria: 'Pro-activeness in Project Collaboration',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'proActivenessInProjectCollaboration',
  },
  {
    id: '5',
    criteria: 'Cultural Fit',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'culturalFit',
  },
];

export const growthPotentialSection: EvaluationCriteria[] = [
  {
    id: '1',
    criteria: 'Long-term Vision & Commitment',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'longTermVisionCommitment',
  },
  {
    id: '2',
    criteria: 'Potential for Multi-Service',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'potentialForMultiService',
  },
  {
    id: '3',
    criteria: 'Cross-sell/Up-sell Potential',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'crossCellAndUpCell',
  },
  {
    id: '4',
    criteria: 'Industry Influence',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'industryInfluence',
  },
  {
    id: '5',
    criteria: 'Referral Capability',
    answer: '',
    confidence: '',
    citation: '',
    criteriaType: '1',
    weight: '3.85',
    criteriaScore: '',
    convertedScore: '',
    type: 'referralCapability',
  },
];
