import type { IOptionsItem } from './mock-option';

export enum EValueModelAI {
  GPT = 'gpt-4.1',
  O3 = 'o3',
  O3Mini = 'o3-mini',
  O1 = 'o1',
  O1Mini = 'o1-mini',
  O4Mini = 'o4-mini',
  GPT4O = 'gpt-4o',
  GPT4OMini = 'gpt-4o-mini',
}

export const ModelAISelection: IOptionsItem[] = [
  {
    label: 'GPT-4.1',
    value: EValueModelAI.GPT,
  },
  {
    label: 'OpenAI O3',
    value: EValueModelAI.O3,
  },
  {
    label: 'OpenAI O3-mini',
    value: EValueModelAI.O3Mini,
  },
  {
    label: 'OpenAI O1',
    value: EValueModelAI.O1,
  },
  {
    label: 'OpenAI O1-mini',
    value: EValueModelAI.O1Mini,
  },
  {
    label: 'OpenAI O4-mini',
    value: EValueModelAI.O4Mini,
  },
  {
    label: 'GPT-4o',
    value: EValueModelAI.GPT4O,
  },
  {
    label: 'GPT-4o-mini',
    value: EValueModelAI.GPT4OMini,
  },
];
