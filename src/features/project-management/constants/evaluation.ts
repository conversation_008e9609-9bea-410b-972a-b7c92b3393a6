import { EType, EValueOfValuationType } from '../types/evaluation';

export const GET_POSITION_BY_TYPE: { [key in EType]: EValueOfValuationType } = {
  [EType.businessType]: EValueOfValuationType.businessType,
  [EType.industryICPFit]: EValueOfValuationType.industryICPFit,
  [EType.revenueScale]: EValueOfValuationType.revenueScale,
  [EType.annualMarketingBudget]: EValueOfValuationType.annualMarketingBudget,
  [EType.contractType]: EValueOfValuationType.contractType,
  [EType.paymentHistory]: EValueOfValuationType.paymentHistory,
  [EType.workingHistoryWithMVVGroup]: EValueOfValuationType.workingHistoryWithMVVGroup,
  [EType.projectFrequency]: EValueOfValuationType.projectFrequency,
  [EType.accessToDecisionMakers]: EValueOfValuationType.accessToDecisionMakers,
  [EType.decisionMakingSpeed]: EValueOfValuationType.decisionMakingSpeed,
  [EType.complexityOfDecisionProcess]: EValueOfValuationType.complexityOfDecisionProcess,
  [EType.marketingTeamStrength]: EValueOfValuationType.marketingTeamStrength,
  [EType.willingnessToShareInformation]: EValueOfValuationType.willingnessToShareInformation,
  [EType.proActivenessInProjectCollaboration]: EValueOfValuationType.proActivenessInProjectCollaboration,
  [EType.culturalFit]: EValueOfValuationType.culturalFit,
  [EType.longTermVisionCommitment]: EValueOfValuationType.longTermVisionCommitment,
  [EType.potentialForMultiService]: EValueOfValuationType.potentialForMultiService,
  [EType.crossCellAndUpCell]: EValueOfValuationType.crossCellAndUpCell,
  [EType.industryInfluence]: EValueOfValuationType.industryInfluence,
  [EType.referralCapability]: EValueOfValuationType.referralCapability,
};
