import { z } from 'zod';

// Define the validation schema for the Initial Screening Form
export const initialScreeningFormSchema = z.object({
  businessType: z.string().optional(),
  revenue: z.string().optional(),
  previousWork: z.string().optional(),
  decisionMaking: z.string().optional(),
  collaboration: z.string().optional(),
  notes: z.string().optional(),
  // Files are handled separately since they can't be directly validated by Zod
});

// Export the type for use in the form component
export type InitialScreeningFormData = z.infer<typeof initialScreeningFormSchema>;
