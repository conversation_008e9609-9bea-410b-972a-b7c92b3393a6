'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { updateStatusProject as updateStatus } from '../services/project.service';

export function useProjectUpdateStatus() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // updateStatus project mutation
  const updateStatusProjectMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: number }) => updateStatus(id, status),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast.success('Project updated successfully');
    },
    onError: (error) => {
      console.error('Error deleting project:', error);
      toast.error('Failed to update project');
    },
  });

  // Function to updateStatus a project
  const updateStatusProject = useCallback(async ({ id, status }: { id: string; status: number }) => {
    return updateStatusProjectMutation.mutateAsync({ id, status });
  }, [updateStatusProjectMutation]);

  return {
    // Mutation state
    isUpdating: updateStatusProjectMutation.isPending,
    updateStatusError: updateStatusProjectMutation.error,

    // Action
    updateStatusProject,
  };
}
