'use client';

import type { CreateProjectPayload } from '../validation/project.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { createProject as createProjectApi } from '../services/project.service';

/**
 * Hook for creating a new project
 *
 * This hook provides a way to create a new project.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function useProjectCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: (data: CreateProjectPayload) => createProjectApi(data),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      console.error('Error creating project:', error);
    },
  });

  // Function to create a project
  const createProject = useCallback(async (data: CreateProjectPayload) => {
    return createProjectMutation.mutateAsync(data);
  }, [createProjectMutation]);

  return {
    // Mutation state
    isCreating: createProjectMutation.isPending,
    createError: createProjectMutation.error,

    // Action
    createProject,
  };
}
