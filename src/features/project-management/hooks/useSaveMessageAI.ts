'use client';

import { useMutation } from '@tanstack/react-query';
import { saveMessageAI } from '../services/project.service';

export function useSaveMessageAI<T>() {
  // Get query client for cache invalidation
  return useMutation({
    mutationFn: ({
      conversationId,
      data,
    }: {
      conversationId: string;
      data: T;
    }) => saveMessageAI<T>(conversationId, data),
    onSuccess: () => {
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error save message in conversation :', error);
    },
  });
}
