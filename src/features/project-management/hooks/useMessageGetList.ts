import { useInfiniteQuery } from '@tanstack/react-query';
import { getListMessage } from '../services/project.service';

export function useMessageGetList(conversationId: string) {
  return useInfiniteQuery({
    queryKey: ['getListMessage', conversationId],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await getListMessage({
        conversationId,
        page: pageParam as number,
        itemsPerPage: 50, // Default page size
      });

      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response structure
      if (lastPage.data && lastPage.data.hasNextPage) {
        return lastPage.data.page + 1;
      }
      return undefined;
    },
    select: data => ({
      pages: data.pages.map(page => page.data),
      pageParams: data.pageParams,
      items: data.pages.flatMap(page => (page.data ? page.data.items : [])),
    }),
    enabled: !!conversationId,
    staleTime: 0,
  });
}
