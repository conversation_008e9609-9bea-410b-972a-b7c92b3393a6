'use client';

import { useQuery } from '@tanstack/react-query';
import { getProjectById } from '../services/project.service';

/**
 * Hook for fetching a single project by ID
 *
 * This hook provides a way to fetch a single project by ID.
 * It uses React Query for data fetching and caching.
 *
 * @param options - Configuration options for the hook
 * @returns Project data and loading/error states
 */
export function useProjectDetail(selectedProjectId: string) {
  return useQuery({
    queryKey: ['project', selectedProjectId],
    queryFn: () => getProjectById(selectedProjectId as string),
    select: response => response.data,
    enabled: !!selectedProjectId,
    staleTime: 0,
  });
}
