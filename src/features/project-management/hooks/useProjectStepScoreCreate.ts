'use client';

import type { CreateScreeningOutcome } from '../types/evaluation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { createScreeningOutcome } from '../services/project.service';

/**
 * Hook for creating a new project
 *
 * This hook provides a way to create a new project.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function useProjectStepScoreCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: ({ data, id }: { data: CreateScreeningOutcome; id: string }) => createScreeningOutcome(data, id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['stepScoring'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error creating step score:', error);
    },
  });

  // Function to create a project
  const createProject = useCallback(async (data: CreateScreeningOutcome, id: string) => {
    return createProjectMutation.mutateAsync({ data, id });
  }, [createProjectMutation]);

  return {
    // Mutation state
    isCreating: createProjectMutation.isPending,
    createError: createProjectMutation.error,

    // Action
    createProject,
  };
}
