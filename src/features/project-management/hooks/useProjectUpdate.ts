'use client';

import type { CreateProjectPayload } from '../validation/project.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { updateProject as updateProjectApi } from '../services/project.service';

export function useProjectUpdate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  const updateProjectMutation = useMutation({
    mutationFn: ({ data, id }: { id: string; data: CreateProjectPayload }) => updateProjectApi(data, id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      console.error('Error creating project:', error);
    },
  });

  // Function to create a project
  const updateProject = useCallback(async (data: CreateProjectPayload, id: string) => {
    return updateProjectMutation.mutateAsync({ data, id });
  }, [updateProjectMutation]);

  return {
    // Mutation state
    isUpdating: updateProjectMutation.isPending,
    updateError: updateProjectMutation.error,

    // Action
    updateProject,
  };
}
