'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { getResearch } from '../services/project.service';
import type { ResearchQueryParams } from '../types/research';

/**
 * Hook for fetching research data with infinite scrolling
 *
 * This hook provides a way to fetch research data with pagination for infinite scrolling.
 * It uses React Query's useInfiniteQuery for data fetching and pagination.
 *
 * @param params - Query parameters for research data
 * @returns Infinite query data and methods
 */
export function useProjectResearchInfiniteQuery(params: ResearchQueryParams = {}) {
  const infiniteQuery = useInfiniteQuery({
    queryKey: ['research', 'infinite', params],
    queryFn: async ({ pageParam = 1 }) => {
      // Use the getResearch function with page parameter
      const response = await getResearch({
        ...params,
        page: pageParam,
        itemsPerPage: params.itemsPerPage || 10, // Default page size
      });

      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response
      if (lastPage.data && lastPage.data.hasNextPage) {
        return lastPage.data.page + 1;
      }
      return undefined;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Extract all research items from all pages
  const allResearchItems = infiniteQuery.data?.pages?.flatMap(page => page.data?.items || []) || [];

  // Extract first page data for convenience
  const firstPageData = infiniteQuery.data?.pages?.[0]?.data;

  return {
    // Data
    infiniteData: infiniteQuery.data,
    firstPageData,
    allResearchItems,

    // Loading states
    isLoading: infiniteQuery.isLoading,
    isFetching: infiniteQuery.isFetching,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,

    // Pagination
    fetchNextPage: infiniteQuery.fetchNextPage,
    hasNextPage: infiniteQuery.hasNextPage,

    // Error state
    error: infiniteQuery.error,

    // Refetch action
    refetch: infiniteQuery.refetch,

    // Mutation helpers
    invalidate: () => infiniteQuery.refetch(),
  };
}
