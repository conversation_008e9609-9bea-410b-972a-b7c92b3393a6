'use client';

import { useQuery } from '@tanstack/react-query';
import { useCallback } from 'react';
import { getScoreDetail } from '../services/project.service';
import { useWorkflowStoreSelector } from '../stores/project-workflow-store/store';
import { useProjectDetailActions } from '../stores/project-workflow-store';

export function useProjectScoreDetail() {
  // Get selected project ID from store if not provided
  const selectedProjectId = useWorkflowStoreSelector(s => s.selectedProjectId);
  const selectedStepId = useWorkflowStoreSelector(s => s.selectedStepId);

  const { setSelectedStepId, setSelectedProjectId } = useProjectDetailActions();

  // Query for project details
  const projectQuery = useQuery({
    queryKey: ['scoreDetail', selectedProjectId, selectedStepId],
    queryFn: () => getScoreDetail(selectedProjectId as string, selectedStepId as string),
    select: response => response.data,
    enabled: !!selectedProjectId && !!selectedStepId,
  });

  // Function to select a project
  const selectScoreDetailId = useCallback((id: string | null, stepId: string | null) => {
    setSelectedProjectId(id);
    setSelectedStepId(stepId);
  }, [setSelectedProjectId, setSelectedStepId]);

  return {
    // Data
    scoreDetail: projectQuery.data,
    selectedProjectId,
    selectedStepId,
    // Loading states
    isLoading: projectQuery.isLoading,
    isFetching: projectQuery.isFetching,

    // Error state
    error: projectQuery.error,

    // Actions
    selectScoreDetailId,
    refetch: projectQuery.refetch,
  };
}
