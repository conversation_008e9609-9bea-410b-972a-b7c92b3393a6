'use client';

import type { ProjectOptions } from './useProjectOptions';
import { useInfiniteQuery } from '@tanstack/react-query';
import { getProjects } from '../services/project.service';
import { useProjectFilters } from './useProjectFilters';
import { mergeProjectOptions } from './useProjectOptions';

/**
 * Hook for fetching projects with infinite scrolling
 *
 * This hook provides a way to fetch projects with pagination for infinite scrolling.
 * It uses React Query's useInfiniteQuery for data fetching and pagination.
 *
 * @param options - Configuration options for the hook
 * @returns Infinite query data and methods
 */
export function useProjectInfiniteQuery(options: Partial<ProjectOptions> = {}) {
  // Merge default options with provided options
  const projectOptions = mergeProjectOptions(options);

  // Get filters from the filter hook
  const { apiFilters } = useProjectFilters();

  // Use a simpler infinite query configuration
  const infiniteQuery = useInfiniteQuery({
    queryKey: ['projects', 'infinite', apiFilters],
    queryFn: async ({ pageParam = 1 }) => {
      // Use the regular getProjects function with page parameter
      const response = await getProjects({
        ...apiFilters,
        page: pageParam,
      });

      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response
      if (lastPage.data && lastPage.data.hasNextPage) {
        return lastPage.data.page + 1;
      }
      return undefined;
    },
    staleTime: projectOptions.staleTime,
    refetchOnWindowFocus: projectOptions.refetchOnWindowFocus,
  });

  // Extract first page data for convenience with safe access
  const firstPageData = infiniteQuery.data?.pages?.[0]?.data;

  return {
    // Data
    infiniteData: infiniteQuery.data,
    firstPageData,

    // Loading states
    isLoading: infiniteQuery.isLoading,
    isFetching: infiniteQuery.isFetching,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,

    // Pagination
    fetchNextPage: infiniteQuery.fetchNextPage,
    hasNextPage: infiniteQuery.hasNextPage,

    // Error state
    error: infiniteQuery.error,

    // Refetch action
    refetch: infiniteQuery.refetch,
  };
}
