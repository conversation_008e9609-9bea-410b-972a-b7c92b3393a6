'use client';

/**
 * Options for project-related hooks
 */
export type ProjectOptions = {
  /**
   * Stale time for queries in milliseconds
   * @default 300000 (5 minutes)
   */
  staleTime?: number;

  /**
   * Whether to refetch on window focus
   * @default false
   */
  refetchOnWindowFocus?: boolean;
};

/**
 * Default options for project hooks
 */
export const DEFAULT_PROJECT_OPTIONS: ProjectOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: false,
};

/**
 * Merges default options with provided options
 *
 * @param options - User provided options
 * @returns Merged options
 */
export function mergeProjectOptions(options: Partial<ProjectOptions> = {}): ProjectOptions {
  return { ...DEFAULT_PROJECT_OPTIONS, ...options };
}
