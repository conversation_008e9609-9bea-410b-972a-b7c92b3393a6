'use client';

import type { ProjectOptions } from './useProjectOptions';
import { useQuery } from '@tanstack/react-query';
import { getProjects } from '../services/project.service';
import { useProjectFilters } from './useProjectFilters';
import { mergeProjectOptions } from './useProjectOptions';

/**
 * Hook for fetching a list of projects
 *
 * This hook provides a way to fetch a list of projects with filtering.
 * It uses React Query for data fetching and caching.
 *
 * @param options - Configuration options for the hook
 * @returns Project list data and loading/error states
 */
export function useProjectList(options: Partial<ProjectOptions> = {}) {
  // Merge default options with provided options
  const projectOptions = mergeProjectOptions(options);

  // Get filters from the filter hook
  const { apiFilters } = useProjectFilters();

  // Query for projects
  const projectsQuery = useQuery({
    queryKey: ['projects', apiFilters],
    queryFn: () => getProjects(apiFilters),
    select: response => response.data,
    staleTime: projectOptions.staleTime,
    refetchOnWindowFocus: projectOptions.refetchOnWindowFocus,
  });

  return {
    // Data
    projects: projectsQuery.data,

    // Loading states
    isLoading: projectsQuery.isLoading,
    isFetching: projectsQuery.isFetching,

    // Error state
    error: projectsQuery.error,

    // Refetch action
    refetch: projectsQuery.refetch,
  };
}
