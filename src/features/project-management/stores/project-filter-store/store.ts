'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { FilterState, ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../../types/project';

/**
 * Project store state interface
 */
type ProjectFilterState = {
  // Filter state
  filters: {
    status: ProjectStatusEnum | 'All';
    type: ProjectTypeEnum | 'All';
    campaign: ProjectCampaignEnum | 'All';
    startDate: Date | undefined;
    endDate: Date | undefined;
  };

  // Search state (separated from filters)
  searchValue: string;

  // UI state
  selectedProjectId: string | null;

  selectedStepId: string | null;
};

/**
 * Project store actions interface
 */
type ProjectFilterActions = {
  /**
   * Set a single filter
   */
  setFilter: <K extends keyof ProjectFilterState['filters']>(
    key: K,
    value: ProjectFilterState['filters'][K]
  ) => void;

  /**
   * Apply multiple filters at once
   */
  applyFilters: (filters: FilterState) => void;

  /**
   * Remove a specific filter
   */
  removeFilter: (filterType: keyof FilterState) => void;

  /**
   * Reset all filters to default values
   */
  resetFilters: () => void;

  /**
   * Set the search value
   */
  setSearchValue: (value: string) => void;

  /**
   * Clear the search value
   */
  clearSearchValue: () => void;

  /**
   * Set the selected project ID
   */
  setSelectedProjectId: (id: string | null) => void;

  setSelectedStepId: (id: string | null) => void;
};

/**
 * Combined project store type
 */
type ProjectStore = ProjectFilterState & ProjectFilterActions;

/**
 * Initial project state
 */
const initialState: ProjectFilterState = {
  // Initial filter state
  filters: {
    status: 'All',
    type: 'All',
    campaign: 'All',
    startDate: undefined,
    endDate: undefined,
  },

  // Initial search state
  searchValue: '',

  // Initial UI state
  selectedProjectId: null,

  selectedStepId: null,

};

/**
 * Create the project store with Zustand
 */
export const useProjectFilterStore = create<ProjectStore>()(
  devtools(
    set => ({
      // Initial state
      ...initialState,

      // Set a single filter
      setFilter: (key, value) => {
        set(state => ({
          filters: {
            ...state.filters,
            [key]: value,
          },
        }));
      },

      // Apply multiple filters at once
      applyFilters: (filters) => {
        set({
          filters: {
            status: filters.status,
            type: filters.type,
            campaign: filters.campaign,
            startDate: filters.startDateRange.from,
            endDate: filters.endDateRange.from,
          },
          // Also update search value if provided
          ...(filters.searchQuery !== undefined && { searchValue: filters.searchQuery }),
        });
      },

      // Remove a specific filter
      removeFilter: (filterType) => {
        set((state) => {
          // Handle search separately
          if (filterType === 'searchQuery') {
            return {
              ...state, // Preserve all other state
              searchValue: '',
            };
          }

          // Handle other filters
          const newFilters = { ...state.filters };

          switch (filterType) {
            case 'status':
              newFilters.status = 'All';
              break;
            case 'type':
              newFilters.type = 'All';
              break;
            case 'campaign':
              newFilters.campaign = 'All';
              break;
            case 'startDateRange':
              newFilters.startDate = undefined;
              break;
            case 'endDateRange':
              newFilters.endDate = undefined;
              break;
          }

          return {
            ...state, // Preserve all other state
            filters: newFilters,
          };
        });
      },

      // Reset all filters to default values
      resetFilters: () => {
        set(state => ({
          ...state, // Preserve other state
          filters: initialState.filters,
          searchValue: initialState.searchValue,
        }));
      },

      // Set the search value
      setSearchValue: (value) => {
        set({ searchValue: value });
      },

      // Clear the search value
      clearSearchValue: () => {
        set({ searchValue: '' });
      },

      // Set the selected project ID
      setSelectedProjectId: (id) => {
        set({ selectedProjectId: id });
      },

      // Set the selected project ID
      setSelectedStepId: (id) => {
        set({ selectedStepId: id });
      },
    }),
    { name: 'project-store' },
  ),
);
