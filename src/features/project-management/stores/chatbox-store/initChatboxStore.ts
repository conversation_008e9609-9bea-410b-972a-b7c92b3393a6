import { create } from 'zustand';

// Define the store type for chat box visibility
type ChatBoxVisibilityStore = {
  isVisible: boolean;
  lastMessage: string;
  isShowChatBox: boolean;
  onSubmitCallback: (() => void) | null;
  show: () => void;
  hide: () => void;
  toggle: () => void;
  submit: (msg: string) => void;
  setOnSubmitCallback: (callback: (() => void) | null) => void;
  onToggleShowChatBox: (status: boolean) => void;
};

// Create a global store for chat box visibility
// This allows the state to be shared across components without prop drilling
export const useChatBoxStore = create<ChatBoxVisibilityStore>((set, get) => ({
  isVisible: false,
  lastMessage: '',
  onSubmitCallback: null,
  isShowChatBox: true,
  show: () => set({ isVisible: true }),
  hide: () => set({ isVisible: false }),
  toggle: () => set(state => ({ isVisible: !state.isVisible })),
  submit: (msg: string) => {
    set(() => ({ lastMessage: msg }));
    // Call the registered callback if it exists
    const { onSubmitCallback } = get();
    if (onSubmitCallback) {
      onSubmitCallback();
    }
  },
  onToggleShowChatBox: (status) => {
    set(() => ({ isShowChatBox: status }));
  },
  setOnSubmitCallback: (callback: (() => void) | null) => set({ onSubmitCallback: callback }),
}));
