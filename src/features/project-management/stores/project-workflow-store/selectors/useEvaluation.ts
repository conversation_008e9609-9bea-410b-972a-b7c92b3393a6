import { SectionType } from '../../../types/evaluation-form';
import { useWorkflowStoreSelector } from '../store';

/**
 * Hook for accessing a specific section's data
 *
 * @param sectionType - The section type to get data for
 * @returns The evaluation criteria for the specified section
 *
 * @example
 * ```tsx
 * function ClientProfileSection() {
 *   const clientProfileData = useEvaluationSection(SectionType.CLIENT_PROFILE);
 *
 *   return (
 *     <div>
 *       {clientProfileData.map((criterion) => (
 *         <div key={criterion.id}>{criterion.criteria}</div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
export function useEvaluationSection(sectionType: SectionType) {
  return useWorkflowStoreSelector(state => state[sectionType]);
}

/**
 * Hook for accessing overall score only
 *
 * This is a convenience hook for components that only need the overall score.
 *
 * @returns The overall score object
 *
 * @example
 * ```tsx
 * function ScoreDisplay() {
 *   const overallScore = useOverallScore();
 *
 *   return (
 *     <div>
 *       <h1>{overallScore.score}</h1>
 *       <p>Rank: {overallScore.rank}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useOverallScore() {
  return useWorkflowStoreSelector(state => state.overallScore);
}

/**
 * Hook for accessing section totals only
 *
 * This is a convenience hook for components that only need section totals.
 *
 * @returns The section totals object
 *
 * @example
 * ```tsx
 * function SectionTotals() {
 *   const sectionTotals = useSectionTotals();
 *
 *   return (
 *     <div>
 *       <p>Client Profile: {sectionTotals[SectionType.CLIENT_PROFILE].score}</p>
 *       <p>Total: {sectionTotals.total.score}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useSectionTotals() {
  return useWorkflowStoreSelector(state => state.sectionTotals);
}

// ============================================================================
// ATOMIC STATE SELECTORS (RECOMMENDED)
// ============================================================================

/**
 * Hook for accessing client profile section data
 *
 * @returns Client profile evaluation criteria array
 */
export function useClientProfile() {
  return useWorkflowStoreSelector(state => state[SectionType.CLIENT_PROFILE]);
}

/**
 * Hook for accessing financial capacity section data
 *
 * @returns Financial capacity evaluation criteria array
 */
export function useFinancialCapacity() {
  return useWorkflowStoreSelector(state => state[SectionType.FINANCIAL_CAPACITY]);
}

/**
 * Hook for accessing collaboration section data
 *
 * @returns Collaboration evaluation criteria array
 */
export function useCollaboration() {
  return useWorkflowStoreSelector(state => state[SectionType.COLLABORATION]);
}

/**
 * Hook for accessing growth potential section data
 *
 * @returns Growth potential evaluation criteria array
 */
export function useGrowthPotential() {
  return useWorkflowStoreSelector(state => state[SectionType.GROWTH_POTENTIAL]);
}

export function useEvaluationActions() {
  return useWorkflowStoreSelector(state => state.actions);
}

export function useScoreDetail() {
  return useWorkflowStoreSelector(state => state.scoreDetail);
}
