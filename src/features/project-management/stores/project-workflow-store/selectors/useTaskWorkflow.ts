import { useWorkflowStoreSelector } from '../store';

/**
 * Hook for accessing tasks only
 *
 * This is a convenience hook for components that only need the tasks list.
 *
 * @returns The tasks array
 *
 * @example
 * ```tsx
 * function TaskList() {
 *   const tasks = useTasks();
 *
 *   return (
 *     <ul>
 *       {tasks.map((task) => (
 *         <li key={task.id}>{task.name}</li>
 *       ))}
 *     </ul>
 *   );
 * }
 * ```
 */
export function useTasks() {
  return useWorkflowStoreSelector(state => state.tasks);
}

/**
 * Hook for accessing selected task only
 *
 * This is a convenience hook for components that only need the selected task.
 *
 * @returns The selected task or null
 *
 * @example
 * ```tsx
 * function SelectedTaskDisplay() {
 *   const selectedTask = useSelectedTask();
 *
 *   if (!selectedTask) {
 *     return <p>No task selected</p>;
 *   }
 *
 *   return (
 *     <div>
 *       <h2>{selectedTask.name}</h2>
 *       <p>{selectedTask.description}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useSelectedTask() {
  return useWorkflowStoreSelector(state => state.selectedTask);
}

/**
 * Hook for accessing sidebar visibility only
 *
 * This is a convenience hook for components that only need sidebar visibility.
 *
 * @returns The sidebar visibility state
 *
 * @example
 * ```tsx
 * function SidebarToggle() {
 *   const sidebarVisible = useSidebarVisible();
 *
 *   return (
 *     <div>
 *       Sidebar is {sidebarVisible ? 'visible' : 'hidden'}
 *     </div>
 *   );
 * }
 * ```
 */
export function useSidebarVisible() {
  return useWorkflowStoreSelector(state => state.sidebarVisible);
}

/**
 * Hook for accessing actions related to task-workflow slice
 */
export function useTaskWorkflowActions() {
  return useWorkflowStoreSelector(state => state.actions);
}
