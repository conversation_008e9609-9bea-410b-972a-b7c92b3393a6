import { useWorkflowStoreSelector } from '../store';

/**
 * Hook for accessing selected project ID only
 * @returns Selected project ID or null
 */
export function useSelectedProjectId() {
  return useWorkflowStoreSelector(s => s.selectedProjectId);
}

/**
 * Hook for accessing project detail actions
 */
export function useProjectDetailActions() {
  return useWorkflowStoreSelector(s => s.actions);
}

export function useProjectInfo() {
  return useWorkflowStoreSelector(s => s.projectInfo);
}
