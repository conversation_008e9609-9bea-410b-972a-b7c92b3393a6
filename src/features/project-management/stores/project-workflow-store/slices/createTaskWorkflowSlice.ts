import { EStatusTask } from '@/features/project-management/types/workflow';
import type { ProjectStoreStateCreator, TaskWorkflowSlice } from '../types';

/**
 * Creates the Task Workflow slice
 *
 * This slice handles simplified task workflow management including:
 * - Managing tasks and their steps
 * - Handling task selection and sidebar visibility
 * - Managing task and step status updates
 *
 * @param set - Zustand set function
 * @param get - Zustand get function
 * @returns TaskWorkflowSlice
 */
export const createTaskWorkflowSlice: ProjectStoreStateCreator<TaskWorkflowSlice> = (set, get) => ({
  // ============================================================================
  // STATE
  // ============================================================================

  tasks: [],
  selectedTask: null,
  sidebarVisible: false,

  // ============================================================================
  // ACTIONS
  // ============================================================================

  actions: {

    /**
     * Set the tasks array
     *
     * @param tasks - The tasks to set
     */
    setTasks: tasks => set({ tasks }),

    /**
     * Set the selected task or step
     *
     * @param task - The task or step to select
     */
    setSelectedTask: task => set({ selectedTask: task }),

    /**
  /**
     * Set sidebar visibility
     *
     * @param visible - Whether the sidebar should be visible
     */
    setSidebarVisible: visible => set({ sidebarVisible: visible }),

    /**
     * Update the status of a task
     *
     * @param taskId - The ID of the task to update
     * @param status - The new status
     */
    updateTaskStatus: (taskId, status) => {
      set(state => ({
        tasks: state.tasks.map(task =>
          task.id === taskId ? { ...task, status } : task,
        ),
      }));
    },

    /**
     * Update the status of a step and automatically update parent task status
     *
     * @param taskId - The ID of the parent task
     * @param stepId - The ID of the step to update
     * @param status - The new status
     */
    updateStepStatus: (taskId, stepId, status) => {
      // First update the step status
      set(state => ({
        tasks: state.tasks.map(task =>
          task.id === taskId
            ? {
                ...task,
                steps: task.steps.map(step =>
                  step.id === stepId ? { ...step, status } : step,
                ),
              }
            : task,
        ),
      }));

      // Then update the parent task status based on steps
      const { tasks, actions } = get();
      const { updateTaskStatus } = actions;
      const task = tasks.find(t => t.id === taskId);

      if (task) {
        const allCompleted = task.steps.every(step => step.status === EStatusTask.COMPLETED);
        const allPending = task.steps.every(step => step.status === EStatusTask.PENDING);

        if (allCompleted) {
          updateTaskStatus(taskId, EStatusTask.COMPLETED);
        } else if (allPending) {
          updateTaskStatus(taskId, EStatusTask.PENDING);
        } else {
          updateTaskStatus(taskId, EStatusTask.IN_PROGRESS);
        }
      }
    },

    /**
     * Toggle sidebar visibility
     */
    toggleSidebar: () => set(state => ({ sidebarVisible: !state.sidebarVisible })),

    /**
     * Initialize the task workflow with default selected task
     */
    initializeTaskWorkflow: () => {
      const { tasks, actions } = get();
      const { setSelectedTask } = actions;

      // Find the first task with steps (which should be task with id '1')
      const task = tasks.find(t => t.id === '1');
      // Find the "Initial Screening Form" step (which should have id '1.1')
      const initialScreeningStep = task?.steps.find(step => step.id === '1.1');

      // Set the selected task to the Initial Screening Form step
      if (initialScreeningStep) {
        setSelectedTask(initialScreeningStep);
      } else {
        // Fallback to the first step of the first task with steps
        const firstTaskWithSteps = tasks.find(t => t.steps.length);
        setSelectedTask(firstTaskWithSteps?.steps[0] ?? null);
      }
    },
  },
});
