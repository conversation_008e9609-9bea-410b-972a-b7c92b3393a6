import type { IOptionsItem } from '../../../constants/mock-option';
import type { EvaluationCriteria, OverallScore } from '../../../types/evaluation-form';
import type { EvaluationSlice, ProjectStoreStateCreator, SectionTotals } from '../types';
import { CONVERT_TYPE_TO_GET_OPTION_ITEMS } from '../../../constants/mock-option';
import {
  initialEvaluationData as initialClientProfileSection,
  collaborationSection as initialCollaborationSection,
  financialCapacitySection as initialFinancialSection,
  growthPotentialSection as initialGrowthSection,
} from '../../../constants/mock-section';
import { SectionType } from '../../../types/evaluation-form';
import type { QAStrategistOutput, ScoreDetail } from '@/features/project-management/types/evaluation';
import type { StepQA } from '@/features/project-management/types/step';
import { processEvaluationData } from '@/features/project-management/utils/workflowUtils';

// Pre-compute all answer lists to avoid repeated lookups
export const PRE_COMPUTED_ANSWER_LISTS: Record<string, IOptionsItem[]> = {};
Object.entries(CONVERT_TYPE_TO_GET_OPTION_ITEMS).forEach(([key, value]) => {
  PRE_COMPUTED_ANSWER_LISTS[key] = [...value];
});

/**
 * Helper function to calculate section weight
 */
export const calculateSectionWeight = (criteria: EvaluationCriteria[]): string => {
  const sum = criteria.reduce(
    (total, criterion) => total + Number.parseFloat(criterion.weight || '0'),
    0,
  );
  return sum.toFixed(2);
};

/**
 * Helper function to calculate section score
 */
export const calculateSectionScore = (criteria: EvaluationCriteria[]): string => {
  const sum = criteria.reduce(
    (total, criterion) => total + Number.parseFloat(criterion.convertedScore || '0'),
    0,
  );
  return sum.toFixed(2);
};

/**
 * Helper function to determine rank based on score
 */
export const getRank = (score: number): string => {
  // Ensure score is a valid number
  if (Number.isNaN(score) || score === null || score === undefined) {
    console.warn('Invalid score provided to getRank:', score);
    return 'F';
  }

  // Convert score to a number to ensure proper comparison
  const numericScore = Number(score);

  if (numericScore >= 4.5) {
    return 'A+';
  }
  if (numericScore >= 4.0) {
    return 'A';
  }
  if (numericScore >= 3.5) {
    return 'B+';
  }
  if (numericScore >= 3.0) {
    return 'B';
  }
  if (numericScore >= 2.5) {
    return 'C+';
  }
  if (numericScore >= 2.0) {
    return 'C';
  }
  if (numericScore >= 1.5) {
    return 'D+';
  }
  if (numericScore >= 1.0) {
    return 'D';
  }
  return 'F';
};

/**
 * Initial section totals
 */
const initialSectionTotals: SectionTotals = {
  [SectionType.CLIENT_PROFILE]: { weight: '0.00', score: '0.00' },
  [SectionType.FINANCIAL_CAPACITY]: { weight: '0.00', score: '0.00' },
  [SectionType.COLLABORATION]: { weight: '0.00', score: '0.00' },
  [SectionType.GROWTH_POTENTIAL]: { weight: '0.00', score: '0.00' },
  total: { weight: '0.00', score: '0.00' },
};

/**
 * Initial overall score
 */
const initialOverallScore: OverallScore = {
  score: 0,
  percentile: 0,
  rank: '',
  data: {
    [SectionType.CLIENT_PROFILE]: [],
    [SectionType.FINANCIAL_CAPACITY]: [],
    [SectionType.COLLABORATION]: [],
    [SectionType.GROWTH_POTENTIAL]: [],
  },
};

/**
 * Creates the Evaluation slice
 *
 * This slice handles all evaluation-related functionality including:
 * - Managing evaluation criteria for different sections
 * - Calculating scores and totals
 * - Managing overall scores and rankings
 * - Handling answer updates and validations
 *
 * @param set - Zustand set function
 * @param get - Zustand get function
 * @returns EvaluationSlice
 */
export const createEvaluationSlice: ProjectStoreStateCreator<EvaluationSlice> = (set, get) => ({
  // ============================================================================
  // STATE
  // ============================================================================

  // Initial state for each section
  [SectionType.CLIENT_PROFILE]: [...initialClientProfileSection],
  [SectionType.FINANCIAL_CAPACITY]: [...initialFinancialSection],
  [SectionType.COLLABORATION]: [...initialCollaborationSection],
  [SectionType.GROWTH_POTENTIAL]: [...initialGrowthSection],

  // Initial section totals
  sectionTotals: { ...initialSectionTotals },

  // Initial overall score
  overallScore: { ...initialOverallScore },

  scoreDetail: null,

  actions: {
    /**
     * Initialize the evaluation store
     *
     * This calculates initial totals and sets up the overall score.
     */
    initializeEvaluation: () => {
      set((state) => {
        // Calculate totals for all sections
        const sectionTotals = {} as SectionTotals;
        let totalWeight = 0;
        let totalScore = 0;

        Object.values(SectionType).forEach((sectionType) => {
          const sectionData = state[sectionType];
          const sectionWeight = calculateSectionWeight(sectionData);
          const sectionScore = calculateSectionScore(sectionData);

          sectionTotals[sectionType] = {
            weight: sectionWeight,
            score: sectionScore,
          };

          totalWeight += Number.parseFloat(sectionWeight);
          totalScore += Number.parseFloat(sectionScore);
        });

        // Add the total to section totals
        sectionTotals.total = {
          weight: totalWeight.toFixed(2),
          score: totalScore.toFixed(2),
        };

        // Calculate overall score
        const normalizedScore = totalScore;
        const percentile = Math.min(normalizedScore * 20, 100);
        const rank = getRank(normalizedScore);

        return {
          sectionTotals,
          overallScore: {
            score: normalizedScore,
            percentile,
            rank,
            data: {
              [SectionType.CLIENT_PROFILE]: JSON.parse(JSON.stringify(state[SectionType.CLIENT_PROFILE])),
              [SectionType.FINANCIAL_CAPACITY]: JSON.parse(JSON.stringify(state[SectionType.FINANCIAL_CAPACITY])),
              [SectionType.COLLABORATION]: JSON.parse(JSON.stringify(state[SectionType.COLLABORATION])),
              [SectionType.GROWTH_POTENTIAL]: JSON.parse(JSON.stringify(state[SectionType.GROWTH_POTENTIAL])),
            },
          },
        };
      });
    },

    /**
     * Update initial evaluation data from AI output
     *
     * @param output - The QA strategist output to map to evaluation data
     */
    updateInitialEvaluationData: (output) => {
      set((state) => {
        const data = {
          initialEvaluationData: state[SectionType.CLIENT_PROFILE],
          financialCapacitySection: state[SectionType.FINANCIAL_CAPACITY],
          collaborationSection: state[SectionType.COLLABORATION],
          growthPotentialSection: state[SectionType.GROWTH_POTENTIAL],
        };

        const { updatedState, sectionTotals, overallScore } = processEvaluationData(data, output);
        return {
          ...updatedState,
          sectionTotals,
          overallScore,
        };
      });
    },

    /**
     * Get section data - optimized to return a reference to the original data
     *
     * @param sectionType - The section type to get data for
     * @returns The evaluation criteria for the section
     */
    getSectionData: (sectionType) => {
      return get()[sectionType];
    },

    /**
     * Get answer list for a criterion type - optimized with pre-computed lists
     *
     * @param type - The criterion type
     * @returns The list of available options
     */
    getAnswerList: (type) => {
      return PRE_COMPUTED_ANSWER_LISTS[type] || [];
    },

    /**
     * Update answer for a criterion
     *
     * This is the most complex operation that updates a single answer and
     * recalculates all dependent values in a single atomic operation.
     *
     * @param sectionType - The section containing the criterion
     * @param id - The criterion ID
     * @param answer - The new answer text
     * @param selectedIndex - The selected option index
     */
    updateAnswer: (sectionType, id, answer, selectedIndex) => {
      // Use a single atomic update to prevent race conditions
      set((state) => {
        // Create a deep copy of the section data to avoid mutation issues
        const sectionData = JSON.parse(JSON.stringify(state[sectionType]));

        // Find the criterion to update
        const criterionIndex = sectionData.findIndex((c: EvaluationCriteria) => c.id === id);
        if (criterionIndex === -1) {
          console.warn(`Criterion with id ${id} not found in section ${sectionType}`);
          return state; // No changes if criterion not found
        }

        // Get the criterion and update it
        const criterion = sectionData[criterionIndex];

        // Calculate the new score based on the selected index
        const score = selectedIndex !== undefined ? selectedIndex + 1 : 0;
        const weight = Number.parseFloat(criterion.weight || '0');
        // Divide by 100 to match the scale used in the mock data
        const convertedScore = ((weight * score) / 100).toFixed(2);

        // Update the criterion with the new values
        sectionData[criterionIndex] = {
          ...criterion,
          answer,
          criteriaScore: score.toString(),
          convertedScore,
          selectedOptionIndex: selectedIndex,
        };

        // Calculate section totals
        const sectionWeight = calculateSectionWeight(sectionData);
        const sectionScore = calculateSectionScore(sectionData);

        // Create a new section totals object
        const updatedSectionTotals = {
          ...state.sectionTotals,
          [sectionType]: {
            weight: sectionWeight,
            score: sectionScore,
          },
        };

        // Calculate overall totals
        let totalWeight = 0;
        let totalScore = 0;

        // Sum up all section weights and scores
        Object.values(SectionType).forEach((section) => {
          if (section === sectionType) {
            totalWeight += Number.parseFloat(sectionWeight);
            totalScore += Number.parseFloat(sectionScore);
          } else {
            totalWeight += Number.parseFloat(updatedSectionTotals[section].weight);
            totalScore += Number.parseFloat(updatedSectionTotals[section].score);
          }
        });

        // Update the total in section totals
        updatedSectionTotals.total = {
          weight: totalWeight.toFixed(2),
          score: totalScore.toFixed(2),
        };

        // Calculate normalized score for overall score
        const normalizedScore = totalScore;
        const percentile = Math.min(normalizedScore * 20, 100);
        const rank = getRank(normalizedScore);

        // Force a complete state update by creating a new state object
        const newState = {
          ...state,
          // Update the specific section with the new data
          [sectionType]: sectionData,

          // Update section totals
          sectionTotals: updatedSectionTotals,

          // Update overall score
          overallScore: {
            score: normalizedScore,
            percentile,
            rank,
            data: {
              // Update all sections in the overall score data
              [SectionType.CLIENT_PROFILE]:
                sectionType === SectionType.CLIENT_PROFILE
                  ? sectionData
                  : JSON.parse(JSON.stringify(state[SectionType.CLIENT_PROFILE])),
              [SectionType.FINANCIAL_CAPACITY]:
                sectionType === SectionType.FINANCIAL_CAPACITY
                  ? sectionData
                  : JSON.parse(JSON.stringify(state[SectionType.FINANCIAL_CAPACITY])),
              [SectionType.COLLABORATION]:
                sectionType === SectionType.COLLABORATION
                  ? sectionData
                  : JSON.parse(JSON.stringify(state[SectionType.COLLABORATION])),
              [SectionType.GROWTH_POTENTIAL]:
                sectionType === SectionType.GROWTH_POTENTIAL
                  ? sectionData
                  : JSON.parse(JSON.stringify(state[SectionType.GROWTH_POTENTIAL])),
            },
          },
        };

        return newState;
      });
    },

    /**
     * Calculate totals for a specific section
     *
     * @param sectionType - The section to calculate totals for
     */
    calculateSectionTotals: (sectionType) => {
      set((state) => {
        const sectionData = state[sectionType];
        const sectionWeight = calculateSectionWeight(sectionData);
        const sectionScore = calculateSectionScore(sectionData);

        return {
          sectionTotals: {
            ...state.sectionTotals,
            [sectionType]: {
              weight: sectionWeight,
              score: sectionScore,
            },
          },
        };
      });
    },

    /**
     * Calculate totals for all sections
     */
    calculateAllTotals: () => {
      // First calculate each section total
      const sectionTotals = {} as SectionTotals;
      let totalWeight = 0;
      let totalScore = 0;

      // Calculate totals for each section in a single operation
      set((state) => {
        Object.values(SectionType).forEach((sectionType) => {
          const sectionData = state[sectionType];
          const sectionWeight = calculateSectionWeight(sectionData);
          const sectionScore = calculateSectionScore(sectionData);

          sectionTotals[sectionType] = {
            weight: sectionWeight,
            score: sectionScore,
          };

          totalWeight += Number.parseFloat(sectionWeight);
          totalScore += Number.parseFloat(sectionScore);
        });

        // Add the total to section totals
        sectionTotals.total = {
          weight: totalWeight.toFixed(2),
          score: totalScore.toFixed(2),
        };

        return { sectionTotals };
      });
    },

    updateScoreDetail: (scoreDetail: StepQA<ScoreDetail, any> | null | undefined) => {
      set(() => {
        const data = scoreDetail?.stepInfo.reduce<Record<string, QAStrategistOutput>>((init, item) => {
          item.infos.forEach((i) => {
            const updatedItem = {
              confidence: `${+i.confidence / 100}` || '',
              option: i.answer,
              sources: i.citation.split(','),

            };

            if (!init[i.type]) {
              init[i.type] = {} as QAStrategistOutput;
            }

            init[i.type] = updatedItem as unknown as QAStrategistOutput;
          });

          return init;
        }, {} as Record<string, QAStrategistOutput>);

        return { scoreDetail: data };
      });
    },

    /**
     * Calculate final overall score
     */
    getFinalOverallScore: () => {
      set((state) => {
        const totalScore = Number.parseFloat(state.sectionTotals.total.score);

        // Calculate normalized score (0-5 scale)
        const normalizedScore = totalScore;

        // Calculate percentile and rank
        const percentile = Math.min(normalizedScore * 20, 100);
        const rank = getRank(normalizedScore);

        // Force a complete state update by creating a new state object
        return {
          overallScore: {
            score: normalizedScore,
            percentile,
            rank,
            data: {
              // Create deep copies of all sections to ensure reactivity
              [SectionType.CLIENT_PROFILE]: JSON.parse(JSON.stringify(state[SectionType.CLIENT_PROFILE])),
              [SectionType.FINANCIAL_CAPACITY]: JSON.parse(JSON.stringify(state[SectionType.FINANCIAL_CAPACITY])),
              [SectionType.COLLABORATION]: JSON.parse(JSON.stringify(state[SectionType.COLLABORATION])),
              [SectionType.GROWTH_POTENTIAL]: JSON.parse(JSON.stringify(state[SectionType.GROWTH_POTENTIAL])),
            },
          },
        };
      });
    },
  },

});
