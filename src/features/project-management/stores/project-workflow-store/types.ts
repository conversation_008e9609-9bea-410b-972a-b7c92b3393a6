import type { StateCreator } from 'zustand';
import type { IOptionsItem } from '../../constants/mock-option';
import type { QAStrategistOutput, ScoreDetail } from '../../types/evaluation';
import type { EvaluationCriteria, OverallScore, SectionType } from '../../types/evaluation-form';
import type { Step, Task, TaskStatus } from '../../types/task-workflow';
import type { EStatusTask, WorkflowStep } from '../../types/workflow';
import type { StepQA } from '../../types/step';
import type { IFileResponse } from '@/shared/types/global';
import type { Project } from '../../types/project';
// import type { WorkflowStep as WorkflowTask } from '../../types/step';

// Section totals type for evaluation
export type SectionTotals = {
  [key in SectionType]: { weight: string; score: string };
} & {
  total: { weight: string; score: string };
};

// ============================================================================
// SLICE INTERFACES
// ============================================================================

/**
 * Project Selection Slice - Handles project and step selection
 */
export type ProjectWorkflowSelectionSlice = {
  // State
  selectedProjectId: string | null;
  selectedStepId: string | null;

  projectInfo: Project | null;

  // Actions
  actions: {
    setSelectedProjectId: (id: string | null) => void;
    setSelectedStepId: (id: string | null) => void;
    clearSelection: () => void;
    saveProjectInfo: (project: Project) => void;
  };
};

/**
 * Evaluation Slice - Handles evaluation forms and scoring
 */
export type EvaluationSlice = {
  // State for each section
  [SectionType.CLIENT_PROFILE]: EvaluationCriteria[];
  [SectionType.FINANCIAL_CAPACITY]: EvaluationCriteria[];
  [SectionType.COLLABORATION]: EvaluationCriteria[];
  [SectionType.GROWTH_POTENTIAL]: EvaluationCriteria[];

  // Totals for each section
  sectionTotals: SectionTotals;

  // Overall score
  overallScore: OverallScore;

  // Score detail

  scoreDetail: Record<string, QAStrategistOutput> | null;

  // Actions
  actions: {
    initializeEvaluation: () => void;
    updateAnswer: (sectionType: SectionType, id: string, answer: string, selectedIndex?: number) => void;
    calculateSectionTotals: (sectionType: SectionType) => void;
    calculateAllTotals: () => void;
    getFinalOverallScore: () => void;
    getSectionData: (sectionType: SectionType) => EvaluationCriteria[];
    getAnswerList: (type: string) => IOptionsItem[];
    updateInitialEvaluationData: (data: QAStrategistOutput) => void;
    updateScoreDetail: (scoreDetail: StepQA<ScoreDetail, any> | null | undefined) => void;
  };
};

/**
 * Workflow Slice - Handles workflow management and navigation
 */
export type WorkflowSlice = {
  // State
  workflow: WorkflowStep[];
  currentTask: WorkflowStep | null;
  currentStep: WorkflowStep | null;
  isLoading: boolean;
  error: string | null;
  currentStepInfoIds: string;
  projectName: string;
  navigationCache?: Map<string, { taskId: string; stepId?: string } | null>;

  // templateList: TemplatesResponse;
  // Actions
  actions: {
    initializeWorkflow: (workflowData: WorkflowStep[]) => void;
    completeStep: (stepId: string, data?: any) => void;
    updateStepData: (stepId: string, data: any) => void;
    getStepById: (stepId: string) => WorkflowStep | null;
    initializeStepData: (stepId: string) => void;
    moveToNextStep: () => void;
    moveToPreviousStep: () => void;
    updateQAListData: (stepId: string, qaList: any[]) => void;
    updateStatus: (id: string, status: EStatusTask, isTask?: boolean) => void;
    updateCurrentStepInfoIds: (id: string) => void;
    setProjectName: (name: string) => void;
    clearNavigationCache: () => void;
    calculatePreviousStep: (currentTask: WorkflowStep, currentStep: WorkflowStep | null, workflow: WorkflowStep[]) => { taskId: string; stepId?: string } | null;
    calculateNextStep: (currentTask: WorkflowStep, currentStep: WorkflowStep | null, workflow: WorkflowStep[]) => { taskId: string; stepId?: string } | null;
    getNextStepId: () => string;
    moveToCurrentStep: (currentTaskId: string, currentStepId: string) => void;
  };
};

/**
 * Task Workflow Slice - Handles task workflow management (simplified)
 */
export type TaskWorkflowSlice = {
  // State
  tasks: Task[];
  selectedTask: Task | Step | null;
  sidebarVisible: boolean;

  // Actions
  actions: {
    setTasks: (tasks: Task[]) => void;
    setSelectedTask: (task: Task | Step | null) => void;
    setSidebarVisible: (visible: boolean) => void;
    updateTaskStatus: (taskId: string, status: TaskStatus) => void;
    updateStepStatus: (taskId: string, stepId: string, status: TaskStatus) => void;
    toggleSidebar: () => void;
    initializeTaskWorkflow: () => void;
  };
};

export type ClientAnalysisSlice = {
  clientFileUploaded: IFileResponse[];
  actions: {
    setClientFileUploaded: (uploaded: IFileResponse[]) => void;
  };
};

// ============================================================================
// COMBINED STORE INTERFACE
// ============================================================================

/**
 * Combined Project Store Shape - All slices combined
 */
export type ProjectWorkflowStoreShape =
  ProjectWorkflowSelectionSlice &
  WorkflowSlice &
  EvaluationSlice &
  TaskWorkflowSlice &
  ClientAnalysisSlice;

// ============================================================================
// STORE CREATOR TYPES
// ============================================================================

/**
 * State creator type for slices
 */
export type ProjectStoreStateCreator<T> = StateCreator<
  ProjectWorkflowStoreShape,
  [],
  [],
  T
>;

/**
 * Parameters for creating the project store
 */
export type createProjectWorkflowStoreParams = {
  projectId?: string;
};

/**
 * Project Store Provider Props
 */
export type ProjectWorkflowStoreProviderProps = {
  children: React.ReactNode;
  projectId?: string;
};

// ============================================================================
// HOOK SELECTOR TYPES
// ============================================================================

/**
 * Selector function type for hooks
 */
export type ProjectWorkflowStoreSelector<T> = (state: ProjectWorkflowStoreShape) => T;
