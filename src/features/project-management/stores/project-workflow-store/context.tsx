'use client';

import React, { createContext, useRef } from 'react';
import type { ProjectWorkflowStoreProviderProps } from './types';
import { createProjectWorkflowStore } from './store';

/**
 * React Context for the Project Store
 *
 * This context provides the Zustand store instance to components in the tree.
 */
type ProjectWorkflowStore = ReturnType<typeof createProjectWorkflowStore>;
export const ProjectStoreContext = createContext<ProjectWorkflowStore | null>(null);

// Provider component
export const ProjectWorkflowStoreProvider: React.FC<ProjectWorkflowStoreProviderProps> = ({
  children,
  // projectId,
}) => {
  // Use useRef to ensure the store is created only once
  // This prevents unnecessary re-creation on re-renders
  const storeRef = useRef<ProjectWorkflowStore | undefined>(undefined);

  // Create the store only if it doesn't exist
  if (!storeRef.current) {
    storeRef.current = createProjectWorkflowStore();
  }

  return (
    <ProjectStoreContext value={storeRef.current}>
      {children}
    </ProjectStoreContext>
  );
};
