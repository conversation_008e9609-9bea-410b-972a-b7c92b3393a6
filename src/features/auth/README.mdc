# Authentication Hook Documentation

This document provides comprehensive documentation for the `useAuth` hook, which centralizes all authentication functionality in a single, reusable hook.

## Features

- **Centralized Authentication**: All auth functionality in one place
- **React Query Integration**: Efficient data fetching, caching, and state management
- **Zustand State Management**: Global auth state accessible throughout the app
- **Robust Error Handling**: Comprehensive error handling with recovery strategies
- **Token Management**: Secure token storage and retrieval in cookies
- **Session Persistence**: Maintain authentication state across page refreshes
- **TypeScript Support**: Full type safety for all functions and parameters

## Installation

The hook is already integrated into the application. The required dependencies are:

- `@tanstack/react-query`: For data fetching and caching
- `zustand`: For state management
- `js-cookie`: For cookie management

## Basic Usage

```tsx
import { useAuth } from '@/core/auth/hooks/useAuth';

function MyComponent() {
  const {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout
  } = useAuth();

  const handleLogin = async (email: string, password: string) => {
    try {
      await login({ email, password });
      // Redirect happens automatically by default
    } catch (error) {
      // Error is handled by the hook, but you can add additional handling here
      console.error('Login failed:', error);
    }
  };

  const handleLogout = async () => {
    await logout();
    // Redirect happens automatically by default
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {isAuthenticated
        ? (
            <>
              <p>
                Welcome,
                {user?.email}
              </p>
              <button onClick={handleLogout}>Logout</button>
            </>
          )
        : (
            <LoginForm onSubmit={handleLogin} />
          )}
    </div>
  );
}
```

## Advanced Usage

### Custom Configuration

You can customize the behavior of the hook by passing options:

```tsx
const auth = useAuth({
  tokenKey: 'my_custom_token_key',
  tokenExpiration: 3600, // 1 hour
  loginRedirectPath: '/dashboard/home',
  logoutRedirectPath: '/login',
  autoRedirect: false, // Disable automatic redirects
});
```

### Manual Redirects

If you disable automatic redirects, you can handle them manually:

```tsx
const { login, isAuthenticated } = useAuth({
  autoRedirect: false,
});

const handleLogin = async (credentials) => {
  const result = await login(credentials);
  if (result.data) {
    // Custom redirect logic
    router.push('/custom-path');
  }
};
```

### Accessing User Data

The user data is available in the `user` property:

```tsx
const { user, isAuthenticated } = useAuth();

if (isAuthenticated && user) {
  console.log('User ID:', user.id);
  console.log('User Email:', user.email);
  console.log('User Roles:', user.roles);
}
```

### Handling Authentication Errors

The hook handles errors automatically, but you can also access the error state:

```tsx
const { error, login } = useAuth();

const handleLogin = async (credentials) => {
  try {
    await login(credentials);
  } catch (err) {
    // Additional error handling
    if (err.type === 'INVALID_CREDENTIALS') {
      // Show custom message
    }
  }
};

// Display error message from the hook
if (error) {
  return <div className="error">{error.message}</div>;
}
```

## API Reference

### Hook Return Values

| Property | Type | Description |
|----------|------|-------------|
| `user` | `User \| null` | The authenticated user or null if not authenticated |
| `isAuthenticated` | `boolean` | Whether the user is authenticated |
| `isLoading` | `boolean` | Whether an authentication operation is in progress |
| `error` | `AuthError \| null` | The current authentication error or null |
| `login` | `(credentials: LoginCredentials) => Promise<ApiResponse<LoginResponse>>` | Function to log in a user |
| `logout` | `() => Promise<void>` | Function to log out the current user |
| `refetchUser` | `() => Promise<ApiResponse<User>>` | Function to refresh the user data |
| `getToken` | `() => string \| null` | Function to get the current auth token |
| `checkAuthentication` | `() => boolean` | Function to check if the user is authenticated |

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `tokenKey` | `string` | `'access_token'` | Cookie name for storing the access token |
| `tokenExpiration` | `number` | `604800` (7 days) | Token expiration time in seconds |
| `tokenOptions` | `TokenOptions` | `{ secure: true, sameSite: 'lax' }` | Cookie options for the access token |
| `loginRedirectPath` | `string` | `'/dashboard/projects'` | Redirect path after successful login |
| `logoutRedirectPath` | `string` | `'/signin'` | Redirect path after logout |
| `autoRedirect` | `boolean` | `true` | Whether to automatically redirect after login/logout |
| `autoRefreshToken` | `boolean` | `false` | Whether to automatically refresh the token |

## Error Handling

The hook handles different types of authentication errors:

| Error Type | Description | Recovery Strategy |
|------------|-------------|-------------------|
| `INVALID_CREDENTIALS` | Invalid email or password | Show error message, allow retry |
| `NETWORK_ERROR` | Network connectivity issues | Show error message, suggest checking connection |
| `SERVER_ERROR` | Server-side errors | Show error message, suggest trying again later |
| `UNAUTHORIZED` | User is not authorized | Redirect to login page |
| `SESSION_EXPIRED` | User session has expired | Redirect to login page |
| `UNKNOWN` | Unspecified errors | Show generic error message |

## Implementation Details

The hook uses a combination of:

1. **Zustand Store**: For global state management
2. **React Query**: For data fetching and caching
3. **Cookies**: For token storage
4. **React Router**: For navigation

This architecture provides a robust, efficient, and type-safe authentication system that can be easily extended and customized.
