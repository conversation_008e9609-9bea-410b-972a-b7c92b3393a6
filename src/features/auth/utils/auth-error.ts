import type { AuthError, AuthErrorType } from '../types/auth.types';

/**
 * Map API errors to AuthError objects
 *
 * @param error - The error from the API
 * @returns An AuthError object
 */
export const mapApiError = (error: any): AuthError => {
  // Default error
  let authError: AuthError = {
    type: 'UNKNOWN' as AuthErrorType,
    message: 'An unknown error occurred',
    originalError: error,
  };

  // If it's an API response error
  if (error && error.statusCode) {
    authError.statusCode = error.statusCode;
    authError.message = error.message || 'Server error';

    // Map status codes to error types
    switch (error.statusCode) {
      case 401:
        authError.type = 'UNAUTHORIZED' as AuthErrorType;
        authError.message = 'Invalid credentials: Please log in again';
        break;
      case 403:
        authError.type = 'UNAUTHORIZED' as AuthErrorType;
        authError.message = 'Forbidden: You do not have permission to access this resource';
        break;
      case 400:
        // Check if it's an invalid credentials error
        if (error.message?.toLowerCase().includes('credentials')
          || error.message?.toLowerCase().includes('password')
          || error.message?.toLowerCase().includes('email')) {
          authError.type = 'INVALID_CREDENTIALS' as AuthErrorType;
          authError.message = 'Invalid email or password';
        } else {
          authError.type = 'SERVER_ERROR' as AuthErrorType;
        }
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        authError.type = 'SERVER_ERROR' as AuthErrorType;
        authError.message = 'Server error: Please try again later';
        break;
      default:
        authError.type = 'UNKNOWN' as AuthErrorType;
    }
  } else if (error instanceof Error) {
    // If it's a network error
    if (error.message.includes('Network') || error.message.includes('connection')) {
      authError = {
        type: 'NETWORK_ERROR' as AuthErrorType,
        message: 'Network error: Please check your internet connection',
        originalError: error,
      };
    } else {
      authError.message = error.message;
    }
  }

  return authError;
};
