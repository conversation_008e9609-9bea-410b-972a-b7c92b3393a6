import type { TokenOptions } from '../types/auth.types';
import Cookies from 'js-cookie';
import { COOKIE_SESSION_KEY, SESSION_EXPIRATION_SECONDS } from '../constant';

/**
 * Default token options
 */
const DEFAULT_TOKEN_OPTIONS: TokenOptions = {
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  expires: SESSION_EXPIRATION_SECONDS,
};

/**
 * Constants for token expiration management
 */
const TOKEN_EXPIRES_TIME_KEY = 'token_expires_time';
const ONE_DAY_IN_MS = 24 * 60 * 60 * 1000; // 1 day in milliseconds

/**
 * Set the access token in cookies and expiration time in localStorage
 *
 * @param token - The access token to store
 * @param options - Cookie options
 * @param tokenKey - The cookie name for the access token
 */
export const setToken = (
  token: string,
  options: TokenOptions = DEFAULT_TOKEN_OPTIONS,
  tokenKey: string = COOKIE_SESSION_KEY,
): void => {
  if (!token) {
    return;
  }

  // Calculate expiration date if expires is provided in seconds
  const expires = options.expires
    ? new Date(Date.now() + options.expires * 1000)
    : undefined;

  // Set the cookie
  Cookies.set(tokenKey, token, {
    secure: options.secure,
    sameSite: options.sameSite,
    expires,
  });

  // Set expiration time in localStorage (1 day from now)
  const expiresTime = Date.now() + ONE_DAY_IN_MS;
  if (typeof window !== 'undefined') {
    localStorage.setItem(TOKEN_EXPIRES_TIME_KEY, expiresTime.toString());
  }
};

/**
 * Get the access token from cookies
 *
 * @param tokenKey - The cookie name for the access token
 * @returns The access token or null if not found
 */
export const getToken = (tokenKey: string = COOKIE_SESSION_KEY): string | null => {
  return Cookies.get(tokenKey) || null;
};

/**
 * Remove the access token from cookies and expiration time from localStorage
 *
 * @param tokenKey - The cookie name for the access token
 */
export const removeToken = (tokenKey: string = COOKIE_SESSION_KEY): void => {
  Cookies.remove(tokenKey);

  // Remove expiration time from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TOKEN_EXPIRES_TIME_KEY);
  }
};

/**
 * Get token expiration time from localStorage
 *
 * @returns The expiration time in milliseconds or null if not found
 */
export const getTokenExpiresTime = (): number | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  const expiresTime = localStorage.getItem(TOKEN_EXPIRES_TIME_KEY);
  return expiresTime ? Number.parseInt(expiresTime, 10) : null;
};

/**
 * Check if token needs refresh (current time + 5 minutes > expiration time)
 *
 * @returns True if token needs refresh, false otherwise
 */
export const shouldRefreshToken = (): boolean => {
  const expiresTime = getTokenExpiresTime();

  if (!expiresTime) {
    return false;
  }

  const currentTime = Date.now();
  const fiveMinutesInMs = 5 * 60 * 1000; // 5 minutes in milliseconds

  return currentTime + fiveMinutesInMs > expiresTime;
};

/**
 * Get time until token refresh is needed (in milliseconds)
 *
 * @returns Time in milliseconds until refresh is needed, or null if no expiration time
 */
export const getTimeUntilRefresh = (): number | null => {
  const expiresTime = getTokenExpiresTime();
  if (!expiresTime) {
    return null;
  }

  const currentTime = Date.now();
  const fiveMinutesInMs = 5 * 60 * 1000; // 5 minutes in milliseconds
  const refreshTime = expiresTime - fiveMinutesInMs;

  return Math.max(0, refreshTime - currentTime);
};

/**
 * Check if the user is authenticated based on the presence of a token
 *
 * @param tokenKey - The cookie name for the access token
 * @returns True if authenticated, false otherwise
 */
export const isAuthenticated = (tokenKey: string = COOKIE_SESSION_KEY): boolean => {
  return !!getToken(tokenKey);
};

/**
 * Parse JWT token to get payload
 * Note: This is not a secure way to validate tokens, only for extracting info
 *
 * @param token - The JWT token
 * @returns The decoded payload or null if invalid
 */
export const parseToken = (token: string): any | null => {
  try {
    // Split the token and get the payload part (second part)
    const base64Url = token.split('.')[1];
    if (!base64Url) {
      return null;
    }

    // Convert base64url to base64
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

    // Decode and parse as JSON
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => `%${(`00${c.charCodeAt(0).toString(16)}`).slice(-2)}`)
        .join(''),
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error parsing token:', error);
    return null;
  }
};

/**
 * Check if token is expired
 *
 * @param token - The JWT token
 * @returns True if expired, false otherwise
 */
export const isTokenExpired = (token: string): boolean => {
  const payload = parseToken(token);
  if (!payload || !payload.exp) {
    return true;
  }

  // exp is in seconds, Date.now() is in milliseconds
  return payload.exp * 1000 < Date.now();
};
