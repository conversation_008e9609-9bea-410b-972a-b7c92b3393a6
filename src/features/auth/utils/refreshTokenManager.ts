import { refreshTokenApi } from '../services/auth.service';
import {
  getTimeUntilRefresh,
  getToken,
  removeToken,
  setToken,
  shouldRefreshToken,
} from './token';
import { COOKIE_SESSION_KEY, SESSION_EXPIRATION_SECONDS } from '../constant';

/**
 * Global refresh token manager
 * This runs independently of React components and will always execute when the app loads
 */
class RefreshTokenManager {
  private timeoutId: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private isRefreshing = false;
  private refreshPromise: Promise<void> | null = null;

  /**
   * Initialize the refresh token logic
   * This should be called when the app starts
   */
  public initialize(): void {
    // Prevent multiple initializations
    if (this.isInitialized) {
      return;
    }

    // Check if user has a token
    const token = getToken(COOKIE_SESSION_KEY);

    // If no token, don't call refresh
    if (!token) {
      console.log('No token found, skipping refresh logic');
      return;
    }

    // Check if token needs immediate refresh
    if (shouldRefreshToken()) {
      console.log('Token needs immediate refresh');
      this.renewToken();
    } else {
      // Schedule refresh for later
      this.scheduleRefresh();
    }

    this.isInitialized = true;
  }

  /**
   * Schedule the next token refresh
   */
  private scheduleRefresh(): void {
    const timeUntilRefresh = getTimeUntilRefresh();

    if (timeUntilRefresh !== null && timeUntilRefresh > 0) {
      console.log(`Scheduling token refresh in ${Math.round(timeUntilRefresh / 1000 / 60)} minutes`);

      // Clear any existing timeout
      this.clearTimeout();

      this.timeoutId = setTimeout(() => {
        this.renewToken();
      }, timeUntilRefresh);
    } else {
      console.log('No valid refresh time calculated');
    }
  }

  /**
   * Renew the access token with queue management
   */
  private async renewToken(): Promise<void> {
    // If already refreshing, return the existing promise
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    // Set refreshing state and create promise
    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      await this.refreshPromise;
    } finally {
      // Reset refreshing state
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<void> {
    try {
      console.log('Refreshing token...');
      const response = await refreshTokenApi();

      if (response.data?.accessToken) {
        // Store new token in cookies and update expiration time
        setToken(
          response.data.accessToken,
          {
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            expires: SESSION_EXPIRATION_SECONDS,
          },
          COOKIE_SESSION_KEY,
        );

        console.log('Token refreshed successfully');

        // Schedule next refresh
        this.scheduleRefresh();
      } else {
        throw new Error('No access token in refresh response');
      }
    } catch (error: any) {
      console.error('Token refresh failed:', error);

      // Clear auth state on refresh failure
      this.clearAuthState();

      if (typeof window !== 'undefined') {
        window.location.href = '/signin';
      }

      throw error; // Re-throw to handle in calling code
    }
  }

  /**
   * Clear authentication state
   */
  private clearAuthState(): void {
    removeToken(COOKIE_SESSION_KEY);
    this.clearTimeout();
    this.isInitialized = false;
  }

  /**
   * Clear any pending timeout
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * Ensure token is valid before making API calls
   * This method should be called before any API request
   */
  public async ensureValidToken(): Promise<void> {
    const token = getToken(COOKIE_SESSION_KEY);

    // If no token, nothing to refresh
    if (!token) {
      return;
    }

    // If token needs refresh, wait for it to complete
    if (shouldRefreshToken()) {
      await this.renewToken();
    }
  }

  /**
   * Check if currently refreshing token
   */
  public get isCurrentlyRefreshing(): boolean {
    return this.isRefreshing;
  }
}

// Create a singleton instance
export const refreshTokenManager = new RefreshTokenManager();
