'use client';

import { useEffect } from 'react';
import { refreshTokenManager } from '../utils/refreshTokenManager';

/**
 * Client-side component to initialize refresh token logic
 * This ensures the refresh token manager runs on the client side only
 */
export function RefreshTokenInitializer() {
  useEffect(() => {
    console.log('RefreshTokenInitializer: Initializing refresh token manager...');
    refreshTokenManager.initialize();
  }, []);

  // This component doesn't render anything
  return null;
}
