/**
 * Authentication related types
 */

/**
 * User profile information
 */
export type User = {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles?: UserRole[];
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any; // Allow for additional properties
};

/**
 * Authentication state
 */
export type AuthState = {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
};

/**
 * Login credentials
 */
export type LoginCredentials = {
  email: string;
  password: string;
};

/**
 * Login response from the API
 */
export type LoginResponse = {
  accessToken: string;
  user?: User;
};

/**
 * Authentication error types
 */
export enum AuthErrorType {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  UNKNOWN = 'UNKNOWN',
}

/**
 * Authentication error
 */
export type AuthError = {
  type: AuthErrorType;
  message: string;
  statusCode?: number;
  originalError?: any;
};

/**
 * Token management options
 */
export type TokenOptions = {
  secure?: boolean;
  httpOnly?: boolean;
  sameSite?: 'strict' | 'lax';
  expires?: number;
};

/**
 * Auth hook configuration options
 */
export type AuthOptions = {
  /**
   * Cookie name for storing the access token
   * @default 'access_token'
   */
  tokenKey?: string;

  /**
   * Token expiration time in seconds
   * @default 604800 (7 days)
   */
  tokenExpiration?: number;

  /**
   * Cookie options for the access token
   */
  tokenOptions?: TokenOptions;

  /**
   * Redirect path after successful login
   * @default '/dashboard'
   */
  loginRedirectPath?: string;

  /**
   * Redirect path after logout
   * @default '/signin'
   */
  logoutRedirectPath?: string;

  /**
   * Whether to automatically redirect after login/logout
   * @default true
   */
  autoRedirect?: boolean;

  /**
   * Whether to automatically refresh the token
   * @default false
   */
  autoRefreshToken?: boolean;
};

export type UserRole = {
  name: string;
};
