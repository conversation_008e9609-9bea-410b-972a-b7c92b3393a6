'use client';

import type { AuthError, AuthState, User } from '../types/auth.types';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

/**
 * Auth store actions interface
 */
type AuthActions = {
  /**
   * Set the authenticated user
   */
  setUser: (user: User | null) => void;

  /**
   * Set the authentication state
   */
  setAuthenticated: (isAuthenticated: boolean) => void;

  /**
   * Set the loading state
   */
  setLoading: (isLoading: boolean) => void;

  /**
   * Set the error state
   */
  setError: (error: AuthError | null) => void;

  /**
   * Reset the auth state (logout)
   */
  reset: () => void;
};

/**
 * Combined auth store type
 */
type AuthStore = AuthState & AuthActions;

/**
 * Initial auth state
 */
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

/**
 * Create the auth store with Zustand
 */
export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      set => ({
        // Initial state
        ...initialState,

        // Actions
        setUser: user => set({ user }),

        setAuthenticated: isAuthenticated => set({ isAuthenticated }),

        setLoading: isLoading => set({ isLoading }),

        setError: error => set({ error }),

        reset: () => set(initialState),
      }),
      {
        name: 'auth-storage',
        // Only persist non-sensitive data
        partialize: state => ({
          isAuthenticated: state.isAuthenticated,
        }),
      },
    ),
  ),
);
