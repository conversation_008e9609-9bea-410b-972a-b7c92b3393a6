import { useQuery } from '@tanstack/react-query';
import { getFrameworkList } from '../components/services';
import type { FrameworkFilters } from '../components/services';

export function useFrameworkGet(filters: FrameworkFilters = {}) {
  return useQuery({
    queryKey: ['getFramework', filters],
    queryFn: () => getFrameworkList(filters),
    select: response => response.data,
    enabled: true,
  });
}
