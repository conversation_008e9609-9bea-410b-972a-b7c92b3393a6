'use client';

import { useMutation } from '@tanstack/react-query';
import { deleteTemplate } from '../components/services';

export function useTemplateDelete() {
  // Get query client for cache invalidation
  // const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteTemplate(id),
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ['getFramework'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error: ', error);
    },
  });
}
