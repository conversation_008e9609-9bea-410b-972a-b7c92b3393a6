import { useCallback } from 'react';
import { useFrameworkStore } from '../store/useFrameworkStore';
import type { FrameworkFilters } from '../components/services';

/**
 * Hook for managing framework filters
 *
 * This hook provides a way to manage filters for framework listing.
 * It uses the framework store for state management.
 *
 * @returns Filter state and actions
 */
export function useFrameworkFilters() {
  // Get filter state and actions from the store
  const {
    searchValue,
    currentPage,
    setSearchValue,
    clearSearchValue,
    setCurrentPage,
    resetPagination,
  } = useFrameworkStore();

  // Check if any filters are active
  const hasActiveFilters = !!searchValue;

  // Prepare API filters based on UI state
  const apiFilters: FrameworkFilters = {
    searchQuery: searchValue || undefined,
    page: currentPage,
  };

  // Function to update a filter
  const updateFilter = useCallback((value: string) => {
    setSearchValue(value);
  }, [setSearchValue]);

  // Function to update page
  const updatePage = useCallback((page: number) => {
    setCurrentPage(page);
  }, [setCurrentPage]);

  // Function to clear all filters
  const clearFilters = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  return {
    // Filter state
    searchValue,
    currentPage,
    hasActiveFilters,
    apiFilters,

    // Filter actions
    updateFilter,
    updatePage,
    clearFilters,
    setSearchValue,
    clearSearchValue,
    setCurrentPage,
    resetPagination,
  };
}
