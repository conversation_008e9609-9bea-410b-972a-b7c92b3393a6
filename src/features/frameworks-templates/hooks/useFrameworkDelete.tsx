'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteFramework } from '../components/services';

export function useFrameworkDelete() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteFramework(id),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['getFramework'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error: ', error);
    },
  });
}
