'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createTemplateFramework } from '../components/services';
import type { TemplatePayload } from '../types';

export function useTemplateCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: TemplatePayload) => createTemplateFramework(payload),
    onSuccess: () => {
      // Invalidate all project-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['createTemplate'] });
      // toast.success('Project created successfully');
    },
    onError: (error) => {
      console.error('Error: ', error);
    },
  });
}
