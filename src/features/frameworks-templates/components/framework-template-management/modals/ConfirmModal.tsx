'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useTranslations } from 'next-intl';

type DeleteButtonType = {
  isOpen: boolean;
  title?: string;
  message?: string;
  closeModal: () => void;
  onConfirm?: () => void;
};

const ConfirmModal: React.FC<DeleteButtonType> = ({
  isOpen,
  title = 'Are you sure to delete?',
  message = 'All the documents within this company will be permanently deleted and cannot be undone once click Continue.',
  closeModal,
  onConfirm,
}) => {
  const t = useTranslations('framework');
  return (
    <>

      <Modal showCloseButton={false} isOpen={isOpen} onClose={closeModal} className="w-[450px]">
        <div className="no-scrollbar w-full p-6">
          <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
            {title}
          </h4>

          <p>{message}</p>

          <div className="flex items-center justify-end gap-4 mt-3">
            <Button
              type="button"
              variant="outline"
              onClick={closeModal}
            >
              {t('common.cancel')}
            </Button>

            <Button
              type="button"
              onClick={onConfirm}
            >
              {t('common.continue')}
            </Button>
          </div>
        </div>
      </Modal>
    </>

  );
};

export default ConfirmModal;
