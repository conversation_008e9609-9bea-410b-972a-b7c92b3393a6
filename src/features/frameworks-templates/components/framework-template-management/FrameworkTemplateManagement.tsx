'use client';

import { PlusIcon } from '@/shared/icons';
import { SearchBox } from '../SearchBox';
import TableManagement from './table-list/TableManagement';
import TemplateForm from './modals/TemplatesForm';
import { useModal } from '@/shared/hooks/useModal';
import { useFrameworkFilters } from '../../hooks/useFrameworkFilters';
import { useFrameworkGet } from '../../hooks/useFrameworkGet';
import { useTranslations } from 'next-intl';

const FrameworkAndTemplateManagement: React.FC = () => {
  const t = useTranslations('framework');
  const { isOpen, openModal, closeModal } = useModal();
  const { apiFilters } = useFrameworkFilters();
  const { isLoading } = useFrameworkGet(apiFilters);

  return (
    <div className="pt-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg mb-0">{t('title')}</h3>
        <PlusIcon onClick={openModal} className="h-6 w-6 cursor-pointer" />
      </div>

      <div className="mt-3">
        <SearchBox placeholder={t('searchPlaceholder')} isLoading={isLoading} />
      </div>

      <div className="overflow-auto">
        <div className="my-2">
          <TableManagement handleCreateFramework={openModal} />
        </div>
      </div>

      <TemplateForm isOpen={isOpen} closeModal={closeModal} />
    </div>
  );
};

export default FrameworkAndTemplateManagement;
