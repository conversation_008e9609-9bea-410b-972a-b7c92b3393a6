'use client';

import { teamMembersSkeletonVariants } from '@/features/team-members/components/team-members-list';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { TableCell, TableRow } from '@/shared/components/ui/table';
import { cn } from '@/shared/utils/utils';
import type { TableConfig } from './TableManagement';

type SkeletonTableProps = {
  tableConfig: TableConfig[];
  rows?: number;
};

/**
 * SkeletonTeamMember Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays skeleton placeholders for team members while loading.
 */
export function SkeletonTable({
  tableConfig,
  rows = 3,
}: SkeletonTableProps) {
  return (
    <>
      {[...Array.from({ length: rows })].map((_, i) => (
        <TableRow key={i}>
          <TableCell className="w-10">
            <Skeleton className={cn(teamMembersSkeletonVariants({ width: 'sm' }))} />
          </TableCell>
          {
            tableConfig.map((config, i) => (
              <TableCell key={config.tag + i} className={config?.className}>
                <Skeleton className={cn(teamMembersSkeletonVariants())} />
              </TableCell>
            ),
            )
          }
        </TableRow>
      ))}
    </>
  );
}
