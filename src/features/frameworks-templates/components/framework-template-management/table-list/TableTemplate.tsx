'use client';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import React, { useImperativeHandle, useState } from 'react';
import { SkeletonTable } from './SkeletonTable';
import { ArrowUpTrayIcon, CheckIcon, ExclamationTriangleIcon, FileEditIcon, LinkIcon, PlusIcon, TrashIcon, XIcon } from '@/shared/icons';
import Link from 'next/link';
import Label from '@/shared/components/form/Label';
import Input from '@/shared/components/form/input/InputField';
import Select from '@/shared/components/form/Select';
import type { TemplateDataTypeRef } from '../modals/TemplatesForm';
import { toast } from 'sonner';
import { http } from '@/core/http/http';
import type { IFileResponse } from '@/shared/types/global';
import { Env } from '@/core/config/Env';
import type { TableConfig } from './TableManagement';
import { useTranslations } from 'next-intl';

export type TableTemplateType = {
  templateList: any[];
  tableConfig: TableConfig[];
  backgroundColorClass?: string;
  isForm?: boolean;
  ref?: React.Ref<TemplateDataTypeRef>;
  onSaveTemplate?: (index: number, item: any) => void;
  onClickCancelTemplate?: (index: number, item: any) => void;
  deleteTemplate?: (index: number, item: any) => void;
};

const TableTemplates: React.FC<TableTemplateType> = ({
  templateList,
  tableConfig,
  backgroundColorClass = 'bg-gray-100',
  isForm,
  ref,
  onSaveTemplate,
  onClickCancelTemplate,
  deleteTemplate,
}) => {
  const t = useTranslations('framework');

  const stepList = [{
    value: '1',
    label: 'Step 1',
  }, {
    value: '2',
    label: 'Step 2',
  }, {
    value: '5',
    label: 'Step 5',
  }];

  const [isLoading, _setIsLoading] = useState<boolean>(false);

  const [templateListData, setTemplateListData] = useState<any[]>(() => templateList ?? []);

  const handleChangeSelection = () => { };

  const handleChangeText = (e: React.ChangeEvent<HTMLInputElement>, item: any, tag: string, index: number) => {
    const text = e.target.value;
    item[tag] = text;
    const templateList = [...templateListData];
    templateList[index] = { ...item };

    setTemplateListData(templateList);
  };

  const handleAddTemplate = () => {
    const initData = {
      template: '',
      step: '1',
      file: {
        url: '',
        name: '',
      },
      reportFile: {
        url: '',
        name: '',
      },
      status: 'new',
      id: `new-${templateListData.length}`,
    };

    setTemplateListData(prev => [...prev, initData]);
  };

  const getFileResponse = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const uploadResponse = http.post<IFileResponse>({
        url: '/files/upload',
        data: formData,
        options: {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      });

      const res = await uploadResponse;
      toast.success(t('fileUploaded'));

      return res;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const handleUploadFile = async (e: React.ChangeEvent<HTMLInputElement>, item: any, tag: string) => {
    const file = e.target.files?.[0] as File;
    toast.info(t('fileUploading'));
    const { data } = await getFileResponse(file);
    const templateList = [...templateListData];
    const index = templateListData.findIndex(t => t.id === item.id);
    if (index !== -1) {
      const itemSelected = templateList[index];
      itemSelected[tag] = {
        ...data,
        name: data?.originalname,
        url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${data?.key}`,
        type: file?.type,
      };

      templateList[index] = { ...itemSelected };

      setTemplateListData(templateList);
    }
  };

  const handleSaveTemplate = (index: number, item: any) => {
    const templateList = [...templateListData];

    if (!item.template) {
      toast.error(t('messageTemplateName'));
      return;
    }
    if (!item.reportFile?.name && !item.file?.name) {
      toast.error(t('emptyFile'));
      return;
    }

    item.status = 'old';

    templateList[index] = { ...item };

    setTemplateListData(templateList);
  };

  const handleDeleteTemplate = (index: number, item: any) => {
    if (deleteTemplate) {
      deleteTemplate(index, item);
      // return;
    }
    setTemplateListData(prev => prev.filter((_, i) => i !== index));
  };

  const toggleEditMode = (item: any) => {
    setTemplateListData(prev => prev.map(data =>
      data.id === item.id
        ? { ...data, isEdit: true }
        : data,
    ));
  };

  const handleOutSideSave = (index: number, item: any) => {
    if (onSaveTemplate) {
      onSaveTemplate(index, item);
    }

    setTemplateListData(prev => prev.map(data =>
      data.id === item.id
        ? { ...data, isEdit: false }
        : data,
    ));
  };

  const handleOutSideCancel = (index: number, item: any) => {
    if (onClickCancelTemplate) {
      onClickCancelTemplate(index, item);
    }

    setTemplateListData(templateList.map(data =>
      data.id === item.id
        ? { ...data, isEdit: false }
        : data,
    ));
  };

  const handleDeleteFile = (item: any, tag: string) => {
    const templateList = [...templateListData];
    const index = templateListData.findIndex(t => t.id === item.id);
    if (index !== -1) {
      const itemSelected = templateList[index];
      itemSelected[tag] = {
        url: '',
        name: '',
      };

      templateList[index] = { ...itemSelected };

      setTemplateListData(templateList);
    }
  };

  useImperativeHandle(ref, () => {
    return {
      onGetTemplate: () => {
        return templateListData.filter(t => t.status === 'old');
      },

      isSaveAll: () => {
        return templateListData.some(t => t.status === 'new');
      },
    };
  }, [templateListData]);

  return (
    <Table className="border border-solid rounded-xl max-h-100">
      <TableHeader className="bg-gray-300 rounded-t-xl">
        <TableRow>
          {tableConfig.map(config => (
            <TableHead key={config.tag} className={config?.className}>
              {config.label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className={`${backgroundColorClass} max-h-[400px]`}>
        {isLoading
          ? <SkeletonTable tableConfig={tableConfig} />
          : templateListData.map((item, index) => (
              <React.Fragment key={item.id || index}>
                {item.status === 'old'
                  ? (
                      <TableRow>
                        {tableConfig.map((config, i) => (
                          config.type === 'file'
                            ? (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {(item?.isEdit || isForm)
                                    ? (
                                        <div className="flex items-center gap-2 ">
                                          <Link href={item[config.tag]?.url}>

                                            <p className={`underline truncate ${config?.maxWidth} ${isForm && 'max-w-30'} mb-0 `}>{item[config.tag].name}</p>
                                          </Link>

                                          <Label className="mb-0 text-black" htmlFor={`upload-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer " />
                                          </Label>

                                          { item[config.tag]?.url && <XIcon onClick={() => handleDeleteFile(item, config.tag)} className="h-5 w-5 cursor-pointer text-red-500" />}

                                          <Input
                                            id={`upload-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e => handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>

                                      )
                                    : (
                                        <div className="flex items-center gap-2">
                                          {item[config.tag]?.url
                                            ? (
                                                <Link href={item[config.tag]?.url} className="flex items-center gap-1">
                                                  <LinkIcon className="h-5 w-5" />
                                                  <p className="m-0 underline cursor-pointer">{t('viewFile')}</p>
                                                </Link>
                                              )
                                            : (
                                                <>
                                                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
                                                  <p className="m-0 cursor-pointer">{t('noFile')}</p>
                                                </>
                                              )}
                                        </div>
                                      )}
                                </TableCell>
                              )
                            : config.type !== 'action'
                              ? (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {!item?.isEdit
                                      ? (
                                          <div className={`pl-2 shrink-0 w-full ${config?.minWidth} ${isForm && 'min-w-80'}`}>
                                            {item[config.tag]}
                                          </div>
                                        )
                                      : (
                                          config.tag === 'step'
                                            ? (
                                                <Select
                                                  options={stepList}
                                                  onChange={handleChangeSelection}
                                                  defaultValue={item[config.tag] || ''}
                                                  isHiddenPlaceHolder={true}
                                                />
                                              )
                                            : (config.tag === 'template'
                                                ? (
                                                    <Input
                                                      id={`input-${item.id}-${config.tag}`}
                                                      type="text"
                                                      onChange={
                                                        e => handleChangeText(e, item, config.tag, index)
                                                      }
                                                      defaultValue={item[config.tag]}
                                                      className={`bg-white shrink-0 w-full ${config?.minWidth} ${config?.minWidth}  ${isForm && 'min-w-80'}`}
                                                    />

                                                  )
                                                : item[config.tag]
                                              )
                                        )}
                                  </TableCell>
                                )

                              : (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {!item?.isEdit
                                      ? (
                                          <div className="flex items-center gap-2 justify-end">
                                            <TrashIcon onClick={() => handleDeleteTemplate(index, item)} className="h-5 w-5 cursor-pointer text-red-500" />
                                            {!isForm && <FileEditIcon onClick={() => toggleEditMode(item)} className="h-5 w-5 cursor-pointer" />}
                                          </div>
                                        )
                                      : (
                                          <div className="flex items-center gap-2 justify-end">
                                            <CheckIcon onClick={() => handleOutSideSave(index, item)} className="h-5 w-5 cursor-pointer text-green-500" />
                                            <XIcon onClick={() => handleOutSideCancel(index, item)} className="h-5 w-5 cursor-pointer text-red-500" />
                                          </div>
                                        )}
                                  </TableCell>
                                )
                        ))}
                      </TableRow>
                    )
                  : (
                      <TableRow>
                        {tableConfig.map((config, i) => (
                          config.type === 'file'
                            ? (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {(item[config.tag].name)
                                    ? (
                                        <div className="flex items-center gap-2 ">
                                          <Link href={item[config.tag].url}>

                                            <p className={`underline truncate ${isForm && 'max-w-30'} mb-0 `}>{item[config.tag].name}</p>
                                          </Link>

                                          <Label className="mb-0 text-black" htmlFor={`upload-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer" />
                                          </Label>

                                          { item[config.tag]?.url && <XIcon onClick={() => handleDeleteFile(item, config.tag)} className="h-5 w-5 cursor-pointer text-red-500" />}

                                          <Input
                                            id={`upload-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e => handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>

                                      )
                                    : (
                                        <div className="flex items-center gap-2">
                                          <p className="m-0 underline cursor-pointer">{t('browserFile')}</p>

                                          <Label className="mb-0 text-black" htmlFor={`upload-new-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer" />
                                          </Label>

                                          <Input
                                            id={`upload-new-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e =>
                                              handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>
                                      )}
                                </TableCell>
                              )
                            : config.type !== 'action'
                              ? (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {
                                      config.tag === 'step'
                                        ? (
                                            <Select
                                              options={stepList}
                                              onChange={handleChangeSelection}
                                              defaultValue={item[config.tag] || ''}
                                              isHiddenPlaceHolder={true}
                                            />
                                          )
                                        : (config.tag === 'template'
                                            ? (
                                                <Input
                                                  id={`input-${item.id}-${config.tag}`}
                                                  type="text"
                                                  onChange={e => handleChangeText(e, item, config.tag, index)}
                                                  defaultValue={item[config.tag]}
                                                  className={`bg-white shrink-0 w-full ${config?.minWidth} ${isForm && 'min-w-80'}`}
                                                />
                                              )
                                            : item[config.tag]

                                          )
                                    }
                                  </TableCell>
                                )

                              : (
                                  <TableCell key={config.tag + i} className={config?.className}>

                                    <div className="flex items-center gap-2 justify-end">
                                      <CheckIcon onClick={() => handleSaveTemplate(index, item)} className="h-5 w-5 cursor-pointer text-green-500" />
                                      <XIcon onClick={() => handleOutSideCancel(index, item)} className="h-5 w-5 cursor-pointer text-red-500" />
                                    </div>

                                  </TableCell>
                                )
                        ))}
                      </TableRow>
                    )}

              </React.Fragment>
            ))}
        {isForm && (
          <TableRow className="sticky bottom-0" onClick={handleAddTemplate}>
            <TableCell colSpan={tableConfig.length} className="cursor-pointer bg-gray-200">
              <div className="flex items-center justify-center">
                <PlusIcon className="w-5 h-5" />
                {t('add')}
              </div>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default TableTemplates;
