'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useTranslations } from 'next-intl';

type DeleteButtonType = {
  isOpen: boolean;
  message?: string;
  closeModal: () => void;
  onConfirm?: () => void;
};

const DeleteModalButton: React.FC<DeleteButtonType> = ({
  isOpen,
  message,
  closeModal,
  onConfirm,
}) => {
  const t = useTranslations('framework');
  return (
    <>

      <Modal showCloseButton={false} isOpen={isOpen} onClose={closeModal} className="w-[450px]">
        <div className="no-scrollbar w-full p-6">
          <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
            {t('messageTitle')}
          </h4>

          <p>{message ?? t('messageDefault')}</p>

          <div className="flex items-center justify-end gap-4 mt-3">
            <Button
              type="button"
              variant="outline"
              onClick={closeModal}
            >
              {t('common.cancel')}
            </Button>

            <Button
              type="button"
              onClick={onConfirm}
            >
              {t('common.continue')}
            </Button>
          </div>
        </div>
      </Modal>
    </>

  );
};

export default DeleteModalButton;
