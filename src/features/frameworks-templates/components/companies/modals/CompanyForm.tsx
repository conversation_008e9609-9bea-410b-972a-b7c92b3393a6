'use client';

import { createCompanyForm } from '@/features/frameworks-templates/validations/framework-template.validation';
import type { CreateCompanyPayload } from '@/features/frameworks-templates/validations/framework-template.validation';
import { TextArea } from '@/shared/components/form/input';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { PlusIcon } from '@/shared/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';

const CompanyForm: React.FC = () => {
  const { isOpen, openModal, closeModal } = useModal();

  const defaultValues = useMemo(() => ({
    name: '',
    description: '',
  }), []);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateCompanyPayload>({
    resolver: zodResolver(createCompanyForm),
    defaultValues,
  });

  const closeModalForm = () => {
    reset();
    closeModal();
  };

  const onSubmit = (data: CreateCompanyPayload) => {
    console.log(data);
  };
  return (
    <>
      <PlusIcon onClick={openModal} className="h-6 w-6 cursor-pointer" />

      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[600px] min-w-[580px]">
        <div className="no-scrollbar w-full p-6">
          <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
            Create company
          </h4>
          <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
            Please enter all the required information to proceed.
          </p>
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Company Name */}
            <div className="col-span-2">
              <Label htmlFor="name">
                Company name
                {' '}
                <span className="text-error-500">*</span>
              </Label>
              <Controller
                control={control}
                name="name"
                render={({ field }) => (
                  <Input
                    id="name"
                    {...field}
                    placeholder="Company name"
                    type="text"
                    error={!!errors.name}
                    hint={errors.name?.message}
                  />
                )}
              />
            </div>

            {/* Description */}
            <div className="col-span-2 mt-4">
              <Label htmlFor="description">
                Description
              </Label>
              <Controller
                control={control}
                name="description"
                render={({ field }) => (
                  <TextArea
                    {...field}
                    value={field.value}
                    placeholder="Type your description here"
                    rows={3}
                  />
                )}
              />
            </div>

          </form>

          <div className="flex items-center justify-end gap-4 mt-3">
            <Button
              type="button"
              variant="outline"
              onClick={closeModalForm}
            >
              Cancel
            </Button>

            <Button
              type="button"
              onClick={handleSubmit(onSubmit)}
            >
              Create
            </Button>
          </div>
        </div>

      </Modal>
    </>

  );
};

export default CompanyForm;
