import { SearchBox } from '../SearchBox';
import CompanyBox from './CompanyBox';
import CompanyForm from './modals/CompanyForm';

const CompanyList: React.FC = () => {
  return (
    <div className="pt-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg mb-0">Companies</h3>
        <CompanyForm />
      </div>

      <div className="mt-3">
        <SearchBox placeholder="Search company" />
      </div>

      <div className="overflow-auto">
        <div className="my-2">
          <CompanyBox title="Mibrand" description="Company description mockup" isSelected={true} />
        </div>
      </div>
    </div>
  );
};

export default CompanyList;
