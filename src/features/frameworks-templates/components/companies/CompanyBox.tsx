'use client';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { ShowMoreIcon } from '@/shared/icons';
import { useState } from 'react';
import DeleteModalButton from './modals/DeleteModalButton';
import { useModal } from '@/shared/hooks/useModal';

type CompanyBoxType = {
  title: string;
  description: string;
  isSelected: boolean;
};

const CompanyBox: React.FC<CompanyBoxType> = ({ title, description, isSelected }) => {
  const [isPopoverOpen, setIsPopoverOpen] = useState<boolean> (false);

  const { isOpen, openModal, closeModal } = useModal();

  const handleOpenModal = () => {
    setIsPopoverOpen(false);
    openModal();
  };

  return (
    <div className={`flex items-center justify-between gap-2 py-3 px-2 bg-gray-100 border  rounded-xl ${isSelected ? 'border-gray-500' : 'border-transparent'}`}>
      <div className="">
        <div className="mb-1">{title}</div>
        <p className="text-sm truncate mb-0">{description}</p>
      </div>

      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger>
          <ShowMoreIcon className="h-5 w-5 cursor-pointer" />
        </PopoverTrigger>
        <PopoverContent className="w-35 p-2">
          <div className="">
            <div className="p-1 cursor-pointer text-sm hover:bg-gray-50">Edit</div>
            <div onClick={handleOpenModal} className="p-1 cursor-pointer text-sm text-red-500 hover:bg-gray-100">Delete</div>
          </div>
        </PopoverContent>
      </Popover>
      <DeleteModalButton isOpen={isOpen} closeModal={closeModal} />
    </div>
  );
};

export default CompanyBox;
