'use client';
import { useTranslations } from 'next-intl';
import FrameworkAndTemplateManagement from '../framework-template-management/FrameworkTemplateManagement';

const FrameworksAndTemplatesWrapper: React.FC = () => {
  const t = useTranslations('framework');
  return (
    <div className="py-4 px-6">
      <h2 className="text-xl font-medium text-foreground">
        {t('header')}
      </h2>
      <p>
        {t('description')}
      </p>

      <div className="flex mt-4 gap-6">
        {/* <div className="flex-3 shrink-0">
          <CompanyList />
        </div> */}
        <div className="flex-10 shrink-0">
          <FrameworkAndTemplateManagement />
        </div>
      </div>
    </div>
  );
};

export default FrameworksAndTemplatesWrapper;
