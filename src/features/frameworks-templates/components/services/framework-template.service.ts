import { http } from '@/core/http/http';
import type { FrameworkPayload, FrameworkResponseType, ItemFrameworkResponse, TemplatePayload, TemplateResponse } from '../../types';
import type { ApiResponse } from '@/shared/types/api-response';

/**
 * Creates a new template
 */
export async function createTemplateFramework(data: TemplatePayload): Promise<ApiResponse<TemplateResponse>> {
  return await http.post<TemplateResponse>({
    url: '/templates/framework',
    data,
  });
}

/**
 * Creates a new Framework
 */
export async function createFramework(data: FrameworkPayload): Promise<ApiResponse<boolean>> {
  return await http.post<boolean>({
    url: '/frameworks',
    data,
  });
}

export type FrameworkFilters = {
  searchQuery?: string;
  page?: number;
  limit?: number;
  mark?: boolean;
};

/**
 * Builds query parameters for framework API requests
 */
const buildFrameworkQueryParams = (filters: FrameworkFilters = {}): string => {
  const params: string[] = [];

  if (filters.searchQuery !== undefined) {
    params.push(`searchValue=${encodeURIComponent(filters.searchQuery)}`);
  }

  if (filters.page !== undefined) {
    params.push(`page=${filters.page}`);
  }

  if (filters.limit !== undefined) {
    params.push(`itemsPerPage=${filters.limit}`);
  }

  return params.length > 0 ? `?${params.join('&')}` : '';
};

export async function getFrameworkList(filters: FrameworkFilters = {}): Promise<ApiResponse<FrameworkResponseType>> {
  const queryString = buildFrameworkQueryParams(filters);
  return await http.get<FrameworkResponseType>({ url: `/frameworks${queryString}` });
}

export async function deleteFramework(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/frameworks/${id}` });
}

export async function getFrameworkDetail(id: string): Promise<ApiResponse<ItemFrameworkResponse>> {
  return await http.get<ItemFrameworkResponse>({ url: `/frameworks/${id}` });
}

export async function updateFramework(data: FrameworkPayload, id: string): Promise<ApiResponse<boolean>> {
  return await http.patch<boolean>({
    url: `/frameworks/${id}`,
    data,
  });
}

export async function deleteTemplate(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/templates/${id}` });
}
