import { create } from 'zustand';

type FrameworkStore = {
  // Search state
  searchValue: string;

  // Pagination state
  currentPage: number;

  // Search actions
  setSearchValue: (value: string) => void;
  clearSearchValue: () => void;

  // Pagination actions
  setCurrentPage: (page: number) => void;
  resetPagination: () => void;
};

/**
 * Framework store for managing framework-related state
 *
 * This store manages the state for framework filtering and search functionality.
 * It follows the same pattern as other feature stores in the application.
 */
export const useFrameworkStore = create<FrameworkStore>(set => ({
  // Initial state
  searchValue: '',
  currentPage: 1,

  // Actions
  setSearchValue: (value: string) => set({ searchValue: value, currentPage: 1 }), // Reset to page 1 when searching
  clearSearchValue: () => set({ searchValue: '', currentPage: 1 }),

  // Pagination actions
  setCurrentPage: (page: number) => set({ currentPage: page }),
  resetPagination: () => set({ currentPage: 1 }),
}));
