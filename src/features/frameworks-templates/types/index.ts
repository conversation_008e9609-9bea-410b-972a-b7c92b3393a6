import type { IFileResponse } from '@/shared/types/global';

export type FrameworkAndTemplatePayload = {
  name: string;
  description: string;
  frameworkIds: string[];
};

export type TemplatePayload = {
  id?: string;
  types: TemplateTypesPayload[];
  type: string;
  name: string;
  fileIds: string[];
};

export type TemplateTypesPayload = {
  category: 'questionnaire' | 'report';
  file: FormData;
  [key: string]: any;
};

export type TemplateResponse = {
  id: string;
  name: string;
  type: 'framework';
  files: (File & { file: string; category: 'report' | 'questionnaire' }) [];
};

export type FrameworkPayload = {
  id?: string;
  name: string;
  templateIds: string[];
};

export type FrameworkResponseType = {
  items: ItemFrameworkResponse[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type ItemFrameworkResponse = {
  name: string;
  templates: TemplateItemFramework[];
  id: string;
  createdAt: string;
  updatedAt: string;
};

export type TemplateItemFramework = {
  name: string;
  type: string;
  id: string;
  files: FileTemplateItemFramework[];
};

export type FileTemplateItemFramework = {
  type: string;
  category: string;
  file: IFileResponse;
};
