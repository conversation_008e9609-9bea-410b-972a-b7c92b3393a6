'use client';

import React, { createContext, useRef } from 'react';
import { createQuestionnaireStore } from './store';
import type { QuestionnaireStoreProviderProps } from './types';

/**
 * React Context for the Project Store
 *
 * This context provides the Zustand store instance to components in the tree.
 */
type QuestionnaireStore = ReturnType<typeof createQuestionnaireStore>;
export const QuestionnaireStoreContext = createContext<QuestionnaireStore | null>(null);

// Provider component
export const QuestionnaireStoreProvider: React.FC<QuestionnaireStoreProviderProps> = ({
  children,
  // projectId,
}) => {
  // Use useRef to ensure the store is created only once
  // This prevents unnecessary re-creation on re-renders
  const storeRef = useRef<QuestionnaireStore | undefined>(undefined);

  // Create the store only if it doesn't exist
  if (!storeRef.current) {
    storeRef.current = createQuestionnaireStore();
  }

  return (
    <QuestionnaireStoreContext value={storeRef.current}>
      {children}
    </QuestionnaireStoreContext>
  );
};
