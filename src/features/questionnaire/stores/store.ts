import { createStore, useStore as useZustandStore } from 'zustand';
import type { QuestionnaireStoreSelector, QuestionnaireStoreShape } from './types';
import { use } from 'react';
import { QuestionnaireStoreContext } from './context';
import { createQuestionnaireSlice } from './slices/createQuestionnaireSlice';

/**
 * Creates a Project Store with all slices combined
 *
 * This function creates a Zustand store that combines all project management slices:
 * - WorkflowSlice: Handles workflow management and navigation
 * - TaskWorkflowSlice: Handles simplified task workflow
 * - EvaluationSlice: Handles evaluation forms and scoring
 *
 * The store follows the slice pattern from the Dify workflow store implementation,
 * allowing for modular, composable state management with proper TypeScript support.
 *
 * @returns A Zustand store instance with all project management functionality
 */
export const createQuestionnaireStore = () => {
  return createStore<QuestionnaireStoreShape>((...args) => {
    const questionnaireSlice = createQuestionnaireSlice(...args);
    return {
      ...questionnaireSlice,
      actions: {
        ...questionnaireSlice.actions,
      },
    };
  });
};

export function useQuestionnaireSelector<T>(selector: QuestionnaireStoreSelector<T>): T {
  // Get the store instance from context
  const storeContext = use(QuestionnaireStoreContext);

  if (!storeContext) {
    throw new Error(
      'useQuestionnaireStoreContext must be used within a QuestionnaireStoreProvider. '
      + 'Make sure to wrap your component tree with <QuestionnaireStoreProvider>.',
    );
  }

  // Use Zustand's useStore hook with the selector
  return useZustandStore(storeContext, selector);
}

export const useQuestionnaireStore = () => {
  return use(QuestionnaireStoreContext);
};
