import { handleAddFieldNameInData } from '../../utils';
import type { QuestionnaireSlice, QuestionnaireStoreStateCreator } from '../types';

export const createQuestionnaireSlice: QuestionnaireStoreStateCreator<QuestionnaireSlice> = (set, _get) => ({

  id: '',

  questionnaire: null,

  actions: {
    initializeQuestionnaire: (questionnaire) => {
      set(() => {
        const sectionConvert = questionnaire.sections.map((section, i) => ({
          ...section,
          questions: handleAddFieldNameInData(section, i),
        }));
        const questionnaireConvert = {
          ...questionnaire,
          sections: [...sectionConvert],
        };
        return { questionnaire: questionnaireConvert };
      });
    },
  },
});
