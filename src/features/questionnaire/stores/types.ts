import type { StateCreator } from 'zustand';
import type { QuestionnaireResponse } from '../types/questionnaire';

export type QuestionnaireSlice = {
  id: string;

  questionnaire: QuestionnaireResponse | null;

  actions: {
    initializeQuestionnaire: (questionnaire: QuestionnaireResponse) => void;
  };
};

export type QuestionnaireStoreProviderProps = {
  children: React.ReactNode;

};

export type QuestionnaireStoreShape = QuestionnaireSlice;

export type QuestionnaireStoreSelector<T> = (state: QuestionnaireStoreShape) => T;

export type QuestionnaireStoreStateCreator<T> = StateCreator<
  QuestionnaireStoreShape,
  [],
  [],
  T
>;
