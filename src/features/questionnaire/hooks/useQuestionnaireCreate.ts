'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { submitQuestionnaire } from '../services/questionnaire.service';
import type { QuestionnairePayload } from '../types/questionnaire';
import { toast } from 'sonner';

export function useCreateQuestionnaire() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: QuestionnairePayload) => submitQuestionnaire(payload),
    onSuccess: (res: any) => {
      queryClient.invalidateQueries({ queryKey: ['createQuestionnaire'] });
      toast.success(res.message);
    },
    onError: (error) => {
      console.error('Error creating questionnaire:', error);
    },
  });
}
