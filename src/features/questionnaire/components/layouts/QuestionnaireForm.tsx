'use client';

import React, { useImperative<PERSON><PERSON><PERSON>, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';
import Checkbox from '@/shared/components/form/input/Checkbox';
import { QuestionType } from '../../types/questionnaire';
import type { OptionsExtend, QuestionInSectionExtend } from '../../types/questionnaire';
import { generateDefaultValuesForm, updateOptionValue, updateSubQuestionValue } from '../../utils';

export type QuestionnaireFormTypeRef = {
  onSubmitForm: () => void;
};

type QuestionnaireFormType = {
  questions: QuestionInSectionExtend[];
  ref?: React.Ref<QuestionnaireFormTypeRef>;
  isDisabled?: boolean;
};

type QuestionnaireFormDataExtend = {
  [key: string]: any;
};

const QuestionnaireForm: React.FC<QuestionnaireFormType> = ({ questions, ref, isDisabled = false }) => {
  const formDefaultValues = useMemo(() => {
    if (!questions.length) {
      return {};
    }
    return generateDefaultValuesForm(questions);
  }, [questions]);

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<QuestionnaireFormDataExtend>({
    defaultValues: formDefaultValues,
    mode: 'onSubmit',
  });

  const getPayloadData = (data: { [key: string]: any }) => {
    const questionsConvert = [...questions];

    const applyValueForm = (questions: any) => {
      return questions.map((question: QuestionInSectionExtend) => ({
        ...question,
        ...((question.type === QuestionType.TEXT) && { answer: data[question.name] }),
        ...(question.options.length && { options: updateOptionValue(question, question.options, data) }),
        ...(question.subQuestions && question.subQuestions.length && { subQuestions: updateSubQuestionValue(question, question.subQuestions, data) }),
      }),
      );
    };
    return applyValueForm(questionsConvert);
  };

  const onSubmit = (data: any) => {
    const payload = getPayloadData(data);
    return payload;
  };

  useImperativeHandle(ref, () => {
    return {
      onSubmitForm: () => {
        const data = getValues();
        const payload = getPayloadData(data);
        return payload;
      },
    };
  }, []);
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4">
      {
        questions?.map((question, index) => (
          <div key={index}>
            {/* Type: Text */}
            { question.type === QuestionType.TEXT && (
              <div>
                <Label htmlFor="fullName">{question.title}</Label>
                <Controller
                  name={question.name}
                  control={control}
                  render={({ field }) => (
                    <Input
                      id={question.name}
                      type="text"
                      placeholder=""
                      value={field.value || ''}
                      onChange={field.onChange}
                      error={!!errors[question.name]}
                      disabled={isDisabled}
                      className="bg-white"
                    />
                  )}
                />
              </div>
            )}

            {/* Type: Radio */}
            {question.type === QuestionType.RADIO && (
              <div>
                <Label>{question.title}</Label>
                <Controller
                  name={question.name}
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-col gap-4 mt-2">
                      {question.options.map(site => (
                        <Radio
                          key={site.name}
                          id={site.name}
                          name={question.name}
                          value={site.title}
                          checked={field.value === site.title}
                          onChange={(value) => {
                            if (!isDisabled) {
                              field.onChange(value);
                              site.isSelected = true;
                            }
                          }}
                          label={site.title}
                          disabled={isDisabled}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>
            )}

            {/* Type: Checkbox */}
            {question.type === QuestionType.CHECKBOX && (
              <div>
                <Label>{question.title}</Label>
                <Controller
                  name={question.name}
                  control={control}
                  render={({ field }) => {
                    const handleCheckboxChange = (option: OptionsExtend, checked: boolean) => {
                      if (isDisabled) {
                        return;
                      }
                      const currentValues = field.value || [];
                      option.isSelected = checked;
                      if (checked) {
                        field.onChange([...currentValues, option.title]);
                      } else {
                        field.onChange(currentValues.filter((value: string) => value !== option.title));
                      }
                    };

                    return (
                      <div className="space-y-3 mt-2">
                        {question.options.map(option => (
                          <div key={option.name}>
                            {!option.isCustom
                              ? (
                                  <Checkbox
                                    id={option.name}
                                    label={option.title}
                                    checked={option.isSelected && (field.value ?? []).includes(option.title)}
                                    onChange={checked => handleCheckboxChange(option, checked)}
                                    disabled={isDisabled}
                                  />
                                )
                              : (
                            // Other Option
                                  <div className="flex items-start gap-3">
                                    <Controller
                                      name={question.name}
                                      control={control}
                                      render={() => (
                                        <Checkbox
                                          id={option.name}
                                          classNameLabel="shrink-0"
                                          label={option.title}
                                          checked={option.isSelected && (field.value ?? []).includes(option.title)}
                                          onChange={checked => handleCheckboxChange(option, checked)}
                                          disabled={isDisabled}
                                        />
                                      )}
                                    />
                                    <Controller
                                      name={option.name}
                                      control={control}
                                      render={({ field: otherField }) => (
                                        <Input
                                          type="text"
                                          value={otherField.value || ''}
                                          onChange={otherField.onChange}
                                          classNameParent="w-full"
                                          className="
                                                flex-1
                                                p-0
                                                h-[unset]
                                                w-full
                                                border-0
                                                border-b
                                                border-solid
                                                rounded-none
                                                bg-white
                                                focus:shadow-[none]
                                                focus:border-black
                                                hover:border-b-[1px]
                                                hover:border-black
                                                "
                                          error={!!errors[option.name]}
                                          disabled={isDisabled}
                                        />
                                      )}
                                    />
                                  </div>
                                )}
                          </div>
                        ),
                        )}

                      </div>
                    );
                  }}
                />
              </div>
            )}

            {/* Type: Evaluation */}
            {question.type === QuestionType.EVALUATION && (
              <div>
                <Label>{question.title}</Label>
                <Controller
                  name={question.name}
                  control={control}
                  render={({ field }) => {
                    const levels = (question?.subQuestions && question?.subQuestions[0]?.options) ?? [];

                    const handleChangeData = (option: OptionsExtend, data: string, subQuestion: QuestionInSectionExtend) => {
                      if (isDisabled) {
                        return;
                      }
                      const currentValues = field.value || {};

                      subQuestion.options.forEach((opt) => {
                        opt.isSelected = opt.name === option.name;
                      });

                      field.onChange({
                        ...currentValues,
                        [option.name]: data,
                      });
                    };

                    return (
                      <div className="mt-4">
                        {/* Table with Sticky First Column */}
                        <div className="overflow-x-auto border border-gray-300 rounded-lg">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="sticky left-0 z-10 bg-gray-50 border-r border-gray-300 p-3 text-left font-medium text-gray-700 min-w-[300px] shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">

                                </th>
                                {levels.map(level => (
                                  <th key={level.name} className="border-l border-gray-300 p-3 text-center font-medium text-gray-700 min-w-[120px] whitespace-nowrap">
                                    {level.title}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {(question.subQuestions ?? []).map(subs => (
                                <tr key={subs.name} className="hover:bg-gray-50 border-t border-gray-300">
                                  <td className="sticky left-0 z-10 bg-white hover:bg-gray-50 border-r border-gray-300 p-3 text-sm text-gray-700 min-w-[300px] shadow-[2px_0_5px_-2px_rgba(0,0,0,0.1)]">
                                    {subs.title}
                                  </td>
                                  {subs.options.map(level => (
                                    <td key={level.name} className="border-l border-gray-300 p-3 text-center min-w-[120px]">
                                      <div className="flex justify-center">
                                        <Radio
                                          id={level.name}
                                          name={level.name}
                                          value={level.title}
                                          checked={level.isSelected && (field.value ?? {})[level.name] === level.title}
                                          onChange={(value) => {
                                            handleChangeData(level, value, subs);
                                          }}
                                          label=""
                                          className="justify-center"
                                          disabled={isDisabled}
                                        />
                                      </div>
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    );
                  }}
                />
              </div>
            )}

            {/* Type: Category */}
            {question.type === QuestionType.CATEGORY && (
              <div>
                <Label>{question.title}</Label>
                <Controller
                  name={question.name}
                  control={control}
                  render={({ field }) => {
                    const handleCheckboxChange = (option: OptionsExtend, checked: boolean) => {
                      if (isDisabled) {
                        return;
                      }
                      const currentValues = field.value || [];
                      option.isSelected = checked;
                      if (checked) {
                        field.onChange([...currentValues, option.title]);
                      } else {
                        field.onChange(currentValues.filter((value: string) => value !== option.title));
                      }
                    };

                    return (
                      <div className="mt-4 space-y-6">
                        {(question.subQuestions ?? []).map(category => (
                          <div key={category.name}>
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 mb-3">{category.title}</h4>
                              <div className="space-y-2">
                                {category.options.map(option => (
                                  !option.isCustom
                                    ? (
                                        <Checkbox
                                          key={option.name}
                                          id={option.name}
                                          label={option.title}
                                          checked={option.isSelected}
                                          onChange={checked => handleCheckboxChange(option, checked)}
                                          disabled={isDisabled}
                                        />
                                      )
                                    : (
                                        <div key={option.name} className="flex items-start gap-3 mt-3">
                                          <Controller
                                            name={option.name}
                                            control={control}
                                            render={() => (
                                              <Checkbox
                                                id={option.name}
                                                label={option.title}
                                                classNameLabel="shrink-0"
                                                checked={option.isSelected}
                                                onChange={checked => handleCheckboxChange(option, checked)}
                                                disabled={isDisabled}
                                              />
                                            )}
                                          />
                                          <Controller
                                            name={option.name}
                                            control={control}
                                            render={({ field: otherField }) => (
                                              <Input
                                                type="text"
                                                value={otherField.value || ''}
                                                onChange={otherField.onChange}
                                                classNameParent="w-full"
                                                className="
                                                      flex-1
                                                      p-0
                                                      h-[unset]
                                                      w-full
                                                      border-0
                                                      border-b
                                                      border-solid
                                                    bg-white
                                                      rounded-none
                                                      focus:shadow-[none]
                                                      focus:border-black
                                                      hover:border-b-[1px]
                                                      hover:border-black
                                                      "
                                                disabled={isDisabled}
                                              />
                                            )}
                                          />
                                        </div>
                                      )
                                ))}
                              </div>
                            </div>

                          </div>
                        ))}

                      </div>
                    );
                  }}
                />

              </div>
            )}

            {/* Type: Multiple Text */}
            {question.type === QuestionType.MULTIPLE_TEXT && (
              <div>
                <Label>{question.title}</Label>
                <div className="mt-4 space-y-4">

                  { (question?.subQuestions ?? []).map(subs => (
                    <div key={subs.name} className="flex gap-2 w-full items-center">
                      <Label className="shrink-0">{subs.title}</Label>
                      <Controller
                        name={subs.name}
                        control={control}
                        render={({ field }) => (
                          <Input
                            placeholder=""
                            classNameParent="w-full"
                            className="
                                         flex-1
                                         p-0
                                         h-[unset]
                                         w-full
                                         border-0
                                         border-b
                                         border-solid
                                       bg-white
                                         rounded-none
                                         focus:shadow-[none]
                                         focus:border-black
                                         hover:border-b-[1px]
                                         hover:border-black
                                         "
                            value={field.value || ''}
                            onChange={field.onChange}
                            error={!!errors[subs.name]}
                            disabled={isDisabled}
                          />
                        )}
                      />
                    </div>
                  ))}

                </div>
              </div>
            )}
          </div>

        ))

      }

    </form>
  );
};

export default QuestionnaireForm;
