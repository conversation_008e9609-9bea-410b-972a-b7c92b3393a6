'use client';

import React, { useEffect, useRef } from 'react';
import { DocumentIcon } from '@/shared/icons';
import QuestionnaireSection from './QuestionnaireSection';
import type { QuestionnaireSectionTypeRef } from './QuestionnaireSection';
import ProjectCardSkeleton from '@/features/project-management/components/project-list/ProjectCardSkeleton';
import { useQuestionnaireData, useQuestionnaireStore } from '../../stores';
import QuestionnaireMarkdown from './QuestionnaireMarkdown';
import type { QuestionnairePayload } from '../../types/questionnaire';
import { Button } from '@/shared/components/ui/button';
import { useCreateQuestionnaire } from '../../hooks/useQuestionnaireCreate';
import { useSearchParams } from 'next/navigation';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { EQuantitative } from '@/features/project-management/types/questionnaire';

const QuestionnaireWrapper: React.FC = () => {
  const questionnaire = useQuestionnaireData();

  const searchParams = useSearchParams();

  const id = searchParams.get('id');

  const { data: generatedQuestionnaire } = useGetInfoDetail<any, any>(id ?? '');

  const { mutateAsync } = useCreateQuestionnaire();

  const formRef = useRef<QuestionnaireSectionTypeRef[]>([]);

  const questionnaireStore = useQuestionnaireStore()!.getState();

  const { actions } = questionnaireStore;

  const { initializeQuestionnaire } = actions;

  const isSubmit = useRef<boolean>(false);

  useEffect(() => {
    if (generatedQuestionnaire && generatedQuestionnaire?.stepInfo.length) {
      const questionForm = generatedQuestionnaire?.stepInfo.find(t => t.type === EQuantitative.QUESTIONNAIRE && t.order === 3);
      if (questionForm) {
        const form = questionForm.infos[0]?.form;

        initializeQuestionnaire(form);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generatedQuestionnaire]);

  const handleSubmit = () => {
    if (isSubmit.current) {
      return;
    }
    if (generatedQuestionnaire && generatedQuestionnaire?.stepInfo.length) {
      const questionForm = generatedQuestionnaire?.stepInfo.find(t => t.type === EQuantitative.QUESTIONNAIRE && t.order === 3);
      const dataForm = formRef.current.map(t => t.onSubmitForm());

      const form = questionForm.infos[0]?.form;
      const questionnaireId = form?.id ?? '';

      const questions = ((dataForm ?? []).flatMap(section => section)) as any;

      const payload = {
        questionnaireId,
        questions,
      } as QuestionnairePayload;

      if (payload) {
        mutateAsync(payload);
        isSubmit.current = true;
      }
    }
  };

  return (
    <div className="min-h-screen bg-[#EEF2FF] py-8 px-4">
      <div className="max-w-5xl mx-auto">
        {
          !questionnaire
            ? (
                <>
                  <h3>Waiting...</h3>
                  <ProjectCardSkeleton />
                </>
              )
            : (
                <>
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                      <DocumentIcon className="w-8 h-8 text-blue-600" />
                    </div>
                    <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                      Market Sizing Questionnaire
                    </h1>
                    <p className="text-gray-600 mb-1">
                      Help us understand your vision needs and preferences
                    </p>
                    <p className="text-sm text-blue-600">
                      *This survey takes approximately 10-15 minutes to complete.
                    </p>
                  </div>

                  {questionnaire.introduction && (
                    <div className="bg-white shadow-sm rounded-2xl p-4 mb-4">
                      <QuestionnaireMarkdown markdown={questionnaire.introduction} />
                    </div>
                  )}

                  {questionnaire.sections.map((section, index) => (
                    <div key={index} className="bg-white shadow-sm rounded-2xl p-4 mb-4">
                      <QuestionnaireSection
                        isDisabled={isSubmit.current}
                        ref={(e) => {
                          if (e) {
                            formRef.current[index] = e;
                          }
                        }}
                        section={section}
                      />
                    </div>
                  ))}

                </>
              )
        }
        {questionnaire && (
          <div className={`sticky bottom-0 bg-white shadow-md p-2 flex justify-center items-center border-t border-t-gray-200 rounded-xl mt-2 ${isSubmit.current ? 'opacity-50' : ''}`}>
            <Button className={`${isSubmit.current ? 'cursor-not-allowed' : ''} `} onClick={() => handleSubmit()}>Submit</Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuestionnaireWrapper;
