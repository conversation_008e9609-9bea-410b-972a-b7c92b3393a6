import { z } from 'zod';

/**
 * Schema for assigning prompts to a step
 */
export const assignPromptsToStepSchema = z.object({
  name: z.string(),
  order: z.number(),
  prompts: z.array(z.string()).min(1, 'At least one prompt must be selected'),
});

/**
 * Type for assigning prompts to a step
 */
export type AssignPromptsToStepPayload = z.infer<typeof assignPromptsToStepSchema>;

/**
 * Schema for removing prompts from a step
 */
export const removePromptsFromStepSchema = z.object({
  name: z.string(),
  order: z.number(),
  prompts: z.array(z.string()),
});

/**
 * Type for removing prompts from a step
 */
export type RemovePromptsFromStepPayload = z.infer<typeof removePromptsFromStepSchema>;
