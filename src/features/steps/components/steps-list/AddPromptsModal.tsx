'use client';

import type { IBaseStep } from '../../types/step.type';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { Badge } from '@/shared/components/ui/badge';
import { cn } from '@/shared/utils/utils';
import { Check, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import { usePromptsForStep, useStepPrompts } from '../../hooks';
import Input from '@/shared/components/form/input/InputField';

type AddPromptsModalProps = {
  step: IBaseStep;
  isOpen: boolean;
  onClose: () => void;
};

/**
 * AddPromptsModal Component
 *
 * This component allows selecting prompts from the prompts list to add to a step.
 * It shows available prompts with search functionality and multi-select capability.
 */
export function AddPromptsModal({ step, isOpen, onClose }: AddPromptsModalProps) {
  const t = useTranslations('Step');
  const [selectedPromptIds, setSelectedPromptIds] = useState<string[]>([]);

  const { assignPrompts, isAssigning } = useStepPrompts();

  // Get existing prompt IDs in the step
  const existingPromptIds = useMemo(() =>
    step.prompts.map(p => p.id), [step.prompts]);

  // Use the new hook for fetching and searching prompts
  const {
    prompts: availablePrompts,
    searchQuery,
    updateSearchQuery,
    clearSearch,
    isLoading,
  } = usePromptsForStep(existingPromptIds);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      clearSearch();
      setSelectedPromptIds([]);
    }
  }, [isOpen, clearSearch]);

  const handlePromptToggle = (promptId: string) => {
    setSelectedPromptIds(prev =>
      prev.includes(promptId)
        ? prev.filter(id => id !== promptId)
        : [...prev, promptId],
    );
  };

  const handleSelectAll = () => {
    if (selectedPromptIds.length === availablePrompts.length) {
      setSelectedPromptIds([]);
    } else {
      setSelectedPromptIds(availablePrompts.map(p => p.id));
    }
  };

  const handleAddPrompts = async () => {
    if (selectedPromptIds.length === 0) {
      return;
    }

    try {
      const prompts = [...existingPromptIds, ...selectedPromptIds];
      await assignPrompts({
        // stepId: step.id,
        name: step.name,
        order: step.order,
        prompts,
      }, step.id);
      onClose();
    } catch (error) {
      console.error('Failed to add prompts:', error);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-4xl">
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">
            {t('add_prompts_to_step', { fallback: 'Add Prompts to Step' })}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {t('add_prompts_description', {
              fallback: 'Select prompts to add to step {stepName}',
              stepName: step.name,
            })}
          </p>
        </div>

        {/* Search */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder={t('search_prompts', { fallback: 'Search prompts...' })}
              value={searchQuery}
              onChange={e => updateSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Select All */}
        {availablePrompts.length > 0 && (
          <div className="mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              className="h-8"
            >
              {selectedPromptIds.length === availablePrompts.length
                ? t('deselect_all', { fallback: 'Deselect All' })
                : t('select_all', { fallback: 'Select All' })}
            </Button>
            {selectedPromptIds.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {selectedPromptIds.length}
                {' '}
                {t('selected', { fallback: 'selected' })}
              </Badge>
            )}
          </div>
        )}

        {/* Prompts List */}
        <div className="max-h-96 overflow-y-auto mb-6">
          {isLoading
            ? (
                <div className="text-center py-8">
                  <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-500">{t('loading_prompts', { fallback: 'Loading prompts...' })}</p>
                </div>
              )
            : availablePrompts.length === 0
              ? (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">
                      {searchQuery
                        ? t('no_prompts_found', { fallback: 'No prompts found matching your search' })
                        : t('no_available_prompts', { fallback: 'No available prompts to add' })}
                    </p>
                  </div>
                )
              : (
                  <div className="space-y-2">
                    {availablePrompts.map(prompt => (
                      <div
                        key={prompt.id}
                        className={cn(
                          'flex items-start gap-3 p-3 border rounded-lg cursor-pointer transition-colors',
                          selectedPromptIds.includes(prompt.id)
                            ? 'border-primary bg-primary/5'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600',
                        )}
                        onClick={() => handlePromptToggle(prompt.id)}
                      >
                        <div className={cn(
                          'flex items-center justify-center w-5 h-5 border-2 rounded',
                          selectedPromptIds.includes(prompt.id)
                            ? 'border-primary bg-primary text-white'
                            : 'border-gray-300 dark:border-gray-600',
                        )}
                        >
                          {selectedPromptIds.includes(prompt.id) && (
                            <Check className="h-3 w-3" />
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                            {prompt.name}
                          </h4>
                          {prompt.description && (
                            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                              {prompt.description}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 dark:text-gray-500 line-clamp-2">
                            {prompt.content}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isAssigning}
          >
            {t('cancel', { fallback: 'Cancel' })}
          </Button>
          <Button
            onClick={handleAddPrompts}
            disabled={selectedPromptIds.length === 0 || isAssigning}
          >
            {isAssigning
              ? t('adding_prompts', { fallback: 'Adding...' })
              : t('add_selected_prompts', {
                  fallback: 'Add {count} Prompts',
                  count: selectedPromptIds.length,
                })}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
