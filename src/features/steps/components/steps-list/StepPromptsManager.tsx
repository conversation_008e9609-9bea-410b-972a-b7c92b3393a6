'use client';

import type { IBaseStep } from '../../types/step.type';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { cn } from '@/shared/utils/utils';
import { FileText, Plus, Trash2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useStepPrompts } from '../../hooks';
import { AddPromptsModal } from './AddPromptsModal';

type StepPromptsManagerProps = {
  step: IBaseStep;
  className?: string;
};

/**
 * StepPromptsManager Component
 *
 * This component manages the prompts within a step.
 * It allows adding and removing prompts from the step.
 */
export function StepPromptsManager({ step, className }: StepPromptsManagerProps) {
  const t = useTranslations('Step');
  const [showAddModal, setShowAddModal] = useState(false);
  const { removePrompts, isRemoving } = useStepPrompts();

  const handleRemovePrompt = async (promptId: string) => {
    const prompts: string[] = [];
    step.prompts.forEach((p) => {
      if (p.id !== promptId) {
        prompts.push(p.id);
      }
    });
    try {
      await removePrompts({
        name: step.name,
        order: step.order,
        prompts,
      }, step.id);
    } catch (error) {
      console.error('Failed to remove prompt:', error);
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          {t('prompts_in_step', { fallback: 'Prompts in this step' })}
        </h4>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowAddModal(true)}
          className="h-8 px-3"
        >
          <Plus className="h-4 w-4 mr-1" />
          {t('add_prompts', { fallback: 'Add Prompts' })}
        </Button>
      </div>

      {/* Prompts List */}
      {step.prompts.length === 0
        ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">
                {t('no_prompts_in_step', { fallback: 'No prompts in this step yet' })}
              </p>
              <p className="text-xs mt-1">
                {t('click_add_prompts', { fallback: 'Click "Add Prompts" to get started' })}
              </p>
            </div>
          )
        : (
            <div className="space-y-2">
              {step.prompts.map(prompt => (
                <div
                  key={prompt.id}
                  className="flex items-start justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant={prompt.active ? 'default' : 'secondary'} className="text-xs">
                        {prompt.active ? 'Active' : 'Inactive'}
                      </Badge>
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {prompt.name}
                      </h5>
                    </div>
                    {prompt.content && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {prompt.content}
                      </p>
                    )}
                    {prompt.description && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {prompt.description}
                      </p>
                    )}
                  </div>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemovePrompt(prompt.id)}
                    disabled={isRemoving}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

      {/* Add Prompts Modal */}
      {showAddModal && (
        <AddPromptsModal
          step={step}
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      )}
    </div>
  );
}
