'use client';

import type { ITableConfig } from '../../types/step.type';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { Card, CardHeader } from '@/shared/components/ui/card';
import { cn } from '@/shared/utils/utils';
import { ChevronRight } from 'lucide-react';

import { stepsSkeletonVariants } from './steps-list-variants';
import { stepCardContainerStyles } from './step-card-variants';

type SkeletonStepProps = {
  tableConfig: ITableConfig[];
  rows?: number;
};

/**
 * SkeletonStep Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays skeleton placeholders for steps while loading.
 * All steps use consistent child-style appearance.
 */
export function SkeletonStep({
  rows = 3,
}: Omit<SkeletonStepProps, 'tableConfig'>) {
  return (
    <div className="space-y-3">
      {[...Array.from({ length: rows })].map((_, i) => (
        <Card key={i} size="sm" className={cn(stepCardContainerStyles)}>
          <CardHeader className="pb-2 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* Order badge skeleton */}
                <Skeleton className={cn(stepsSkeletonVariants({ width: 'sm' }))} />
                {/* Title skeleton */}
                <Skeleton className={cn(stepsSkeletonVariants({ width: 'lg' }))} />
              </div>

              <div className="flex items-center gap-2">
                {/* Prompt count badge skeleton */}
                <Skeleton className={cn(stepsSkeletonVariants({ width: 'md' }))} />
                {/* Arrow icon skeleton */}
                <div className="ml-2">
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  );
}
