import { cva } from 'class-variance-authority';

/**
 * Steps List Header Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list header container variants are defined using the cva utility for consistent styling.
 */
export const stepsListHeaderContainerVariants = cva(
  'flex flex-col gap-4 mb-6',
  {
    variants: {
      variant: {
        default: '',
        compact: 'gap-2 mb-4',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Header Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list header title variants are defined using the cva utility for consistent styling.
 */
export const stepsListHeaderTitleVariants = cva(
  'text-2xl font-bold text-gray-900 dark:text-white',
  {
    variants: {
      variant: {
        default: '',
        compact: 'text-xl',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
