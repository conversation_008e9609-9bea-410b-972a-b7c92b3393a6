'use client';

import type { VariantProps } from 'class-variance-authority';
import { cn } from '@/shared/utils/utils';
import { useTranslations } from 'next-intl';

import {
  stepsListHeaderContainerVariants,
  stepsListHeaderTitleVariants,
} from './steps-list-header-variants';

type StepsListHeaderProps = {
  variant?: VariantProps<typeof stepsListHeaderContainerVariants>['variant'];
};

/**
 * StepsListHeader Component
 *
 * This component displays the header for the steps list page.
 * It includes the title and description.
 */
export function StepsListHeader({ variant }: StepsListHeaderProps) {
  const t = useTranslations('Step');

  return (
    <div className={cn(stepsListHeaderContainerVariants({ variant }))}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className={cn(stepsListHeaderTitleVariants({ variant }))}>
            {t('steps_title', { fallback: 'Steps' })}
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('steps_description', { fallback: 'Manage workflow steps and their associated prompts' })}
          </p>
        </div>
      </div>

    </div>
  );
}
