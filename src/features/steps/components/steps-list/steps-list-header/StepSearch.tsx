'use client';

import Input from '@/shared/components/form/input/InputField';
import FancyLoader from '@/shared/components/ui/fancy-loader/FancyLoader';
import { useDebounce } from '@/shared/hooks/useDebounce';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useStepFilters } from '../../../hooks';

/**
 * StepSearch component
 *
 * Simple implementation with:
 * - Local state for immediate UI feedback
 * - Debounced updates to the search state
 * - No circular dependencies
 */
export function StepSearch() {
  const t = useTranslations('Step');
  const { searchValue, updateFilter } = useStepFilters();
  const isLoading = false; // We'll need to get this from a different hook later

  // Track if we're handling an update from user input
  const isUserInput = useRef(false);

  // Track the previous search value to detect external changes
  const prevSearchValue = useRef(searchValue);

  // Local state for the input value
  const [inputValue, setInputValue] = useState(searchValue);

  // Debounced value for API calls
  const debouncedValue = useDebounce(inputValue, 300);

  // Update the filter when debounced value changes (only from user input)
  useEffect(() => {
    if (isUserInput.current && debouncedValue !== searchValue) {
      updateFilter(debouncedValue);
    }
    isUserInput.current = false;
  }, [debouncedValue, searchValue, updateFilter]);

  // Sync input value with external search value changes
  useEffect(() => {
    if (prevSearchValue.current !== searchValue && !isUserInput.current) {
      setInputValue(searchValue);
    }
    prevSearchValue.current = searchValue;
  }, [searchValue]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    isUserInput.current = true;
    setInputValue(value);
  }, []);

  return (
    <div className="relative">
      <Input
        type="text"
        placeholder={t('search_steps', { fallback: 'Search steps...' })}
        value={inputValue}
        onChange={handleInputChange}
        className="w-full"
      />
      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <FancyLoader size="sm" />
        </div>
      )}
    </div>
  );
}
