'use client';

import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { cn } from '@/shared/utils/utils';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { StepPromptsManager } from './StepPromptsManager';

import type { IBaseStep } from '../../types/step.type';
import {
  stepCardBadgeVariants,
  stepCardContainerVariants,
  stepCardHeaderVariants,
  stepCardTitleVariants,
} from './step-card-variants';

type StepCardProps = {
  step: IBaseStep;
  className?: string;
  isParent?: boolean; // Whether this step is a parent with children
  showPrompts?: boolean; // Whether to show prompts section
  displayOrder?: string; // Custom display order (e.g., "1.1", "1.2")
  isHiddenArrow?: boolean;
};

/**
 * StepCard Component
 *
 * This component displays a single step with its details and prompts.
 * - Parent steps: Show header only, no prompts
 * - Child steps: Show with prompts using child styling
 * - Standalone steps: Show as child steps with prompts
 */
export function StepCard({
  step,
  className,
  isParent = false,
  showPrompts = true,
  displayOrder,
  isHiddenArrow,
}: StepCardProps) {
  const t = useTranslations('Step');
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Determine variant based on props
  const cardVariant = isParent ? 'parent' : 'child';

  return (
    <Card
      size="sm"
      className={cn(
        stepCardContainerVariants({ variant: cardVariant }),
        className,
      )}
    >
      <CardHeader
        className={cn(
          stepCardHeaderVariants({ variant: cardVariant }),
          'cursor-pointer hover:bg-gray-50/50 dark:hover:bg-gray-800/50 transition-colors',
        )}
        onClick={toggleExpanded}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Badge
              variant={isParent ? 'default' : 'secondary'}
              className={cn(stepCardBadgeVariants({ variant: cardVariant }))}
            >
              {displayOrder || step.order}
            </Badge>
            <CardTitle className={cn(stepCardTitleVariants({ variant: cardVariant }))}>
              {step.name}
            </CardTitle>
          </div>

          {!isHiddenArrow
            && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {step.prompts.length}
                  {' '}
                  {step.prompts.length === 1 ? t('prompt', { fallback: 'prompt' }) : t('prompts', { fallback: 'prompts' })}
                </Badge>
                {/* Arrow icon to indicate open/closed state */}
                <div className="ml-2">
                  {isExpanded
                    ? (
                        <ChevronDown className="h-4 w-4 text-gray-500 transition-transform duration-200" />
                      )
                    : (
                        <ChevronRight className="h-4 w-4 text-gray-500 transition-transform duration-200" />
                      )}
                </div>
              </div>
            )}
        </div>
      </CardHeader>

      {isExpanded && showPrompts && (
        <CardContent className="pt-0 px-4 pb-4">
          <StepPromptsManager step={step} />
        </CardContent>
      )}
    </Card>
  );
}
