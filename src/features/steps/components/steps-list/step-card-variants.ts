import { cva } from 'class-variance-authority';

/**
 * Step Card Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * Parent and child steps have different appearances.
 */
export const stepCardContainerVariants = cva(
  'w-full transition-all duration-200 gap-3',
  {
    variants: {
      variant: {
        parent: 'border-l-4 border-l-primary shadow-sm',
        child: 'border-l-4 border-l-primary/30',
      },
    },
    defaultVariants: {
      variant: 'child',
    },
  },
);

// Keep the simple styles for backward compatibility
export const stepCardContainerStyles = 'w-full transition-all duration-200 gap-3 bg-gray-50/50 dark:bg-gray-800/50 border-l-4 border-l-primary/30';

/**
 * Step Card Header Variants
 *
 * This component uses semantic color tokens from our theming system.
 * Parent and child steps have different header styles.
 */
export const stepCardHeaderVariants = cva(
  'pb-2',
  {
    variants: {
      variant: {
        parent: '',
        child: 'py-2',
      },
    },
    defaultVariants: {
      variant: 'child',
    },
  },
);

// Keep the simple styles for backward compatibility
export const stepCardHeaderStyles = 'pb-2 py-2';

/**
 * Step Card Badge Variants
 *
 * This component uses semantic color tokens from our theming system.
 * Parent and child steps have different badge styles.
 */
export const stepCardBadgeVariants = cva(
  'font-mono',
  {
    variants: {
      variant: {
        parent: 'text-sm font-semibold',
        child: 'text-xs',
      },
    },
    defaultVariants: {
      variant: 'child',
    },
  },
);

// Keep the simple styles for backward compatibility
export const stepCardBadgeStyles = 'font-mono text-xs';

/**
 * Step Card Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * Parent and child steps have different title styles.
 */
export const stepCardTitleVariants = cva(
  'font-normal',
  {
    variants: {
      variant: {
        parent: 'text-lg text-primary',
        child: 'text-base text-gray-700 dark:text-gray-300',
      },
    },
    defaultVariants: {
      variant: 'child',
    },
  },
);

// Keep the simple styles for backward compatibility
export const stepCardTitleStyles = 'text-base text-gray-700 dark:text-gray-300';

/**
 * Step Card Description Styles
 *
 * This component uses semantic color tokens from our theming system.
 * All steps use consistent appearance.
 */
export const stepCardDescriptionStyles = 'text-sm mt-1 text-gray-500 dark:text-gray-400';

/**
 * Step Hierarchy Container Styles
 *
 * This component uses semantic color tokens from our theming system.
 * All steps use consistent spacing.
 */
export const stepHierarchyContainerStyles = 'space-y-3';
