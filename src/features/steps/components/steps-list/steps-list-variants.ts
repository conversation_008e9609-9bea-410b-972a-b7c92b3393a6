import { cva } from 'class-variance-authority';

/**
 * Steps List Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list container variants are defined using the cva utility for consistent styling.
 */
export const stepsListContainerVariants = cva(
  'w-full space-y-3',
  {
    variants: {
      variant: {
        default: '',
        compact: 'space-y-2',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Empty Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list empty message variants are defined using the cva utility for consistent styling.
 */
export const stepsListEmptyMessageVariants = cva(
  'text-center py-8',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary-500',
        secondary: 'text-secondary-500',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Loading Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list loading message variants are defined using the cva utility for consistent styling.
 */
export const stepsListLoadingMessageVariants = cva(
  'text-center py-4 text-muted-foreground',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Error Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list error container variants are defined using the cva utility for consistent styling.
 */
export const stepsListErrorContainerVariants = cva(
  'flex flex-col items-center justify-center gap-4 p-8 text-center',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Error Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list error icon variants are defined using the cva utility for consistent styling.
 */
export const stepsListErrorIconVariants = cva(
  'h-12 w-12',
  {
    variants: {
      variant: {
        default: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps List Error Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps list error message variants are defined using the cva utility for consistent styling.
 */
export const stepsListErrorMessageVariants = cva(
  'text-lg font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Steps Skeleton Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The steps skeleton variants are defined using the cva utility for consistent styling.
 */
export const stepsSkeletonVariants = cva(
  'h-3',
  {
    variants: {
      width: {
        default: 'w-[100px]',
        sm: 'w-[50px]',
        md: 'w-[100px]',
        lg: 'w-[150px]',
        xl: 'w-[200px]',
      },
    },
    defaultVariants: {
      width: 'default',
    },
  },
);
