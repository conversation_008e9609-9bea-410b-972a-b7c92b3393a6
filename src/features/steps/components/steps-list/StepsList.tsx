'use client';

import type { VariantProps } from 'class-variance-authority';
import { cn } from '@/shared/utils/utils';
import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { useStepInfiniteQuery } from '../../hooks';
import { StepsListHeader } from './steps-list-header';
import { StepsHierarchy } from './StepsHierarchy';
import { SkeletonStep } from './SkeletonStep';
import {
  stepsListContainerVariants,
  stepsListEmptyMessageVariants,
  stepsListErrorContainerVariants,
  stepsListErrorIconVariants,
  stepsListErrorMessageVariants,
  stepsListLoadingMessageVariants,
} from './steps-list-variants';

type StepsListProps = {
  variant?: VariantProps<typeof stepsListContainerVariants>['variant'];
};

/**
 * StepsList ComponentNo
 *
 * This component displays a list of steps with hierarchical structure.
 * It supports infinite scrolling and shows each step with its prompts.
 */
export function StepsList({ variant }: StepsListProps) {
  const t = useTranslations('Step');

  // Get steps data
  const {
    infiniteData,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useStepInfiniteQuery();

  // Safely flatten all steps from all pages with null checks
  const steps = infiniteData?.pages
    ? infiniteData.pages.flatMap((page) => {
        // The API response is in the data property of the ApiResponse
        return page?.data?.items || [];
      })
    : [];

  // Intersection observer for infinite scrolling
  const { ref, inView } = useInView({
    threshold: 0,
    rootMargin: '100px',
  });

  // Fetch next page when in view
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Loading state
  if (isLoading) {
    return (
      <div className={cn(stepsListContainerVariants({ variant }))}>
        <StepsListHeader />
        <div className="space-y-4">
          <SkeletonStep rows={5} />
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn(stepsListContainerVariants({ variant }))}>
        <StepsListHeader />
        <div className={cn(stepsListErrorContainerVariants())}>
          <AlertCircle className={cn(stepsListErrorIconVariants())} />
          <h3 className={cn(stepsListErrorMessageVariants())}>
            {t('error_loading_steps', { fallback: 'Error loading steps' })}
          </h3>
          <p className={cn(stepsListEmptyMessageVariants())}>
            {error.message || t('error_generic', { fallback: 'Something went wrong. Please try again.' })}
          </p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!steps || steps.length === 0) {
    return (
      <div className={cn(stepsListContainerVariants({ variant }))}>
        <StepsListHeader />
        <div className={cn(stepsListErrorContainerVariants())}>
          <h3 className={cn(stepsListErrorMessageVariants())}>
            {t('no_steps_found', { fallback: 'No steps found' })}
          </h3>
          <p className={cn(stepsListEmptyMessageVariants())}>
            {t('no_steps_description', { fallback: 'Steps will appear here once they are created.' })}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(stepsListContainerVariants({ variant }))}>
      <StepsListHeader />

      <StepsHierarchy steps={steps} />

      {/* Intersection observer target */}
      {hasNextPage && (
        <div
          ref={ref}
          className="h-20 flex items-center justify-center mt-4"
        >
          {isFetchingNextPage && (
            <div className={cn(stepsListLoadingMessageVariants())}>
              {t('loading_more_steps', { fallback: 'Loading more steps...' })}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
