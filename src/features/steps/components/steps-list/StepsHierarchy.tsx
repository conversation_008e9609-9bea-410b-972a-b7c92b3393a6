'use client';

import type { IResponseStepList } from '../../types/step.type';
import { useMemo } from 'react';
import { StepCard } from './StepCard';
import { cn } from '@/shared/utils/utils';
import { stepHierarchyContainerStyles } from './step-card-variants';

type StepsHierarchyProps = {
  steps: IResponseStepList[];
};

/**
 * StepsHierarchy Component
 *
 * This component organizes steps into a hierarchical structure:
 * - Parent steps with children: Show parent header + children (no parent prompts)
 * - Child steps: Show with prompts using child styling
 * - Standalone steps: Show as child steps with prompts
 */
export function StepsHierarchy({ steps }: StepsHierarchyProps) {
  // Sort steps by order - they already come with the correct hierarchy
  const sortedSteps = useMemo(() => {
    return [...steps].sort((a, b) => a.order - b.order);
  }, [steps]);

  if (!steps || steps.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <p>No steps available</p>
      </div>
    );
  }

  return (
    <div className={cn(stepHierarchyContainerStyles)}>
      {sortedSteps.map((step) => {
        const hasChildren = step.children && step.children.length > 0;

        if (hasChildren) {
          // Parent step with children - show parent and children
          return (
            <div key={step.id} className="space-y-2">
              {/* Parent Step - no prompts, consistent parent styling */}
              <StepCard
                step={step}
                isParent={true}
                showPrompts={false}
                displayOrder={`${step.order + 1}`}
                isHiddenArrow={true}
              />

              {/* Children Steps - nested with prompts and indexed numbering */}
              <div className="ml-6 space-y-1 border-l-2 border-gray-200 dark:border-gray-700 pl-4">
                {step.children!.map((childStep, index) => (
                  <StepCard
                    key={childStep.id}
                    step={childStep}
                    isParent={false}
                    showPrompts={true}
                    displayOrder={`${step.order + 1}.${index + 1}`}
                  />
                ))}
              </div>
            </div>
          );
        } else {
          // Standalone step without children - show with parent styling but with prompts
          return (
            <div key={step.id} className="space-y-1">
              <StepCard
                step={step}
                isParent={true}
                showPrompts={true}
                displayOrder={`${step.order + 1}`}
              />
            </div>
          );
        }
      })}
    </div>
  );
}
