import type { ApiResponse } from '@/shared/types/api-response';
import type { AssignPromptsToStepPayload } from '../validation/step.validation';
import type { IResponseStepList } from '../types/step.type';
import { http } from '@/core/http/http';

export const DEFAULT_PAGE_SIZE = 10;

export type StepFilters = {
  searchQuery?: string;
  page?: number;
  limit?: number;
};

export type GetStepsResponse = {
  items: IResponseStepList[];
  total: number;
  page: number;
  totalPages: number;
  limit: number;
};

/**
 * Builds query parameters for steps API requests
 */
const buildStepQueryParams = (filters: StepFilters = {}): string => {
  const params: string[] = [];

  // Make sure to include the search query even if it's just spaces
  if (filters.searchQuery !== undefined) {
    params.push(`searchValue=${encodeURIComponent(filters.searchQuery)}`);
  }

  if (filters.page !== undefined) {
    params.push(`page=${filters.page}`);
  }

  if (filters.limit !== undefined) {
    params.push(`itemsPerPage=${filters.limit}`);
  }

  return params.length > 0 ? `?${params.join('&')}` : '';
};

/**
 * Fetches steps from the API with filtering
 */
export async function getSteps(filters: StepFilters = {}): Promise<ApiResponse<GetStepsResponse>> {
  const queryString = buildStepQueryParams(filters);
  const response = await http.get<GetStepsResponse>({ url: `/base-steps${queryString}` });
  return response;
}

/**
 * Fetches steps with pagination for infinite scrolling
 */
export async function getStepsPage(
  filters: StepFilters = {},
  pageParam = 1,
): Promise<ApiResponse<GetStepsResponse>> {
  const paginatedFilters = {
    ...filters,
    page: pageParam,
    limit: filters.limit || DEFAULT_PAGE_SIZE,
  };

  const queryString = buildStepQueryParams(paginatedFilters);
  return await http.get<GetStepsResponse>({ url: `/base-steps${queryString}` });
}

/**
 * Fetches a step by ID
 */
export async function getStepById(id: string): Promise<ApiResponse<IResponseStepList>> {
  return await http.get<IResponseStepList>({ url: `/base-steps/${id}` });
}

/**
 * Assigns prompts to a step
 */
export async function assignPromptsToStep(payload: AssignPromptsToStepPayload, id: string): Promise<ApiResponse<void>> {
  return await http.patch<void>({
    url: `/base-steps/${id}`,
    data: payload,
  });
}

/**
 * Removes prompts from a step
 */
// export async function removePromptsFromStep(data: RemovePromptsFromStepPayload): Promise<ApiResponse<void>> {
//   console.log('f2');

//   return await http.delete<void>({
//     url: `/base-steps/${data.stepId}/prompts`,
//     options: {
//       data: { promptIds: data.promptIds },
//     },
//   });
// }
