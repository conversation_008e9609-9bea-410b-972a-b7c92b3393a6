'use client';

import type { StepOptions } from './useStepOptions';
import { useInfiniteQuery } from '@tanstack/react-query';
import { useStepStore } from '../stores/step.store';
import { mergeStepOptions } from './useStepOptions';
import { getSteps } from '../services/step.service';

/**
 * Hook for infinite query of steps
 *
 * This hook provides infinite scrolling functionality for steps.
 * It uses React Query's useInfiniteQuery for data fetching.
 *
 * @param options - Query options
 * @returns Infinite query result
 */
export function useStepInfiniteQuery(options: Partial<StepOptions> = {}) {
  // Merge options with defaults
  const stepOptions = mergeStepOptions(options);

  // Get search value from the store
  const { searchValue } = useStepStore();

  // Prepare API filters based on UI state
  const apiFilters = {
    searchQuery: searchValue || undefined,
  };

  // Infinite query for steps
  const infiniteQuery = useInfiniteQuery({
    queryKey: ['steps', 'infinite', apiFilters],
    queryFn: async ({ pageParam = 1 }) => {
      // Use the getStepsPage function with page parameter
      const response = await getSteps({
        ...apiFilters,
        page: pageParam,
      });

      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response
      if (lastPage.data) {
        const { page, totalPages } = lastPage.data;
        return page < totalPages ? page + 1 : undefined;
      }
      return undefined;
    },
    staleTime: stepOptions.staleTime,
    refetchOnWindowFocus: stepOptions.refetchOnWindowFocus,
  });

  // Extract first page data for convenience with safe access
  const firstPageData = infiniteQuery.data?.pages?.[0]?.data;

  return {
    // Data
    infiniteData: infiniteQuery.data,
    firstPageData,

    // Loading states
    isLoading: infiniteQuery.isLoading,
    isFetching: infiniteQuery.isFetching,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,

    // Pagination
    fetchNextPage: infiniteQuery.fetchNextPage,
    hasNextPage: infiniteQuery.hasNextPage,

    // Error state
    error: infiniteQuery.error,

    // Refetch action
    refetch: infiniteQuery.refetch,
  };
}
