'use client';

import { useStepFilters } from './useStepFilters';
import { useStepInfiniteQuery } from './useStepInfiniteQuery';
import { useStepPrompts } from './useStepPrompts';

/**
 * Comprehensive hook for step management
 *
 * This hook combines all the individual step hooks into a single hook.
 *
 * @returns Combined step methods and state
 */
export function useSteps() {
  // Get step filters
  const {
    searchValue,
    apiFilters,
    updateFilter: updateSearchValue,
    removeFilter: clearSearchValue,
  } = useStepFilters();

  // Get step infinite query
  const {
    infiniteData: infiniteSteps,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useStepInfiniteQuery();

  // Get step prompt management
  const {
    assignPrompts,
    removePrompts,
    isAssigning,
    isRemoving,
  } = useStepPrompts();

  // Return everything needed
  return {
    // State
    searchValue,
    apiFilters,

    // Steps data
    infiniteSteps,

    // Loading states
    isLoading,
    isFetchingNextPage,
    isAssigning,
    isRemoving,

    // Error states
    error,

    // Search actions
    updateSearchValue,
    clearSearchValue,

    // Prompt management actions
    assignPrompts,
    removePrompts,

    // Query actions
    fetchNextPage,
    hasNextPage,
    refetch,
  };
}
