'use client';

import type { AssignPromptsToStepPayload, RemovePromptsFromStepPayload } from '../validation/step.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { assignPromptsToStep } from '../services/step.service';

/**
 * Hook for managing prompts within steps
 *
 * This hook provides functionality to add and remove prompts from steps.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Prompt management methods and state
 */
export function useStepPrompts() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Assign prompts to step mutation
  const assignPromptsMutation = useMutation({
    mutationFn: (data: { payload: AssignPromptsToStepPayload; id: string }) => assignPromptsToStep(data.payload, data.id),
    onSuccess: () => {
      // Invalidate all step-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['steps'] });
      toast.success('Prompts added to step successfully');
    },
    onError: (error: any) => {
      console.error('Error assigning prompts to step:', error);
      toast.error(error?.message || 'Failed to add prompts to step');
    },
  });

  // Remove prompts from step mutation
  const removePromptsMutation = useMutation({
    mutationFn: (data: { payload: RemovePromptsFromStepPayload; id: string }) => assignPromptsToStep(data.payload, data.id),
    onSuccess: () => {
      // Invalidate all step-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['steps'] });
      toast.success('Prompts removed from step successfully');
    },
    onError: (error: any) => {
      console.error('Error removing prompts from step:', error);
      toast.error(error?.message || 'Failed to remove prompts from step');
    },
  });

  // Function to assign prompts to a step
  const assignPrompts = useCallback(async (payload: AssignPromptsToStepPayload, id: string) => {
    return assignPromptsMutation.mutateAsync({ payload, id });
  }, [assignPromptsMutation]);

  // Function to remove prompts from a step
  const removePrompts = useCallback(async (payload: RemovePromptsFromStepPayload, id: string) => {
    return removePromptsMutation.mutateAsync({ payload, id });
  }, [removePromptsMutation]);

  return {
    // Mutation state
    isAssigning: assignPromptsMutation.isPending,
    isRemoving: removePromptsMutation.isPending,
    assignError: assignPromptsMutation.error,
    removeError: removePromptsMutation.error,

    // Actions
    assignPrompts,
    removePrompts,
  };
}
