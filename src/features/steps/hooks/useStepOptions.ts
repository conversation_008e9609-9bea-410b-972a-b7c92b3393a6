'use client';

/**
 * Options for step-related hooks
 */
export type StepOptions = {
  /**
   * Stale time for queries in milliseconds
   * @default 300000 (5 minutes)
   */
  staleTime?: number;

  /**
   * Whether to refetch on window focus
   * @default false
   */
  refetchOnWindowFocus?: boolean;
};

/**
 * Default options for step hooks
 */
export const DEFAULT_STEP_OPTIONS: StepOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: false,
};

/**
 * Merges default options with provided options
 *
 * @param options - User provided options
 * @returns Merged options
 */
export function mergeStepOptions(options: Partial<StepOptions> = {}): StepOptions {
  return { ...DEFAULT_STEP_OPTIONS, ...options };
}
