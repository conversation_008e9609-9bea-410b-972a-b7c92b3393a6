'use client';

import type { StepFilters } from '../services/step.service';
import { useCallback } from 'react';
import { useStepStore } from '../stores/step.store';

/**
 * Hook for managing step filters
 *
 * This hook provides a way to manage filters for step listing.
 * It uses the step store for state management.
 *
 * @returns Filter state and actions
 */
export function useStepFilters() {
  // Get filter state and actions from the store
  const {
    searchValue,
    setSearchValue,
    clearSearchValue,
  } = useStepStore();

  // Check if any filters are active
  const hasActiveFilters = !!searchValue;

  // Prepare API filters based on UI state
  const apiFilters: StepFilters = {
    searchQuery: searchValue || undefined,
  };

  // Function to update a filter
  const updateFilter = useCallback((value: string) => {
    setSearchValue(value);
  }, [setSearchValue]);

  // Function to remove a filter
  const removeFilter = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  // Function to clear all filters
  const clearAllFilters = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  return {
    // Filter state
    searchValue,
    hasActiveFilters,
    apiFilters,

    // Filter actions
    updateFilter,
    removeFilter,
    clearAllFilters,
  };
}
