'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getPrompts } from '@/features/prompt/services/prompt.service';

/**
 * Hook for fetching and managing prompts for step selection
 *
 * This hook provides functionality to:
 * - Fetch all available prompts
 * - Search through prompts by name, description, and content
 * - Filter out prompts that are already assigned to a step
 * - Handle loading and error states
 */
export function usePromptsForStep(existingPromptIds: string[] = []) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // Debounce search query with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch all prompts with debounced search query
  const {
    data: promptsResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['prompts-for-step', debouncedSearchQuery],
    queryFn: async () => {
      const response = await getPrompts({
        searchQuery: debouncedSearchQuery || undefined,
        limit: 50, // Get more prompts for selection
      });
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Extract prompts from response
  const allPrompts = useMemo(() => {
    if (!promptsResponse?.data?.items) {
      return [];
    }
    return promptsResponse.data.items;
  }, [promptsResponse]);

  // Filter available prompts (exclude already assigned ones)
  const availablePrompts = useMemo(() => {
    return allPrompts.filter(prompt => !existingPromptIds.includes(prompt.id));
  }, [allPrompts, existingPromptIds]);

  // Update search query
  const updateSearchQuery = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchQuery('');
  }, []);

  return {
    // Data
    prompts: availablePrompts,
    allPrompts,

    // Search
    searchQuery,
    updateSearchQuery,
    clearSearch,

    // States
    isLoading,
    error,

    // Actions
    refetch,
  };
}
