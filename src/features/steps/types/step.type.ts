export type IStepList = {
  tableConfig: ITableConfig[];
  data: IDataStepList[];
  loading: boolean;
};

export type ITableConfig = {
  tag: string;
  label: string;
  className?: string;
};

export type IDataStepList = {
  no: number;
  name: string;
  description: string;
  order: string; // e.g., "1", "1.1", "1.2", "2", etc.
  promptCount: number;
  updatedAt: string;
  id: string;
  [key: string]: string | number;
};

export type IResponseStepList = {
  children: IBaseStep[];
} & IBaseStep;

export type IStepPrompt = {
  id: string;
  name: string;
  description: string;
  content: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
};

export type IBaseStep = {
  name: string;
  order: number;
  prompts: IStepPrompt[];
  root: boolean;
  createdAt: string;
  updatedAt: string;
  id: string;
};
