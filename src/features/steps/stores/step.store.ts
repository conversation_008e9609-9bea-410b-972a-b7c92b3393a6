'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * Step store state interface
 */
type StepState = {
  // Search state
  searchValue: string;

  // Selected step for prompt management
  selectedStepId: string | null;
};

/**
 * Step store actions interface
 */
type StepActions = {
  /**
   * Set the search value
   */
  setSearchValue: (value: string) => void;

  /**
   * Clear the search value
   */
  clearSearchValue: () => void;

  /**
   * Set the selected step ID
   */
  setSelectedStepId: (id: string | null) => void;

  /**
   * Clear the selected step ID
   */
  clearSelectedStepId: () => void;
};

/**
 * Combined step store type
 */
type StepStore = StepState & StepActions;

/**
 * Initial step state
 */
const initialState: StepState = {
  // Initial search state
  searchValue: '',

  // Initial selection state
  selectedStepId: null,
};

/**
 * Step store
 *
 * This store manages the state for the steps feature.
 * It uses Zustand for state management with devtools support.
 */
export const useStepStore = create<StepStore>()(
  devtools(
    set => ({
      // Initial state
      ...initialState,

      // Search actions
      setSearchValue: (value: string) =>
        set(
          { searchValue: value },
          false,
          'setSearchValue',
        ),

      clearSearchValue: () =>
        set(
          { searchValue: '' },
          false,
          'clearSearchValue',
        ),

      // Selection actions
      setSelectedStepId: (id: string | null) =>
        set(
          { selectedStepId: id },
          false,
          'setSelectedStepId',
        ),

      clearSelectedStepId: () =>
        set(
          { selectedStepId: null },
          false,
          'clearSelectedStepId',
        ),
    }),
    {
      name: 'step-store',
    },
  ),
);
