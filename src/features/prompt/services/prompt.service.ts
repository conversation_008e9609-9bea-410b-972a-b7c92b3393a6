import type { ApiResponse } from '@/shared/types/api-response';
import type { CreatePromptPayload, UpdatePromptPayload } from '../validation/prompt.validation';
import type { IResponsePromptList } from '../types/prompt.type';
import { http } from '@/core/http/http';

export const DEFAULT_PAGE_SIZE = 10;

export type PromptFilters = {
  searchQuery?: string;
  page?: number;
  limit?: number;
};

export type GetPromptsResponse = {
  items: IResponsePromptList[];
  total: number;
  page: number;
  totalPages: number;
  limit: number;
};

/**
 * Builds query parameters for prompts API requests
 */
const buildPromptQueryParams = (filters: PromptFilters = {}): string => {
  const params: string[] = [];

  // Make sure to include the search query even if it's just spaces
  if (filters.searchQuery !== undefined) {
    params.push(`searchValue=${encodeURIComponent(filters.searchQuery)}`);
  }

  if (filters.page !== undefined) {
    params.push(`page=${filters.page}`);
  }

  if (filters.limit !== undefined) {
    params.push(`itemsPerPage=${filters.limit}`);
  }

  return params.length > 0 ? `?${params.join('&')}` : '';
};

/**
 * Fetches prompts from the API with filtering
 */
export async function getPrompts(filters: PromptFilters = {}): Promise<ApiResponse<GetPromptsResponse>> {
  const queryString = buildPromptQueryParams(filters);
  const response = await http.get<GetPromptsResponse>({ url: `/prompts${queryString}` });
  return response;
}

/**
 * Fetches prompts with pagination for infinite scrolling
 */
export async function getPromptsPage(
  filters: PromptFilters = {},
  pageParam = 1,
): Promise<ApiResponse<GetPromptsResponse>> {
  const paginatedFilters = {
    ...filters,
    page: pageParam,
    limit: filters.limit || DEFAULT_PAGE_SIZE,
  };

  const queryString = buildPromptQueryParams(paginatedFilters);
  return await http.get<GetPromptsResponse>({ url: `/prompts${queryString}` });
}

/**
 * Fetches a prompt by ID
 */
export async function getPromptById(id: string): Promise<ApiResponse<IResponsePromptList>> {
  return await http.get<IResponsePromptList>({ url: `/prompts/${id}` });
}

/**
 * Deletes a prompt by ID
 */
export async function deletePromptById(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/prompts/${id}` });
}

/**
 * Creates a new prompt
 */
export async function createPrompt(data: CreatePromptPayload): Promise<ApiResponse<IResponsePromptList>> {
  return await http.post<IResponsePromptList>({
    url: '/prompts',
    data,
  });
}

/**
 * Updates a prompt
 */
export async function updatePrompt(id: string, data: UpdatePromptPayload): Promise<ApiResponse<IResponsePromptList>> {
  return await http.patch<IResponsePromptList>({
    url: `/prompts/${id}`,
    data,
  });
}
