import { z } from 'zod';

/**
 * Schema for creating a new prompt
 */
export const createPromptSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  content: z.string().min(1, 'Content is required'),
});

/**
 * Type for creating a new prompt
 */
export type CreatePromptPayload = z.infer<typeof createPromptSchema>;

/**
 * Schema for updating a prompt
 */
export const updatePromptSchema = createPromptSchema.partial();

/**
 * Type for updating a prompt
 */
export type UpdatePromptPayload = z.infer<typeof updatePromptSchema>;
