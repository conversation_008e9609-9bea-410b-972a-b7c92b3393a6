'use client';

import { usePromptCreate } from './usePromptCreate';
import { usePromptDelete } from './usePromptDelete';
import { usePromptFilters } from './usePromptFilters';
import { usePromptInfiniteQuery } from './usePromptInfiniteQuery';
import { usePromptUpdate } from './usePromptUpdate';

/**
 * Comprehensive hook for prompt management
 *
 * This hook combines all the individual prompt hooks into a single hook.
 *
 * @returns Combined prompt methods and state
 */
export function usePrompts() {
  // Get prompt filters
  const {
    searchValue,
    apiFilters,
    updateFilter: updateSearchValue,
    removeFilter: clearSearchValue,
  } = usePromptFilters();

  // Get prompt infinite query
  const {
    infiniteData: infinitePrompts,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = usePromptInfiniteQuery();

  // Get prompt create
  const {
    createPrompt,
  } = usePromptCreate();

  // Get prompt update
  const {
    updatePrompt,
  } = usePromptUpdate();

  // Get prompt delete
  const {
    deletePrompt,
  } = usePromptDelete();

  // Return everything needed
  return {
    // State
    searchValue,
    apiFilters,

    // Prompts data
    infinitePrompts,

    // Loading states
    isLoading,
    isFetchingNextPage,

    // Error states
    error,

    // Search actions
    updateSearchValue,
    clearSearchValue,

    // Data actions
    createPrompt,
    updatePrompt,
    deletePrompt,

    // Query actions
    fetchNextPage,
    hasNextPage,
    refetch,
  };
}
