'use client';

import type { CreatePromptPayload } from '../validation/prompt.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { createPrompt as createPrompt<PERSON><PERSON> } from '../services/prompt.service';

/**
 * Hook for creating a new prompt
 *
 * This hook provides a way to create a new prompt.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Create mutation and helper method
 */
export function usePromptCreate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Create prompt mutation
  const createPromptMutation = useMutation({
    mutationFn: (data: CreatePromptPayload) => createPromptApi(data),
    onSuccess: () => {
      // Invalidate all prompt-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      toast.success('Prompt created successfully');
    },
    onError: (error: any) => {
      console.error('Error creating prompt:', error);
      toast.error(error?.message || 'Failed to create prompt');
    },
  });

  // Function to create a prompt
  const createPrompt = useCallback(async (data: CreatePromptPayload) => {
    return createPromptMutation.mutateAsync(data);
  }, [createPromptMutation]);

  return {
    // Mutation state
    isCreating: createPromptMutation.isPending,
    createError: createPromptMutation.error,

    // Action
    createPrompt,
  };
}
