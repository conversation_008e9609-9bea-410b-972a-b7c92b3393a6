'use client';

import type { PromptOptions } from './usePromptOptions';
import { useInfiniteQuery } from '@tanstack/react-query';
import { getPrompts } from '../services/prompt.service';
import { usePromptStore } from '../stores/prompt.store';
import { mergePromptOptions } from './usePromptOptions';

/**
 * Hook for fetching prompts with infinite scrolling
 *
 * This hook provides a way to fetch prompts with pagination for infinite scrolling.
 * It uses React Query's useInfiniteQuery for data fetching and pagination.
 *
 * @param options - Configuration options for the hook
 * @returns Infinite query data and methods
 */
export function usePromptInfiniteQuery(options: Partial<PromptOptions> = {}) {
  // Merge default options with provided options
  const promptOptions = mergePromptOptions(options);

  // Get search value from the store
  const { searchValue } = usePromptStore();

  // Prepare API filters based on UI state
  const apiFilters = {
    searchQuery: searchValue || undefined,
  };

  // Use infinite query for prompts
  const infiniteQuery = useInfiniteQuery({
    queryKey: ['prompts', 'infinite', apiFilters],
    queryFn: async ({ pageParam = 1 }) => {
      // Use the getPromptsPage function with page parameter
      const response = await getPrompts({
        ...apiFilters,
        page: pageParam,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response
      if (lastPage.data) {
        const { page, totalPages } = lastPage.data;
        return page < totalPages ? page + 1 : undefined;
      }
      return undefined;
    },
    staleTime: promptOptions.staleTime,
    refetchOnWindowFocus: promptOptions.refetchOnWindowFocus,
  });

  // Extract first page data for convenience with safe access
  const firstPageData = infiniteQuery.data?.pages?.[0]?.data;

  return {
    // Data
    infiniteData: infiniteQuery.data,
    firstPageData,

    // Loading states
    isLoading: infiniteQuery.isLoading,
    isFetching: infiniteQuery.isFetching,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,

    // Pagination
    fetchNextPage: infiniteQuery.fetchNextPage,
    hasNextPage: infiniteQuery.hasNextPage,

    // Error state
    error: infiniteQuery.error,

    // Refetch action
    refetch: infiniteQuery.refetch,
  };
}
