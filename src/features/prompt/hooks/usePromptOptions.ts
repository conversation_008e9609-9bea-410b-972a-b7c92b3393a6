'use client';

/**
 * Options for prompt-related hooks
 */
export type PromptOptions = {
  /**
   * Stale time for queries in milliseconds
   * @default 300000 (5 minutes)
   */
  staleTime?: number;

  /**
   * Whether to refetch on window focus
   * @default false
   */
  refetchOnWindowFocus?: boolean;
};

/**
 * Default options for prompt hooks
 */
export const DEFAULT_PROMPT_OPTIONS: PromptOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: false,
};

/**
 * Merges default options with provided options
 *
 * @param options - User provided options
 * @returns Merged options
 */
export function mergePromptOptions(options: Partial<PromptOptions> = {}): PromptOptions {
  return { ...DEFAULT_PROMPT_OPTIONS, ...options };
}
