'use client';

import type { UpdatePromptPayload } from '../validation/prompt.validation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { updatePrompt as updatePrompt<PERSON><PERSON> } from '../services/prompt.service';

/**
 * Hook for updating a prompt
 *
 * This hook provides a way to update a prompt.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Update mutation and helper method
 */
export function usePromptUpdate() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Update prompt mutation
  const updatePromptMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePromptPayload }) => updatePromptApi(id, data),
    onSuccess: () => {
      // Invalidate all prompt-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      toast.success('Prompt updated successfully');
    },
    onError: (error: any) => {
      console.error('Error updating prompt:', error);
      toast.error(error?.message || 'Failed to update prompt');
    },
  });

  // Function to update a prompt
  const updatePrompt = useCallback(async (id: string, data: UpdatePromptPayload) => {
    return updatePromptMutation.mutateAsync({ id, data });
  }, [updatePromptMutation]);

  return {
    // Mutation state
    isUpdating: updatePromptMutation.isPending,
    updateError: updatePromptMutation.error,

    // Action
    updatePrompt,
  };
}
