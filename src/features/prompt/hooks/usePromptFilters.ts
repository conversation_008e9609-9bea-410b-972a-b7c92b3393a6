'use client';

import type { PromptFilters } from '../services/prompt.service';
import { useCallback } from 'react';
import { usePromptStore } from '../stores/prompt.store';

/**
 * Hook for managing prompt filters
 *
 * This hook provides a way to manage filters for prompt listing.
 * It uses the prompt store for state management.
 *
 * @returns Filter state and actions
 */
export function usePromptFilters() {
  // Get filter state and actions from the store
  const {
    searchValue,
    setSearchValue,
    clearSearchValue,
  } = usePromptStore();

  // Check if any filters are active
  const hasActiveFilters = !!searchValue;

  // Prepare API filters based on UI state
  const apiFilters: PromptFilters = {
    searchQuery: searchValue || undefined,
  };

  // Function to update a filter
  const updateFilter = useCallback((value: string) => {
    setSearchValue(value);
  }, [setSearchValue]);

  // Function to remove a filter
  const removeFilter = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  // Function to clear all filters
  const clearAllFilters = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  return {
    // Filter state
    searchValue,
    apiFilters,
    hasActiveFilters,

    // Filter actions
    updateFilter,
    removeFilter,
    clearAllFilters,
  };
}
