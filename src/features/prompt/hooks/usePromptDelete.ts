'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { deletePromptById } from '../services/prompt.service';

/**
 * Hook for deleting a prompt
 *
 * This hook provides a way to delete a prompt by ID.
 * It uses React Query's useMutation for data mutation.
 *
 * @returns Delete mutation and helper method
 */
export function usePromptDelete() {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();

  // Delete prompt mutation
  const deletePromptMutation = useMutation({
    mutationFn: (id: string) => deletePromptById(id),
    onSuccess: () => {
      // Invalidate all prompt-related queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['prompts'] });
      toast.success('Prompt deleted successfully');
    },
    onError: (error: any) => {
      console.error('Error deleting prompt:', error);
      toast.error(error?.message || 'Failed to delete prompt');
    },
  });

  // Function to delete a prompt
  const deletePrompt = useCallback(async (id: string) => {
    return deletePromptMutation.mutateAsync(id);
  }, [deletePromptMutation]);

  return {
    // Mutation state
    isDeleting: deletePromptMutation.isPending,
    deleteError: deletePromptMutation.error,

    // Action
    deletePrompt,
  };
}
