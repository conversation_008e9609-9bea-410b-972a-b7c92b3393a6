export type IPromptList = {
  tableConfig: ITableConfig[];
  data: IDataPromptList[];
  loading: boolean;
};

export type ITableConfig = {
  tag: string;
  label: string;
  className?: string;
};

export type IDataPromptList = {
  no: number;
  name: string;
  description: string;
  content: string;
  updatedAt: string;
  id: string;
  [key: string]: string | number;
};

export type IResponsePromptList = {
  name: string;
  description: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  id: string;
};
