'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * Prompt store state interface
 */
type PromptState = {
  // Search state
  searchValue: string;
};

/**
 * Prompt store actions interface
 */
type PromptActions = {
  /**
   * Set the search value
   */
  setSearchValue: (value: string) => void;

  /**
   * Clear the search value
   */
  clearSearchValue: () => void;
};

/**
 * Combined prompt store type
 */
type PromptStore = PromptState & PromptActions;

/**
 * Initial prompt state
 */
const initialState: PromptState = {
  // Initial search state
  searchValue: '',
};

/**
 * Create the prompt store with Zustand
 */
export const usePromptStore = create<PromptStore>()(
  devtools(
    set => ({
      // Initial state
      ...initialState,

      // Set the search value
      setSearchValue: (value) => {
        set({ searchValue: value });
      },

      // Clear the search value
      clearSearchValue: () => {
        set({ searchValue: '' });
      },
    }),
  ),
);
