'use client';

import type { VariantProps } from 'class-variance-authority';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';
import { cn } from '@/shared/utils/utils';
import { useTranslations } from 'next-intl';
import {
  promptsListHeaderContainerVariants,
  promptsListHeaderTitleVariants,
} from './prompts-list-header-variants';
import { PromptSearch } from './PromptSearch';
import { NewPromptForm } from '../NewPromptForm';

type PromptsListHeaderProps = {
  variant?: VariantProps<typeof promptsListHeaderContainerVariants>['variant'];
};

/**
 * PromptsListHeader Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a header with search and filters for the prompts list.
 */
export function PromptsListHeader({ variant = 'default' }: PromptsListHeaderProps = {}) {
  const t = useTranslations('Prompt');

  return (
    <div className={cn(promptsListHeaderContainerVariants({ variant }))}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className={cn(promptsListHeaderTitleVariants({ variant }))}>
          {t('prompts')}
        </h2>

        <div className="flex flex-1 md:flex-row items-center gap-4 max-w-full ml-auto">
          <div className="w-full">
            <div className="flex items-center gap-4 w-full">
              {/* Search */}
              <PromptSearch />
            </div>
          </div>

          {/* Wrap the NewPromptForm with PermissionGuard */}
          <PermissionGuard permission={Permission.CREATE_PROMPT}>
            <NewPromptForm />
          </PermissionGuard>
        </div>
      </div>
    </div>
  );
}
