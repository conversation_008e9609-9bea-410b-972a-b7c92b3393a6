import { cva } from 'class-variance-authority';

/**
 * Prompts List Header Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list header container variants are defined using the cva utility for consistent styling.
 */
export const promptsListHeaderContainerVariants = cva(
  'p-4',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Header Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list header title variants are defined using the cva utility for consistent styling.
 */
export const promptsListHeaderTitleVariants = cva(
  'text-xl font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Header Filter Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list header filter button variants are defined using the cva utility for consistent styling.
 */
export const promptsListHeaderFilterButtonVariants = cva(
  'flex items-center justify-center size-11 text-sm font-medium border rounded-lg relative',
  {
    variants: {
      variant: {
        default: 'bg-background border-border hover:bg-muted',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Header Filter Indicator Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list header filter indicator variants are defined using the cva utility for consistent styling.
 */
export const promptsListHeaderFilterIndicatorVariants = cva(
  'absolute -top-1 -right-1 h-3 w-3 rounded-full border-2',
  {
    variants: {
      variant: {
        default: 'bg-primary border-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
