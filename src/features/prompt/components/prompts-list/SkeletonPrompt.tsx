'use client';

import type { ITableConfig } from '../../types/prompt.type';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { TableCell, TableRow } from '@/shared/components/ui/table';
import { cn } from '@/shared/utils/utils';
import { promptsSkeletonVariants } from './prompts-list-variants';

type SkeletonPromptProps = {
  tableConfig: ITableConfig[];
  rows?: number;
};

/**
 * SkeletonPrompt Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays skeleton placeholders for prompts while loading.
 */
export function SkeletonPrompt({
  tableConfig,
  rows = 3,
}: SkeletonPromptProps) {
  return (
    <>
      {[...Array.from({ length: rows })].map((_, i) => (
        <TableRow key={i}>
          <TableCell className="w-10">
            <Skeleton className={cn(promptsSkeletonVariants({ width: 'sm' }))} />
          </TableCell>
          {
            tableConfig.map((config, i) => (
              <TableCell key={config.tag + i} className={config?.className}>
                <Skeleton className={cn(promptsSkeletonVariants())} />
              </TableCell>
            ),
            )
          }
        </TableRow>
      ))}
    </>
  );
}
