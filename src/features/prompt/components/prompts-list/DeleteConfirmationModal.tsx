'use client';

import { DeleteConfirmationModal as SharedDeleteConfirmationModal } from '@/shared/components/modals/DeleteConfirmationModal';
import { useTranslations } from 'next-intl';
import { usePromptDelete } from '../../hooks';

type PromptDeleteConfirmationModalProps = {
  promptId: string;
  promptName: string;
};

/**
 * DeleteConfirmationModal Component
 *
 * This component uses the shared DeleteConfirmationModal for deleting a prompt.
 */
export function DeleteConfirmationModal({
  promptId,
  promptName,
}: PromptDeleteConfirmationModalProps) {
  const t = useTranslations('Prompt');
  const { deletePrompt, isDeleting } = usePromptDelete();

  // Wrap the deletePrompt function to match the expected type
  const handleDelete = async (id: string) => {
    await deletePrompt(id);
  };

  // Create a message with the prompt name inserted
  const message = t.rich('confirm_delete_prompt', {
    name: () => <strong>{promptName}</strong>,
  });

  return (
    <SharedDeleteConfirmationModal
      entityId={promptId}
      entityName={promptName}
      title={t('confirm_delete_prompt_title')}
      confirmationMessage={message}
      deleteButtonText={t('delete')}
      deletingButtonText={t('deleting')}
      cancelButtonText={t('cancel')}
      onDelete={handleDelete}
      isDeleting={isDeleting}
    />
  );
}
