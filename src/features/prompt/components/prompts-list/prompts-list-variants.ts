import { cva } from 'class-variance-authority';

/**
 * Prompts List Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list container variants are defined using the cva utility for consistent styling.
 */
export const promptsListContainerVariants = cva(
  'flex flex-col h-[calc(100vh-77px)]',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Empty Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list empty message variants are defined using the cva utility for consistent styling.
 */
export const promptsListEmptyMessageVariants = cva(
  'text-center py-8',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',

      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Loading Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list loading message variants are defined using the cva utility for consistent styling.
 */
export const promptsListLoadingMessageVariants = cva(
  'text-center py-4 text-muted-foreground',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Error Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list error container variants are defined using the cva utility for consistent styling.
 */
export const promptsListErrorContainerVariants = cva(
  'flex flex-col items-center justify-center gap-4 p-8 text-center',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Error Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list error icon variants are defined using the cva utility for consistent styling.
 */
export const promptsListErrorIconVariants = cva(
  'h-12 w-12',
  {
    variants: {
      variant: {
        default: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts List Error Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts list error message variants are defined using the cva utility for consistent styling.
 */
export const promptsListErrorMessageVariants = cva(
  'text-lg font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Prompts Skeleton Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The prompts skeleton variants are defined using the cva utility for consistent styling.
 */
export const promptsSkeletonVariants = cva(
  'h-3',
  {
    variants: {
      width: {
        default: 'w-[100px]',
        sm: 'w-[50px]',
        md: 'w-[100px]',
        lg: 'w-[150px]',
        xl: 'w-[200px]',
      },
    },
    defaultVariants: {
      width: 'default',
    },
  },
);
