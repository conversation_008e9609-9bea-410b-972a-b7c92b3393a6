'use client';

import type { CreatePromptPayload } from '../../validation/prompt.validation';
import Input from '@/shared/components/form/input/InputField';
import TextArea from '@/shared/components/form/input/TextArea';
import Label from '@/shared/components/form/Label';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { usePromptCreate } from '../../hooks';
import { createPromptSchema } from '../../validation/prompt.validation';

export function NewPromptForm() {
  const t = useTranslations('Prompt');
  const { isOpen, openModal, closeModal } = useModal();
  const { createPrompt } = usePromptCreate();

  // Default form values
  const defaultValues = useMemo(() =>
    (
      {
        name: '',
        description: '',
        content: '',
      }
    ), []);

  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreatePromptPayload>({
    resolver: zodResolver(createPromptSchema),
    defaultValues,
  });

  // Reset form and close modal
  const resetForm = useCallback(() => {
    reset(defaultValues);
  }, [reset, defaultValues]);

  // Close modal and reset form
  const closeModalForm = useCallback(() => {
    closeModal();
    resetForm();
  }, [closeModal, resetForm]);

  const onSubmit = async (data: CreatePromptPayload) => {
    // Prepare the payload
    const promptPayload: CreatePromptPayload = {
      name: data.name,
      description: data.description,
      content: data.content,
    };

    try {
      await createPrompt(promptPayload);
      resetForm();
      closeModalForm();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Failed to create prompt:', error);
    }
  };

  return (
    <>
      <Button onClick={openModal}>{t('create_new_prompt_button')}</Button>

      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {t('form_title')}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {t('form_subtitle')}
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
            <div className="custom-scrollbar h-[450px] overflow-y-auto px-2 pb-3">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                {/* Prompt Information Section */}
                <div className="col-span-2 mb-2">
                  <h5 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                    {t('form_title')}
                  </h5>
                </div>

                {/* Name */}
                <div className="col-span-2">
                  <Label htmlFor="name">
                    {t('name')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        {...field}
                        placeholder={t('placeholder.name')}
                        type="text"
                        error={!!errors.name}
                        hint={errors.name?.message}
                      />
                    )}
                  />
                </div>

                {/* Description */}
                <div className="col-span-2">
                  <Label htmlFor="description">
                    {t('description')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="description"
                    render={({ field }) => (
                      <TextArea
                        placeholder={t('placeholder.description')}
                        value={field.value}
                        onChange={field.onChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white min-h-[100px]"
                        rows={3}
                        error={!!errors.description}
                        hint={errors.description?.message}
                      />
                    )}
                  />
                </div>

                {/* Content */}
                <div className="col-span-2">
                  <Label htmlFor="content">
                    {t('content')}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="content"
                    render={({ field }) => (
                      <TextArea
                        placeholder={t('placeholder.content')}
                        value={field.value}
                        onChange={field.onChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white min-h-[100px]"
                        rows={3}
                        error={!!errors.content}
                        hint={errors.content?.message}
                      />
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button variant="outline" onClick={closeModalForm} disabled={isSubmitting}>
                {t('cancel_button')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t('creating_button') : t('create_button')}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
