'use client';

import type { VariantProps } from 'class-variance-authority';
import type { ITableConfig } from '../../types/prompt.type';
import { Button } from '@/shared/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import { cn } from '@/shared/utils/utils';
import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { usePromptInfiniteQuery } from '../../hooks';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { SkeletonPrompt } from './SkeletonPrompt';
import { PromptsListHeader } from './prompts-list-header';
import { UpdatePromptForm } from './UpdatePromptForm';
import {
  promptsListContainerVariants,
  promptsListEmptyMessageVariants,
  promptsListErrorContainerVariants,
  promptsListErrorIconVariants,
  promptsListErrorMessageVariants,
  promptsListLoadingMessageVariants,
} from './prompts-list-variants';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';

type PromptsListProps = {
  variant?: VariantProps<typeof promptsListContainerVariants>['variant'];
};

/**
 * PromptsList Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a list of prompts in a table format with infinite scrolling.
 */
export function PromptsList({ variant = 'default' }: PromptsListProps = {}) {
  const t = useTranslations('Prompt');

  // Set up query with current filters
  const {
    infiniteData: data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = usePromptInfiniteQuery();

  // Check if there's an error
  const isError = !!error;

  // Set up intersection observer for infinite scrolling
  const { ref, inView } = useInView();

  // Load more prompts when the user scrolls to the bottom
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Safely flatten all prompts from all pages with null checks
  const allPrompts = data?.pages
    ? data.pages.flatMap((page) => {
        // The API response is in the data property of the ApiResponse
        return page?.data?.items || [];
      })
    : [];

  // Define table configuration
  const tableConfig: ITableConfig[] = [
    { tag: 'no', label: t('table.no'), className: 'w-[60px]' },
    { tag: 'name', label: t('table.name') },
    { tag: 'description', label: t('table.description'), className: 'max-w-[300px] truncate' },
    { tag: 'content', label: t('table.content'), className: 'max-w-[300px] truncate' },
    { tag: 'updatedAt', label: t('table.updated_at') },
    { tag: 'actions', label: t('table.actions'), className: 'w-[100px]' },
  ];

  // Format prompts data for display
  const formattedPrompts = allPrompts.map((prompt, index) => {
    const item: Record<string, any> = {
      no: index + 1,
      name: prompt.name,
      description: prompt.description,
      content: prompt.content,
      updatedAt: new Date(prompt.updatedAt || Date.now()).toLocaleString(),
      id: prompt.id,
      actions: (
        <div className="flex items-center space-x-2">
          <PermissionGuard permission={Permission.EDIT_PROMPT}>
            <UpdatePromptForm prompt={prompt} />
          </PermissionGuard>
          <PermissionGuard permission={Permission.DELETE_PROMPT}>
            <DeleteConfirmationModal
              promptId={prompt.id}
              promptName={prompt.name}
            />
          </PermissionGuard>
        </div>
      ),
    };
    return item;
  });

  return (
    <div className={cn(promptsListContainerVariants({ variant }))}>
      {/* Fixed header with search - doesn't scroll */}
      <PromptsListHeader />

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto px-4 py-4">
        {isError
          ? (
              <div className={cn(promptsListErrorContainerVariants({ variant }))}>
                <AlertCircle className={cn(promptsListErrorIconVariants({ variant }))} size={32} />
                <p className={cn(promptsListErrorMessageVariants({ variant }))}>
                  {error?.message || 'An error occurred while loading prompts.'}
                </p>
                <Button onClick={() => refetch()} variant="outline">
                  {t('try_again')}
                </Button>
              </div>
            )
          : (
              <Table>
                <TableHeader>
                  <TableRow>
                    {tableConfig.map(config => (
                      <TableHead key={config.tag} className={config?.className}>
                        {config.label}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading
                    ? <SkeletonPrompt tableConfig={tableConfig} />
                    : formattedPrompts.length
                      ? (
                          formattedPrompts.map((item, index) => (
                            <TableRow key={item.id || index}>
                              {tableConfig.map((config, i) => (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {item[config.tag]}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        )
                      : (
                          <TableRow>
                            <TableCell colSpan={tableConfig.length} className={cn(promptsListEmptyMessageVariants({ variant }))}>
                              {t('no_prompts_found')}
                            </TableCell>
                          </TableRow>
                        )}
                </TableBody>
              </Table>
            )}

        {/* Intersection observer target */}
        {hasNextPage && (
          <div
            ref={ref}
            className="h-20 flex items-center justify-center mt-4"
          >
            {isFetchingNextPage && (
              <div className={cn(promptsListLoadingMessageVariants({ variant }))}>
                {t('loading_more_prompts')}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
