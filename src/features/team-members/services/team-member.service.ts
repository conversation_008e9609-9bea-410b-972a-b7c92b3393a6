import type { User } from '@/features/auth/types/auth.types';
import type { ApiResponse } from '@/shared/types/api-response';
import type { CreateUserPayload } from '../validation/team-member.validation';
import { http } from '@/core/http/http';

export const DEFAULT_PAGE_SIZE = 10;

export type TeamMemberFilters = {
  searchQuery?: string;
  page?: number;
  limit?: number;
};

export type GetTeamMembersResponse = {
  items: User[];
  total: number;
  page: number;
  totalPages: number;
  limit: number;
};

/**
 * Builds query parameters for team members API requests
 */
const buildTeamMemberQueryParams = (filters: TeamMemberFilters = {}): string => {
  const params = new URLSearchParams();

  // Make sure to include the search query even if it's just spaces
  if (filters.searchQuery !== undefined) {
    params.append('searchValue', filters.searchQuery);
  }

  if (filters.page !== undefined) {
    params.append('page', filters.page.toString());
  }

  if (filters.limit !== undefined) {
    params.append('itemsPerPage', filters.limit.toString());
  }

  return params.toString() ? `?${params.toString()}` : '';
};

/**
 * Fetches team members from the API with filtering
 */
export async function getTeamMembers(filters: TeamMemberFilters = {}): Promise<ApiResponse<GetTeamMembersResponse>> {
  const queryString = buildTeamMemberQueryParams(filters);
  const response = await http.get<GetTeamMembersResponse>({ url: `/users${queryString}` });
  return response;
}

/**
 * Fetches team members with pagination for infinite scrolling
 */
export async function getTeamMembersPage(
  filters: TeamMemberFilters = {},
  pageParam = 1,
): Promise<ApiResponse<GetTeamMembersResponse>> {
  const paginatedFilters = {
    ...filters,
    page: pageParam,
    limit: filters.limit || DEFAULT_PAGE_SIZE,
  };

  const queryString = buildTeamMemberQueryParams(paginatedFilters);
  return await http.get<GetTeamMembersResponse>({ url: `/users${queryString}` });
}

// Team member detail functionality removed

/**
 * Deletes a team member by ID
 */
export async function deleteTeamMemberById(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/users/${id}` });
}

/**
 * Creates a new team member
 */
export async function createTeamMember(data: CreateUserPayload): Promise<ApiResponse<User>> {
  return await http.post<User>({
    url: '/auth/register',
    data,
  });
}
