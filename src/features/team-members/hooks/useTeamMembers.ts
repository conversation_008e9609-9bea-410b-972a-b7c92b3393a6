'use client';

import { useTeamMemberCreate } from './useTeamMemberCreate';
import { useTeamMemberDelete } from './useTeamMemberDelete';
import { useTeamMemberFilters } from './useTeamMemberFilters';
import { useTeamMemberInfiniteQuery } from './useTeamMemberInfiniteQuery';

/**
 * Comprehensive hook for team member management
 *
 * This hook combines all the individual team member hooks into a single hook.
 * It's provided for backward compatibility with the old API.
 *
 * @returns Combined team member methods and state
 */
export function useTeamMembers() {
  // Get team member filters
  const {
    searchValue,
    apiFilters,
    updateFilter: updateSearchValue,
    removeFilter: clearSearchValue,
  } = useTeamMemberFilters();

  // Get team member infinite query
  const {
    infiniteData: infiniteTeamMembers,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useTeamMemberInfiniteQuery();

  // Team member detail functionality removed

  // Get team member create
  const {
    createTeamMember,
  } = useTeamMemberCreate();

  // Get team member delete
  const {
    deleteTeamMember,
  } = useTeamMemberDelete();

  // Return everything needed
  return {
    // State
    searchValue,
    apiFilters,

    // Team members data
    infiniteTeamMembers,

    // Loading states
    isLoading,
    isFetchingNextPage,

    // Error states
    error,

    // Search actions
    updateSearchValue,
    clearSearchValue,

    // Data actions
    createTeamMember,
    deleteTeamMember,

    // Query actions
    fetchNextPage,
    hasNextPage,
    refetch,
  };
}
