'use client';

/**
 * Options for team member-related hooks
 */
export type TeamMemberOptions = {
  /**
   * Stale time for queries in milliseconds
   * @default 300000 (5 minutes)
   */
  staleTime?: number;

  /**
   * Whether to refetch on window focus
   * @default false
   */
  refetchOnWindowFocus?: boolean;
};

/**
 * Default options for team member hooks
 */
export const DEFAULT_TEAM_MEMBER_OPTIONS: TeamMemberOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: false,
};

/**
 * Merges default options with provided options
 *
 * @param options - User provided options
 * @returns Merged options
 */
export function mergeTeamMemberOptions(options: Partial<TeamMemberOptions> = {}): TeamMemberOptions {
  return { ...DEFAULT_TEAM_MEMBER_OPTIONS, ...options };
}
