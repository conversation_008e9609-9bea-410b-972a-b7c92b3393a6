'use client';

import type { TeamMemberOptions } from './useTeamMemberOptions';
import { useInfiniteQuery } from '@tanstack/react-query';
import { getTeamMembers } from '../services/team-member.service';
import { useTeamMemberStore } from '../stores/team-member.store';
import { mergeTeamMemberOptions } from './useTeamMemberOptions';

/**
 * Hook for fetching team members with infinite scrolling
 *
 * This hook provides a way to fetch team members with pagination for infinite scrolling.
 * It uses React Query's useInfiniteQuery for data fetching and pagination.
 *
 * @param options - Configuration options for the hook
 * @returns Infinite query data and methods
 */
export function useTeamMemberInfiniteQuery(options: Partial<TeamMemberOptions> = {}) {
  // Merge default options with provided options
  const teamMemberOptions = mergeTeamMemberOptions(options);

  // Get search value from the store
  const { searchValue } = useTeamMemberStore();

  // Prepare API filters based on UI state
  const apiFilters = {
    searchQuery: searchValue || undefined,
  };

  // Use infinite query for team members
  const infiniteQuery = useInfiniteQuery({
    queryKey: ['team-members', 'infinite', apiFilters],
    queryFn: async ({ pageParam = 1 }) => {
      // Use the getTeamMembersPage function with page parameter
      const response = await getTeamMembers({
        ...apiFilters,
        page: pageParam,
      });
      return response;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      // Check if there's a next page based on the response
      if (lastPage.data) {
        const { page, totalPages } = lastPage.data;
        return page < totalPages ? page + 1 : undefined;
      }
      return undefined;
    },
    staleTime: teamMemberOptions.staleTime,
    refetchOnWindowFocus: teamMemberOptions.refetchOnWindowFocus,
  });

  // Extract first page data for convenience with safe access
  const firstPageData = infiniteQuery.data?.pages?.[0]?.data;

  return {
    // Data
    infiniteData: infiniteQuery.data,
    firstPageData,

    // Loading states
    isLoading: infiniteQuery.isLoading,
    isFetching: infiniteQuery.isFetching,
    isFetchingNextPage: infiniteQuery.isFetchingNextPage,

    // Pagination
    fetchNextPage: infiniteQuery.fetchNextPage,
    hasNextPage: infiniteQuery.hasNextPage,

    // Error state
    error: infiniteQuery.error,

    // Refetch action
    refetch: infiniteQuery.refetch,
  };
}
