'use client';

import type { TeamMemberFilters } from '../services/team-member.service';
import { useCallback } from 'react';
import { useTeamMemberStore } from '../stores/team-member.store';

/**
 * Hook for managing team member filters
 *
 * This hook provides a way to manage filters for team member listing.
 * It uses the team member store for state management.
 *
 * @returns Filter state and actions
 */
export function useTeamMemberFilters() {
  // Get filter state and actions from the store
  const {
    searchValue,
    setSearchValue,
    clearSearchValue,
  } = useTeamMemberStore();

  // Check if any filters are active
  const hasActiveFilters = !!searchValue;

  // Prepare API filters based on UI state
  const apiFilters: TeamMemberFilters = {
    searchQuery: searchValue || undefined,
  };

  // Function to update a filter
  const updateFilter = useCallback((value: string) => {
    setSearchValue(value);
  }, [setSearchValue]);

  // Function to remove a filter
  const removeFilter = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  // Function to reset all filters
  const resetFilters = useCallback(() => {
    clearSearchValue();
  }, [clearSearchValue]);

  return {
    // Filter state
    searchValue,
    hasActiveFilters,
    apiFilters,

    // Filter actions
    updateFilter,
    removeFilter,
    resetFilters,
  };
}
