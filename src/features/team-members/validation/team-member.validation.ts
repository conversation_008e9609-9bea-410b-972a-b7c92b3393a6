import { z } from 'zod';

/**
 * Schema for creating a new user
 */
export const createUserSchema = (t: (key: string) => string) => {
  return z.object({
    firstName: z.string().min(1, t('firstNameRequired')),
    lastName: z.string().min(1, t('lastNameRequired')),
    email: z.string().email(t('emailValid')),
    password: z
      .string()
      .min(8, t('passwordMin'))
      .regex(/[A-Z]/, t('passwordUpperCase'))
      .regex(/[a-z]/, t('passwordLowerCase'))
      .regex(/\d/, t('passwordNumber'))
      .regex(/[^A-Z0-9]/i, t('passwordSpecial')),
    roles: z.array(z.string()).min(1, t('roleRequired')),
  });
};

/**
 * Type for creating a new user
 */
export type CreateUserPayload = z.infer<ReturnType<typeof createUserSchema>>;
