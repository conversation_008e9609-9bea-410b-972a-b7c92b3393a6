export type ITeamMemberList = {
  tableConfig: ITableConfig[];
  data: IDataTeamMemberList[];
  loading: boolean;
};

export type ITableConfig = {
  tag: string;
  label: string;
  className?: string;
};

export type IDataTeamMemberList = {
  no: number;
  name: string;
  email: string;
  updatedAt: string;
  id: string;
  [key: string]: string | number;
};

export type IResponseUserList = {
  tenantId: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: IRoles[];
  isActive: boolean;
  isInvited: boolean;
  passwordChangeRequired: boolean;
  createdAt: string;
  updatedAt: string;
  id: string;
};

export type IRoles = {
  name: string;
  description: string;
  permissions: string[];
  permissionGroups: string[];
  createdAt: string;
  updatedAt: string;
  id: string;
};
