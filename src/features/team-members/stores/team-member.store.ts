'use client';

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * Team member store state interface
 */
type TeamMemberState = {
  // Search state
  searchValue: string;
};

/**
 * Team member store actions interface
 */
type TeamMemberActions = {
  /**
   * Set the search value
   */
  setSearchValue: (value: string) => void;

  /**
   * Clear the search value
   */
  clearSearchValue: () => void;
};

/**
 * Combined team member store type
 */
type TeamMemberStore = TeamMemberState & TeamMemberActions;

/**
 * Initial team member state
 */
const initialState: TeamMemberState = {
  // Initial search state
  searchValue: '',
};

/**
 * Create the team member store with Zustand
 */
export const useTeamMemberStore = create<TeamMemberStore>()(
  devtools(
    set => ({
      // Initial state
      ...initialState,

      // Set the search value
      setSearchValue: (value) => {
        set({ searchValue: value });
      },

      // Clear the search value
      clearSearchValue: () => {
        set({ searchValue: '' });
      },
    }),
  ),
);
