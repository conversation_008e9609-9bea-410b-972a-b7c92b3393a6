'use client';

import { DeleteConfirmationModal as SharedDeleteConfirmationModal } from '@/shared/components/modals/DeleteConfirmationModal';
import { useTranslations } from 'next-intl';
import { useTeamMemberDelete } from '../../hooks';
import { toast } from 'sonner';

type TeamMemberDeleteConfirmationModalProps = {
  teamMemberId: string;
  teamMemberName: string;
};

/**
 * DeleteConfirmationModal Component
 *
 * This component uses the shared DeleteConfirmationModal for deleting a team member.
 */
export function DeleteConfirmationModal({
  teamMemberId,
  teamMemberName,
}: TeamMemberDeleteConfirmationModalProps) {
  const t = useTranslations('TeamMember');

  const noticeTranslate = useTranslations('notice');
  const { deleteTeamMember, isDeleting } = useTeamMemberDelete();

  // Wrap the deleteTeamMember function to match the expected type
  const handleDelete = async (id: string) => {
    try {
      await deleteTeamMember(id);
      toast.success(noticeTranslate('teamMembersDeleteSuc'));
    } catch (error) {
      console.log(error);
      toast.error(noticeTranslate('teamMembersDeleteFail'));
    }
  };

  // Create a message with the team member name inserted
  const message = t.rich('confirm_delete_user', {
    name: () => <strong>{teamMemberName}</strong>,
  });

  return (
    <SharedDeleteConfirmationModal
      entityId={teamMemberId}
      entityName={teamMemberName}
      title={t('confirm_delete_user_title')}
      confirmationMessage={message}
      deleteButtonText={t('delete')}
      deletingButtonText={t('deleting')}
      cancelButtonText={t('cancel')}
      onDelete={handleDelete}
      isDeleting={isDeleting}
    />
  );
}
