'use client';

import type { VariantProps } from 'class-variance-authority';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';
import { cn } from '@/shared/utils/utils';
import { useTranslations } from 'next-intl';
import {
  teamMembersListHeaderContainerVariants,
  teamMembersListHeaderTitleVariants,
} from './team-members-list-header-variants';
import { TeamMemberSearch } from './TeamMemberSearch';
import { NewTeamMemberForm } from '../NewTeamMemberForm';

type TeamMembersListHeaderProps = {
  variant?: VariantProps<typeof teamMembersListHeaderContainerVariants>['variant'];
};

/**
 * TeamMembersListHeader Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a header with search and filters for the team members list.
 */
export function TeamMembersListHeader({ variant = 'default' }: TeamMembersListHeaderProps = {}) {
  const t = useTranslations('TeamMember');

  return (
    <div className={cn(teamMembersListHeaderContainerVariants({ variant }))}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className={cn(teamMembersListHeaderTitleVariants({ variant }))}>
          {t('team_members')}
        </h2>

        <div className="flex flex-1 md:flex-row items-center gap-4 max-w-full ml-auto">
          <div className="w-full">
            <div className="flex items-center gap-4 w-full">
              {/* Search */}
              <TeamMemberSearch />
            </div>
          </div>

          {/* Wrap the NewTeamMemberForm with PermissionGuard */}
          <PermissionGuard permission={Permission.CREATE_USER}>
            <NewTeamMemberForm />
          </PermissionGuard>
        </div>
      </div>

    </div>
  );
}
