import { cva } from 'class-variance-authority';

/**
 * Team Members List Header Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list header container variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListHeaderContainerVariants = cva(
  'p-4',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Header Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list header title variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListHeaderTitleVariants = cva(
  'text-xl font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Header Filter Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list header filter button variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListHeaderFilterButtonVariants = cva(
  'flex items-center justify-center size-11 text-sm font-medium border rounded-lg relative',
  {
    variants: {
      variant: {
        default: 'bg-background border-border hover:bg-muted',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Header Filter Indicator Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list header filter indicator variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListHeaderFilterIndicatorVariants = cva(
  'absolute -top-1 -right-1 h-3 w-3 rounded-full border-2',
  {
    variants: {
      variant: {
        default: 'bg-primary border-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
