'use client';

import Input from '@/shared/components/form/input/InputField';
import FancyLoader from '@/shared/components/ui/fancy-loader/FancyLoader';
import { useDebounce } from '@/shared/hooks/useDebounce';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTeamMemberFilters } from '../../../hooks';

/**
 * TeamMemberSearch component
 *
 * Simple implementation with:
 * - Local state for immediate UI feedback
 * - Debounced updates to the search state
 * - No circular dependencies
 */
export function TeamMemberSearch() {
  const t = useTranslations('TeamMember');
  const { searchValue, updateFilter } = useTeamMemberFilters();
  const isLoading = false; // We'll need to get this from a different hook later

  // Track if we're handling an update from user input
  const isUserInput = useRef(false);

  // Track the previous search value to detect external changes
  const prevSearchValue = useRef(searchValue);

  // Local state for the input value
  const [inputValue, setInputValue] = useState(searchValue);

  // Create a debounced version of the input value
  const debouncedValue = useDebounce(inputValue, 500);

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    isUserInput.current = true;
    setInputValue(e.target.value);
  }, []);

  // Update search value when debounced value changes (only from user input)
  useEffect(() => {
    // Only update if this change came from user input
    if (isUserInput.current && debouncedValue !== searchValue) {
      updateFilter(debouncedValue);
      isUserInput.current = false;
    }
  }, [debouncedValue, searchValue, updateFilter]);

  // Handle external search value changes
  useEffect(() => {
    // Only run this effect once on mount to set up the layout effect
    const handleExternalSearchValueChange = () => {
      // Check if search value changed externally and not from user input
      if (searchValue !== prevSearchValue.current && !isUserInput.current) {
        // Schedule the state update for the next render
        requestAnimationFrame(() => {
          setInputValue(searchValue);
        });
      }

      // Update previous search value
      prevSearchValue.current = searchValue;
    };

    // Call the handler when searchValue changes
    handleExternalSearchValueChange();
  }, [searchValue]);

  return (
    <div className="flex-grow text-right relative">
      <Input
        type="text"
        id="search"
        placeholder={t('search_team_members')}
        value={inputValue}
        onChange={handleSearchChange}
        className="md:max-w-[400px]"
      />
      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <FancyLoader />
        </div>
      )}
    </div>
  );
}
