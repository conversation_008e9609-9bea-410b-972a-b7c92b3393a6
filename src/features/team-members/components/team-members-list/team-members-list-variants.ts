import { cva } from 'class-variance-authority';

/**
 * Team Members List Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list container variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListContainerVariants = cva(
  'flex flex-col h-[calc(100vh-77px)]',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list icon variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListIconVariants = cva(
  'cursor-pointer',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground hover:text-foreground',
        primary: 'text-primary-500 hover:text-primary-600',
        secondary: 'text-secondary-500 hover:text-secondary-600',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Empty Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list empty message variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListEmptyMessageVariants = cva(
  'text-center py-8',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary-500',
        secondary: 'text-secondary-500',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Error Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list error container variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListErrorContainerVariants = cva(
  'flex flex-col items-center justify-center gap-4 p-8 text-center',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Error Icon Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list error icon variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListErrorIconVariants = cva(
  'h-12 w-12',
  {
    variants: {
      variant: {
        default: 'text-destructive',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Error Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list error message variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListErrorMessageVariants = cva(
  'text-lg font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members List Loading Message Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members list loading message variants are defined using the cva utility for consistent styling.
 */
export const teamMembersListLoadingMessageVariants = cva(
  'text-center py-4',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Team Members Skeleton Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The team members skeleton variants are defined using the cva utility for consistent styling.
 */
export const teamMembersSkeletonVariants = cva(
  'h-3',
  {
    variants: {
      width: {
        default: 'w-[100px]',
        sm: 'w-[50px]',
        md: 'w-[100px]',
        lg: 'w-[150px]',
        xl: 'w-[200px]',
      },
    },
    defaultVariants: {
      width: 'default',
    },
  },
);
