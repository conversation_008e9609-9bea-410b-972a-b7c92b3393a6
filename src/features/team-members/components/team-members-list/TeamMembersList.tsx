'use client';

import type { VariantProps } from 'class-variance-authority';
import type { ITableConfig } from '../../types/team-member.type';
import { Button } from '@/shared/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import { cn } from '@/shared/utils/utils';
import { AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { useTeamMemberInfiniteQuery } from '../../hooks';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { SkeletonTeamMember } from './SkeletonTeamMember';
import { TeamMembersListHeader } from './team-members-list-header';
import {
  teamMembersListContainerVariants,
  teamMembersListEmptyMessageVariants,
  teamMembersListErrorContainerVariants,
  teamMembersListErrorIconVariants,
  teamMembersListErrorMessageVariants,
  teamMembersListLoadingMessageVariants,
} from './team-members-list-variants';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';

type TeamMembersListProps = {
  variant?: VariantProps<typeof teamMembersListContainerVariants>['variant'];
};

/**
 * TeamMembersList Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a list of team members in a table format with infinite scrolling.
 */
export function TeamMembersList({ variant = 'default' }: TeamMembersListProps = {}) {
  const t = useTranslations('TeamMember');

  // Set up query with current filters
  const {
    infiniteData: data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
    refetch,
  } = useTeamMemberInfiniteQuery();

  // Check if there's an error
  const isError = !!error;

  // Set up intersection observer for infinite scrolling
  const { ref, inView } = useInView();

  // Load more team members when the user scrolls to the bottom
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Safely flatten all team members from all pages with null checks
  const allTeamMembers = data?.pages
    ? data.pages.flatMap((page) => {
        // The API response is in the data property of the ApiResponse
        return page?.data?.items || [];
      })
    : [];

  // Define table configuration
  const tableConfig: ITableConfig[] = [
    {
      tag: 'no',
      label: t('table.no'),
    },
    {
      tag: 'name',
      label: t('table.full_name'),
    },
    {
      tag: 'email',
      label: t('table.email'),
    },
    {
      tag: 'updatedAt',
      label: t('table.updated_at'),
    },
    {
      tag: 'actions',
      label: t('table.actions'),
      className: 'w-24',
    },
  ];

  // Format team members data for display
  const formattedTeamMembers = allTeamMembers.map((user, index) => {
    const item: Record<string, any> = {
      no: index + 1,
      name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
      email: user.email,
      updatedAt: new Date(user.updatedAt || Date.now()).toLocaleString(),
      id: user.id,
      actions: (
        <div className="flex items-center space-x-2">
          <PermissionGuard permission={Permission.DELETE_USER}>
            <DeleteConfirmationModal
              teamMemberId={user.id}
              teamMemberName={`${user.firstName || ''} ${user.lastName || ''}`.trim()}
            />
          </PermissionGuard>
        </div>
      ),
    };
    return item;
  });

  return (
    <div className={cn(teamMembersListContainerVariants({ variant }))}>
      {/* Fixed header with search - doesn't scroll */}
      <TeamMembersListHeader />

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto px-4 py-4">
        {isError
          ? (
              <div className={cn(teamMembersListErrorContainerVariants({ variant }))}>
                <AlertCircle className={cn(teamMembersListErrorIconVariants({ variant }))} />
                <p className={cn(teamMembersListErrorMessageVariants({ variant }))}>
                  {error?.message || t('error_message')}
                </p>
                <Button onClick={() => refetch()} variant="outline">
                  {t('try_again')}
                </Button>
              </div>
            )
          : (
              <Table>
                <TableHeader>
                  <TableRow>
                    {tableConfig.map(config => (
                      <TableHead key={config.tag} className={config?.className}>
                        {config.label}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading
                    ? <SkeletonTeamMember tableConfig={tableConfig} />
                    : formattedTeamMembers.length
                      ? (
                          formattedTeamMembers.map((item, index) => (
                            <TableRow key={item.id || index}>
                              {tableConfig.map((config, i) => (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {item[config.tag]}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        )
                      : (
                          <TableRow>
                            <TableCell colSpan={tableConfig.length} className={cn(teamMembersListEmptyMessageVariants({ variant }))}>
                              {t('no_team_members_found')}
                            </TableCell>
                          </TableRow>
                        )}
                </TableBody>
              </Table>
            )}

        {/* Intersection observer target */}
        {hasNextPage && (
          <div
            ref={ref}
            className="h-20 flex items-center justify-center mt-4"
          >
            {isFetchingNextPage && (
              <div className={cn(teamMembersListLoadingMessageVariants({ variant }))}>
                {t('loading_more_team_members')}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
