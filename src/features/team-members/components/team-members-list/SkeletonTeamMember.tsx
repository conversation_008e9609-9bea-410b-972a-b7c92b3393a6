'use client';

import type { ITableConfig } from '../../types/team-member.type';
import { Skeleton } from '@/shared/components/ui/skeleton';
import { TableCell, TableRow } from '@/shared/components/ui/table';
import { cn } from '@/shared/utils/utils';
import { teamMembersSkeletonVariants } from './team-members-list-variants';

type SkeletonTeamMemberProps = {
  tableConfig: ITableConfig[];
  rows?: number;
};

/**
 * SkeletonTeamMember Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays skeleton placeholders for team members while loading.
 */
export function SkeletonTeamMember({
  tableConfig,
  rows = 3,
}: SkeletonTeamMemberProps) {
  return (
    <>
      {[...Array.from({ length: rows })].map((_, i) => (
        <TableRow key={i}>
          <TableCell className="w-10">
            <Skeleton className={cn(teamMembersSkeletonVariants({ width: 'sm' }))} />
          </TableCell>
          {
            tableConfig.map((config, i) => (
              <TableCell key={config.tag + i} className={config?.className}>
                <Skeleton className={cn(teamMembersSkeletonVariants())} />
              </TableCell>
            ),
            )
          }
        </TableRow>
      ))}
    </>
  );
}
