import type { VariantProps } from 'class-variance-authority';
import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import GridShape from '@/shared/components/common/GridShape';
import { cn } from '@/shared/utils/utils';
import {
  error404ButtonVariants,
  error404ContainerVariants,
  error404ContentVariants,
  error404TextVariants,
  error404TitleVariants,
} from './error-404-variants';

export const metadata: Metadata = {
  title: 'Next.js Error 404 | TailAdmin - Next.js Dashboard Template',
  description:
    'This is Next.js Error 404 page for TailAdmin - Next.js Tailwind CSS Admin Dashboard Template',
};

type Error404Props = {
  containerVariant?: VariantProps<typeof error404ContainerVariants>['variant'];
  contentSize?: VariantProps<typeof error404ContentVariants>['size'];
  titleSize?: VariantProps<typeof error404TitleVariants>['size'];
  textSize?: VariantProps<typeof error404TextVariants>['size'];
  buttonVariant?: VariantProps<typeof error404ButtonVariants>['variant'];
};

/**
 * Error404 Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a 404 error page with a message and a link to the home page.
 */
export default function Error404() {
  const defaultVariants: Error404Props = {
    containerVariant: 'default',
    contentSize: 'md',
    titleSize: 'md',
    textSize: 'base',
    buttonVariant: 'default',
  };

  const { containerVariant, contentSize, titleSize, textSize, buttonVariant } = defaultVariants;

  return (
    <div className={error404ContainerVariants({ variant: containerVariant })}>
      <GridShape />
      <div className={cn(
        error404ContentVariants({ size: contentSize }),
        'sm:max-w-[472px]',
      )}
      >
        <h1 className={cn(
          error404TitleVariants({ size: titleSize }),
          'xl:text-title-2xl',
        )}
        >
          ERROR
        </h1>

        <Image
          src="/images/error/404.svg"
          alt="404"
          className="dark:hidden"
          width={472}
          height={152}
        />
        <Image
          src="/images/error/404-dark.svg"
          alt="404"
          className="hidden dark:block"
          width={472}
          height={152}
        />

        <p className={cn(
          error404TextVariants({ size: textSize }),
          'sm:text-lg',
        )}
        >
          We can’t seem to find the page you are looking for!
        </p>

        <Link
          href="/"
          className={error404ButtonVariants({ variant: buttonVariant })}
        >
          Back to Home Page
        </Link>
      </div>
    </div>
  );
}
