import { cva } from 'class-variance-authority';

/**
 * Error404 Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const error404ContainerVariants = cva(
  'relative flex flex-col items-center justify-center min-h-screen p-6 overflow-hidden z-1',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Error404 Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The content variants are defined using the cva utility for consistent styling.
 */
export const error404ContentVariants = cva(
  'mx-auto w-full text-center',
  {
    variants: {
      size: {
        sm: 'max-w-[242px]',
        md: 'max-w-[472px]',
      },
    },
    defaultVariants: {
      size: 'sm',
    },
  },
);

/**
 * Error404 Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The title variants are defined using the cva utility for consistent styling.
 */
export const error404TitleVariants = cva(
  'mb-8 font-medium text-foreground',
  {
    variants: {
      size: {
        'md': 'text-title-md',
        'lg': 'text-title-lg',
        'xl': 'text-title-xl',
        '2xl': 'text-title-2xl',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  },
);

/**
 * Error404 Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The text variants are defined using the cva utility for consistent styling.
 */
export const error404TextVariants = cva(
  'mt-10 mb-6 text-muted-foreground',
  {
    variants: {
      size: {
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg',
      },
    },
    defaultVariants: {
      size: 'base',
    },
  },
);

/**
 * Error404 Button Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The button variants are defined using the cva utility for consistent styling.
 */
export const error404ButtonVariants = cva(
  'inline-flex items-center justify-center rounded-lg border px-5 py-3.5 text-sm font-medium shadow-theme-xs',
  {
    variants: {
      variant: {
        default: 'border-border bg-background text-foreground hover:bg-muted hover:text-foreground',
        primary: 'border-primary-200 bg-primary-50 text-primary-900 hover:bg-primary-100 dark:border-primary-800 dark:bg-primary-900/10 dark:text-primary-50 dark:hover:bg-primary-900/20',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
