import { cva } from 'class-variance-authority';

/**
 * Auth Layout Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const authLayoutContainerVariants = cva(
  'relative p-6 z-1 sm:p-0',
  {
    variants: {
      variant: {
        default: 'bg-background',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Auth Layout Wrapper Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The wrapper variants are defined using the cva utility for consistent styling.
 */
export const authLayoutWrapperVariants = cva(
  'relative flex lg:flex-row w-full h-screen justify-center flex-col sm:p-0',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Auth Layout Sidebar Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The sidebar variants are defined using the cva utility for consistent styling.
 */
export const authLayoutSidebarVariants = cva(
  'lg:w-1/2 w-full h-full lg:grid items-center hidden',
  {
    variants: {
      variant: {
        default: 'bg-primary dark:bg-primary/5',
        secondary: 'bg-secondary dark:bg-secondary/5',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Auth Layout Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The text variants are defined using the cva utility for consistent styling.
 */
export const authLayoutTextVariants = cva(
  'text-center',
  {
    variants: {
      variant: {
        default: 'text-primary-foreground dark:text-primary-foreground/60',
        secondary: 'text-secondary-foreground dark:text-secondary-foreground/60',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
