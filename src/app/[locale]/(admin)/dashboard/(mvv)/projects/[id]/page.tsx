'use client';

import React from 'react';
import TaskBreadcrumb from '@/features/project-management/components/workflow/layout/TaskBreadcrumb';
import WorkflowWrapper from '@/features/project-management/components/workflow/layout/WorkflowWrapper';
import { useParams } from 'next/navigation';
import { DirtyStepProvider } from '@/features/project-management/contexts/DirtyStepContext';

export default function WorkflowPage() {
  const params = useParams<{ id: string }>();
  const projectId = params.id;

  return (
    <div className="space-y-6 h-[calc(100vh-77px)] overflow-hidden flex flex-col">
      <DirtyStepProvider>
        <TaskBreadcrumb />

        <WorkflowWrapper projectId={projectId} />
      </DirtyStepProvider>
    </div>
  );
}
