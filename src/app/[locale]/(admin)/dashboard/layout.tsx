'use client';

import React from 'react';
import { useSidebar } from '@/shared/contexts/SidebarContext';
import AppHeader from '@/shared/layout/AppHeader';
import AppSidebar from '@/shared/layout/AppSidebar';
import Backdrop from '@/shared/layout/Backdrop';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();

  // Dynamic class for main content margin based on sidebar state
  const mainContentMargin = isMobileOpen
    ? 'ml-0'
    : isExpanded || isHovered
      ? 'lg:ml-[240px]'
      : 'lg:ml-[90px]';

  return (
    <div className="min-h-screen xl:flex">
      {/* Sidebar and Backdrop */}
      <AppSidebar />
      <Backdrop />
      {/* Main Content Area */}
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${mainContentMargin}`}
      >
        {/* Header */}
        <AppHeader />

        {/* Page Content - full-width */}
        <div>{children}</div>
      </div>
    </div>
  );
}
