import { cva } from 'class-variance-authority';

/**
 * Badge Page Card Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card variants are defined using the cva utility for consistent styling.
 */
export const badgePageCardVariants = cva(
  'rounded-2xl border',
  {
    variants: {
      variant: {
        default: 'border-border bg-card text-card-foreground',
        primary: 'border-primary-200 bg-primary-50/30 dark:border-primary-800 dark:bg-primary-900/10',
        secondary: 'border-secondary-200 bg-secondary-50/30 dark:border-secondary-800 dark:bg-secondary-900/10',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Badge Page Card Header Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card header variants are defined using the cva utility for consistent styling.
 */
export const badgePageCardHeaderVariants = cva(
  'px-6 py-5',
  {
    variants: {
      variant: {
        default: '',
        primary: 'bg-primary-100/50 dark:bg-primary-900/20',
        secondary: 'bg-secondary-100/50 dark:bg-secondary-900/20',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Badge Page Card Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card title variants are defined using the cva utility for consistent styling.
 */
export const badgePageCardTitleVariants = cva(
  'text-base font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
        primary: 'text-primary-900 dark:text-primary-50',
        secondary: 'text-secondary-900 dark:text-secondary-50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Badge Page Card Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The card content variants are defined using the cva utility for consistent styling.
 */
export const badgePageCardContentVariants = cva(
  'p-6 border-t xl:p-10',
  {
    variants: {
      variant: {
        default: 'border-border',
        primary: 'border-primary-100 dark:border-primary-800/30',
        secondary: 'border-secondary-100 dark:border-secondary-800/30',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
