import type { VariantProps } from 'class-variance-authority';
import type { Metadata } from 'next';
import React from 'react';
import PageBreadcrumb from '@/shared/components/common/PageBreadCrumb';
import { Badge } from '@/shared/components/ui/badge';
import {
  badgePageCardContentVariants,
  badgePageCardHeaderVariants,
  badgePageCardTitleVariants,
  badgePageCardVariants,
} from './badge-page-variants';

export const metadata: Metadata = {
  title: 'Next.js Badge | TailAdmin - Next.js Dashboard Template',
  description:
    'This is Next.js Badge page for TailAdmin - Next.js Tailwind CSS Admin Dashboard Template',
  // other metadata
};

type BadgeCardProps = {
  title: string;
  children: React.ReactNode;
  variant?: VariantProps<typeof badgePageCardVariants>['variant'];
};

/**
 * BadgeCard Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a card for displaying badge examples.
 */
const BadgeCard: React.FC<BadgeCardProps> = ({
  title,
  children,
  variant = 'default',
}) => {
  return (
    <div className={badgePageCardVariants({ variant })}>
      <div className={badgePageCardHeaderVariants({ variant })}>
        <h3 className={badgePageCardTitleVariants({ variant })}>
          {title}
        </h3>
      </div>
      <div className={badgePageCardContentVariants({ variant })}>
        {children}
      </div>
    </div>
  );
};

/**
 * BadgePage Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays various badge examples.
 */
export default function BadgePage() {
  return (
    <div>
      <PageBreadcrumb pageTitle="Badges" />
      <div className="space-y-5 sm:space-y-6">
        <BadgeCard title="With Light Background">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            {/* Light Variant */}
            <Badge variant="default" color="primary">
              Primary
            </Badge>
            <Badge variant="default" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="default" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="default" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="default" color="info">
              Info
            </Badge>
            <Badge variant="default" color="light">
              Light
            </Badge>
            <Badge variant="default" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>

        <BadgeCard title="With Solid Background" variant="primary">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            {/* Light Variant */}
            <Badge variant="outline" color="primary">
              Primary
            </Badge>
            <Badge variant="outline" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="outline" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="outline" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="outline" color="info">
              Info
            </Badge>
            <Badge variant="outline" color="light">
              Light
            </Badge>
            <Badge variant="outline" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>

        <BadgeCard title="Light Background with Left Icon">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            <Badge variant="default" color="primary">
              Primary
            </Badge>
            <Badge variant="default" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="default" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="default" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="default" color="info">
              Info
            </Badge>
            <Badge variant="default" color="light">
              Light
            </Badge>
            <Badge variant="default" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>

        <BadgeCard title="Solid Background with Left Icon" variant="primary">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            <Badge variant="outline" color="primary">
              Primary
            </Badge>
            <Badge variant="outline" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="outline" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="outline" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="outline" color="info">
              Info
            </Badge>
            <Badge variant="outline" color="light">
              Light
            </Badge>
            <Badge variant="outline" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>

        <BadgeCard title="Light Background with Right Icon" variant="secondary">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            <Badge variant="default" color="primary">
              Primary
            </Badge>
            <Badge variant="default" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="default" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="default" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="default" color="info">
              Info
            </Badge>
            <Badge variant="default" color="light">
              Light
            </Badge>
            <Badge variant="default" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>

        <BadgeCard title="Solid Background with Right Icon" variant="secondary">
          <div className="flex flex-wrap gap-4 sm:items-center sm:justify-center">
            <Badge variant="outline" color="primary">
              Primary
            </Badge>
            <Badge variant="outline" color="success">
              Success
            </Badge>
            {' '}
            <Badge variant="outline" color="error">
              Error
            </Badge>
            {' '}
            <Badge variant="outline" color="warning">
              Warning
            </Badge>
            {' '}
            <Badge variant="outline" color="info">
              Info
            </Badge>
            <Badge variant="outline" color="light">
              Light
            </Badge>
            <Badge variant="outline" color="dark">
              Dark
            </Badge>
          </div>
        </BadgeCard>
      </div>
    </div>
  );
}
