import type { VariantProps } from 'class-variance-authority';
import type { Metadata } from 'next';
import React from 'react';
import PageBreadcrumb from '@/shared/components/common/PageBreadCrumb';
import {
  blankPageContainerVariants,
  blankPageContentVariants,
  blankPageTextVariants,
  blankPageTitleVariants,
} from './blank-page-variants';

export const metadata: Metadata = {
  title: 'Next.js Blank Page | TailAdmin - Next.js Dashboard Template',
  description: 'This is Next.js Blank Page TailAdmin Dashboard Template',
};

type BlankPageProps = {
  containerVariant?: VariantProps<typeof blankPageContainerVariants>['variant'];
  contentVariant?: VariantProps<typeof blankPageContentVariants>['variant'];
  titleVariant?: VariantProps<typeof blankPageTitleVariants>['variant'];
  textVariant?: VariantProps<typeof blankPageTextVariants>['variant'];
};

/**
 * BlankPage Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a blank page template with customizable styling.
 */
export default function BlankPage() {
  const defaultVariants: BlankPageProps = {
    containerVariant: 'default',
    contentVariant: 'default',
    titleVariant: 'default',
    textVariant: 'default',
  };

  const { containerVariant, contentVariant, titleVariant, textVariant } = defaultVariants;

  return (
    <div>
      <PageBreadcrumb pageTitle="Blank Page" />
      <div className={blankPageContainerVariants({ variant: containerVariant })}>
        <div className={blankPageContentVariants({ variant: contentVariant })}>
          <h3 className={blankPageTitleVariants({ variant: titleVariant })}>
            Card Title Here
          </h3>
          <p className={blankPageTextVariants({ variant: textVariant })}>
            Start putting content on grids or panels, you can also use different
            combinations of grids.Please check out the dashboard and other pages
          </p>
        </div>
      </div>
    </div>
  );
}
