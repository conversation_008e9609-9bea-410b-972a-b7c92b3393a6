import { cva } from 'class-variance-authority';

/**
 * Blank Page Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const blankPageContainerVariants = cva(
  'min-h-screen rounded-2xl border px-5 py-7 xl:px-10 xl:py-12',
  {
    variants: {
      variant: {
        default: 'border-border bg-card',
        primary: 'border-primary-200 bg-primary-50/30 dark:border-primary-800 dark:bg-primary-900/10',
        secondary: 'border-secondary-200 bg-secondary-50/30 dark:border-secondary-800 dark:bg-secondary-900/10',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Blank Page Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The content variants are defined using the cva utility for consistent styling.
 */
export const blankPageContentVariants = cva(
  'mx-auto w-full max-w-[630px] text-center',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Blank Page Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The title variants are defined using the cva utility for consistent styling.
 */
export const blankPageTitleVariants = cva(
  'mb-4 font-medium text-theme-xl sm:text-2xl',
  {
    variants: {
      variant: {
        default: 'text-foreground',
        primary: 'text-primary-900 dark:text-primary-50',
        secondary: 'text-secondary-900 dark:text-secondary-50',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Blank Page Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The text variants are defined using the cva utility for consistent styling.
 */
export const blankPageTextVariants = cva(
  'text-sm sm:text-base',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
        primary: 'text-primary-700 dark:text-primary-300',
        secondary: 'text-secondary-700 dark:text-secondary-300',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);
