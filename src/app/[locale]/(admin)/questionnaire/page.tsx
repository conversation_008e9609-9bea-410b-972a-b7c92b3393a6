import QuestionnaireWrapper from '@/features/questionnaire/components/layouts/QuestionnaireWrapper';

type SearchParams = Promise<{ [key: string]: string }>;

export default async function Questionnaire({ searchParams }: { searchParams: SearchParams }) {
  const searchValue = await searchParams;
  if (!searchValue?.id) {
    throw new Error('Id is not found');
  }
  return <QuestionnaireWrapper />;
}
