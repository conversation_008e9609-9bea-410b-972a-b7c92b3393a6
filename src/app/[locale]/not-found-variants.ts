import { cva } from 'class-variance-authority';

/**
 * Error404 Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const error404ContainerVariants = cva(
  'relative flex flex-col items-center justify-center min-h-screen p-6 overflow-hidden z-1',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Error404 Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The content variants are defined using the cva utility for consistent styling.
 */
export const error404ContentVariants = cva(
  'mx-auto w-full text-center',
  {
    variants: {
      size: {
        sm: 'max-w-[242px]',
        md: 'max-w-[472px]',
      },
    },
    defaultVariants: {
      size: 'sm',
    },
  },
);

/**
 * Error404 Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The title variants are defined using the cva utility for consistent styling.
 */
export const error404TitleVariants = cva(
  'mb-8 font-medium',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
      size: {
        'md': 'text-title-md',
        'lg': 'text-title-lg',
        'xl': 'text-title-xl',
        '2xl': 'text-title-2xl',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  },
);

/**
 * Error404 Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The text variants are defined using the cva utility for consistent styling.
 */
export const error404TextVariants = cva(
  'mt-10 mb-6',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
      size: {
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'base',
    },
  },
);
