import { cva } from 'class-variance-authority';

/**
 * Error Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The container variants are defined using the cva utility for consistent styling.
 */
export const errorContainerVariants = cva(
  'relative flex flex-col items-center justify-center min-h-screen p-6 overflow-hidden z-1',
  {
    variants: {
      variant: {
        default: '',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

/**
 * Error Content Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The content variants are defined using the cva utility for consistent styling.
 */
export const errorContentVariants = cva(
  'mx-auto w-full text-center',
  {
    variants: {
      size: {
        sm: 'max-w-[155px]',
        md: 'max-w-[204px]',
        lg: 'max-w-[242px]',
        xl: 'max-w-[472px]',
      },
    },
    defaultVariants: {
      size: 'lg',
    },
  },
);

/**
 * Error Image Container Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The image container variants are defined using the cva utility for consistent styling.
 */
export const errorImageContainerVariants = cva(
  'mx-auto mb-10 w-full text-center',
  {
    variants: {
      size: {
        sm: 'max-w-[155px]',
        md: 'max-w-[204px]',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  },
);

/**
 * Error Title Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The title variants are defined using the cva utility for consistent styling.
 */
export const errorTitleVariants = cva(
  'mb-2 font-bold',
  {
    variants: {
      variant: {
        default: 'text-foreground',
      },
      size: {
        'md': 'text-title-md',
        'lg': 'text-title-lg',
        'xl': 'text-title-xl',
        '2xl': 'text-title-2xl',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  },
);

/**
 * Error Text Variants
 *
 * This component uses semantic color tokens from our theming system.
 * The text variants are defined using the cva utility for consistent styling.
 */
export const errorTextVariants = cva(
  'mt-6 mb-6',
  {
    variants: {
      variant: {
        default: 'text-muted-foreground',
      },
      size: {
        sm: 'text-sm',
        base: 'text-base',
        lg: 'text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'base',
    },
  },
);
