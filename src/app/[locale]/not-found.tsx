import type { VariantProps } from 'class-variance-authority';
import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import GridShape from '@/shared/components/common/GridShape';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/shared/utils/utils';
import {
  error404ContainerVariants,
  error404ContentVariants,
  error404TextVariants,
  error404TitleVariants,
} from './not-found-variants';

export const metadata: Metadata = {
  title: 'Error 404 | SNP',
  description:
    'This is Error 404 page for SNP',
};

type Error404Props = {
  containerVariant?: VariantProps<typeof error404ContainerVariants>['variant'];
  contentSize?: VariantProps<typeof error404ContentVariants>['size'];
  titleVariant?: VariantProps<typeof error404TitleVariants>['variant'];
  titleSize?: VariantProps<typeof error404TitleVariants>['size'];
  textVariant?: VariantProps<typeof error404TextVariants>['variant'];
  textSize?: VariantProps<typeof error404TextVariants>['size'];
};

/**
 * Error404 Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays a 404 error page with a message and a link to the home page.
 */
export default function Error404({
  containerVariant = 'default',
  contentSize = 'md',
  titleVariant = 'default',
  titleSize = 'md',
  textVariant = 'default',
  textSize = 'base',
}: Error404Props = {}) {
  return (
    <div className={cn(error404ContainerVariants({ variant: containerVariant }))}>
      <GridShape />
      <div className={cn(
        error404ContentVariants({ size: contentSize }),
        'sm:max-w-[472px]',
      )}
      >
        <h1 className={cn(
          error404TitleVariants({ variant: titleVariant, size: titleSize }),
          'xl:text-title-2xl',
        )}
        >
          ERROR
        </h1>

        <Image
          src="/images/error/404.svg"
          alt="404"
          className="dark:hidden"
          width={472}
          height={152}
        />
        <Image
          src="/images/error/404-dark.svg"
          alt="404"
          className="hidden dark:block"
          width={472}
          height={152}
        />

        <p className={cn(
          error404TextVariants({ variant: textVariant, size: textSize }),
          'sm:text-lg',
        )}
        >
          We can't seem to find the page you are looking for!
        </p>

        <Link
          href="/"
        >
          <Button>
            Back to Home Page
          </Button>
        </Link>
      </div>
    </div>
  );
}
