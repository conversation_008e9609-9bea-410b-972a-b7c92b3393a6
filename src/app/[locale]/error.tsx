'use client';

import type { VariantProps } from 'class-variance-authority';
import Image from 'next/image';
import Link from 'next/link';
import GridShape from '@/shared/components/common/GridShape';
import { Button } from '@/shared/components/ui/button';
import { cn } from '@/shared/utils/utils';
import {
  errorContainerVariants,
  errorContentVariants,
  errorImageContainerVariants,
  errorTextVariants,
  errorTitleVariants,
} from './error-variants';

type ErrorProps = {
  error: Error & { digest?: string };
  containerVariant?: VariantProps<typeof errorContainerVariants>['variant'];
  contentSize?: VariantProps<typeof errorContentVariants>['size'];
  imageContainerSize?: VariantProps<typeof errorImageContainerVariants>['size'];
  titleVariant?: VariantProps<typeof errorTitleVariants>['variant'];
  titleSize?: VariantProps<typeof errorTitleVariants>['size'];
  textVariant?: VariantProps<typeof errorTextVariants>['variant'];
  textSize?: VariantProps<typeof errorTextVariants>['size'];
};

/**
 * GlobalError Component
 *
 * This component uses semantic color tokens from our theming system.
 * It displays an error page with a message and a link to the home page.
 */
export default function GlobalError({
  error,
  containerVariant = 'default',
  contentSize = 'xl',
  imageContainerSize = 'md',
  titleVariant = 'default',
  titleSize = 'md',
  textVariant = 'default',
  textSize = 'base',
}: ErrorProps) {
  return (
    <div className={cn(errorContainerVariants({ variant: containerVariant }))}>
      <GridShape />
      <div className={cn(errorContentVariants({ size: contentSize }))}>
        <div className={cn(errorImageContainerVariants({ size: imageContainerSize }))}>
          <Image
            src="/images/error/maintenance.svg"
            alt="Error"
            className="dark:hidden"
            width={472}
            height={152}
          />
          <Image
            src="/images/error/maintenance-dark.svg"
            alt="Error"
            className="hidden dark:block"
            width={472}
            height={152}
          />
        </div>

        <h1 className={cn(
          errorTitleVariants({ variant: titleVariant, size: titleSize }),
          'xl:text-title-2xl',
        )}
        >
          ERROR
        </h1>

        <p className={cn(
          errorTextVariants({ variant: textVariant, size: textSize }),
          'sm:text-lg',
        )}
        >
          {error?.message}
        </p>

        <Link
          href="/"
        >
          <Button>
            Back to Home Page
          </Button>
        </Link>
      </div>
    </div>
  );
}
