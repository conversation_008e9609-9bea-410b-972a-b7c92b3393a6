import type { NextRequest } from 'next/server';
import { getUserFromSession } from '@/features/auth/session';
import { NextResponse } from 'next/server';
import { Env } from '@/core/config/Env';

/**
 * POST handler for making authenticated requests to external APIs
 * Retrieves token from cookies and forwards the request if authenticated
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { data, endpoint } = await request.json();

    // Get user session from cookies to retrieve token
    const user = await getUserFromSession(request.cookies);

    if (!user) {
      return NextResponse.json(
        {
          error: 'Authentication token not found. Please log in to continue.',
          statusCode: 401,
          message: 'Unauthorized',
        },
        { status: 401 },
      );
    }

    // Make the POST request to the external API
    const response = await fetch(`${Env.NEXT_PUBLIC_RUNTIME_API_URL}/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user.accessToken || user.token}`,
      },
      body: JSON.stringify(data),
    });

    // Parse the response
    const responseData = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          error: responseData.message || 'External API request failed',
          statusCode: response.status,
          message: 'External API Error',
          data: responseData,
        },
        { status: response.status },
      );
    }

    // Return successful response
    return NextResponse.json({
      statusCode: 200,
      message: 'Request successful',
      data: responseData,
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('Error in copilotkit-api route:', error);

    // Return an error response
    return NextResponse.json(
      {
        error: errorMessage,
        statusCode: 500,
        message: 'Internal Server Error',
      },
      { status: 500 },
    );
  }
}
