// app/api/download-docx/route.ts
import { NextResponse } from 'next/server';
import htmlToDocx from 'html-to-docx';

// Fix error lib with some text too long
(console as any).warning = console.warn;

export async function POST(request: Request) {
  try {
    const { data } = await request.json();
    const { html } = data;
    const docxBuffer = await htmlToDocx(html, {
      orientation: 'portrait',
      margins: { top: 720, right: 720, bottom: 720, left: 720 },
    });

    return new Response(docxBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="document.docx"`,
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: errorMessage,
        statusCode: 500,
        message: 'Internal Server Error',
      },
      { status: 500 },
    );
  }
}
