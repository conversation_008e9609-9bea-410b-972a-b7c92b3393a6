import type { NextRequest } from 'next/server';
import {
  copilotKitEndpoint,
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  OpenAIAdapter,
} from '@copilotkit/runtime';
import { Env } from '@/core/config/Env';
import OpenAI from 'openai';

const openai = new OpenAI();

const serviceAdapter = new OpenAIAdapter({ openai });

const runtime = new CopilotRuntime({
  remoteEndpoints: [
    copilotKitEndpoint({
      url: Env.NEXT_PUBLIC_RUNTIME_URL || 'https://mvv-brain.dev.minastik.com/api/copilotkit',
    }),
  ],
});

export const POST = async (req: NextRequest) => {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: '/api/copilotkit',
  });

  return handleRequest(req);
};
