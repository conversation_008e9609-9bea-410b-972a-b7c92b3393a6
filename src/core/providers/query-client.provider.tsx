'use client';

import type { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';

/**
 * Default query client options
 */
const defaultOptions = {
  queries: {
    refetchOnWindowFocus: false,
    retry: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes
  },
};

/**
 * React Query client provider component
 *
 * This component initializes a new QueryClient instance for each client
 * and provides it to the application via QueryClientProvider.
 */
export function ReactQueryProvider({ children }: { children: ReactNode }) {
  // Create a new QueryClient instance for each client
  const [queryClient] = useState(() => new QueryClient({ defaultOptions }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
