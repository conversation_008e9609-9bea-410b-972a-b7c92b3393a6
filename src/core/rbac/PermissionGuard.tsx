'use client';

import type { ReactNode } from 'react';
import type { Permission } from './permissions';
import { usePermissions } from './usePermissions';

type PermissionGuardProps = {
  permission?: Permission;
  children: ReactNode;
  fallback?: ReactNode;
};

/**
 * PermissionGuard component
 *
 * Conditionally renders children based on user permissions
 *
 * @example
 * <PermissionGuard permission={Permission.CREATE_PROJECT}>
 *   <Button>Create Project</Button>
 * </PermissionGuard>
 */
export function PermissionGuard({
  permission,
  children,
  fallback = null,
}: PermissionGuardProps) {
  const { can } = usePermissions();

  if (!permission || can(permission)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}
