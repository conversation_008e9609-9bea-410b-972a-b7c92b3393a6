import type { UserRole } from '@/features/auth/types/auth.types';
import type { User } from '@sentry/nextjs';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { getPermissionsForRoles, hasPermission, Permission } from './permissions';

// Define protected routes and required permissions
const protectedRoutes = [
  {
    pattern: '/dashboard/team-members(.*)',
    requiredPermission: Permission.VIEW_USERS,
  },
  // User-accessible routes (both admin and regular users)
  {
    pattern: '/dashboard/projects(.*)',
    requiredPermission: Permission.VIEW_PROJECTS,
  },
  {
    pattern: '/dashboard/prompts(.*)',
    requiredPermission: Permission.VIEW_PROMPTS,
  },
];

/**
 * Check if a path matches a route pattern
 */
function matchesPattern(path: string, pattern: string): boolean {
  return new RegExp(`^${pattern.replace(/\(\.+\)/g, '.*')}$`).test(path);
}

/**
 * RBAC middleware to protect routes based on user permissions
 */
export async function rbacMiddleware(request: NextRequest, user?: User | null) {
  const path = request.nextUrl.pathname;

  // Find the first matching protected route
  const matchedRoute = protectedRoutes.find(route =>
    matchesPattern(path, route.pattern),
  );

  // If no protected route matches, allow access
  if (!matchedRoute) {
    return null;
  }

  // If no user, redirect to login
  if (!user) {
    const locale = request.nextUrl.pathname.match(/(\/.*)\/dashboard/)?.at(1) ?? '';
    const signInUrl = new URL(`${locale}/signin`, request.url);
    return NextResponse.redirect(signInUrl.toString());
  }

  // Get user permissions from roles
  const roleNames = (user.roles as UserRole[]) || [];
  const userPermissions = getPermissionsForRoles(roleNames);

  // Check if user has required permission for this route
  const hasAccess = hasPermission(userPermissions, matchedRoute.requiredPermission);

  // If user doesn't have access, redirect to unauthorized page
  if (!hasAccess) {
    const locale = request.nextUrl.pathname.match(/(\/.*)\/dashboard/)?.at(1) ?? '';
    const unauthorizedUrl = new URL(`${locale}/unauthorized`, request.url);
    return NextResponse.redirect(unauthorizedUrl.toString());
  }

  // User has access, allow request
  return null;
}
