import type { UserRole } from '@/features/auth/types/auth.types';

export const ROLE_ADMIN = 'admin';
export const ROLE_USER = 'user';

// Define permission types
export enum Permission {
  // Project permissions
  VIEW_PROJECTS = 'view:projects',
  CREATE_PROJECT = 'create:project',
  EDIT_PROJECT = 'edit:project',
  DELETE_PROJECT = 'delete:project',

  // User permissions
  VIEW_USERS = 'view:users',
  CREATE_USER = 'create:user',
  EDIT_USER = 'edit:user',
  DELETE_USER = 'delete:user',

  // Prompt permissions
  VIEW_PROMPTS = 'view:prompts',
  CREATE_PROMPT = 'create:prompt',
  EDIT_PROMPT = 'edit:prompt',
  DELETE_PROMPT = 'delete:prompt',

  // Prompt permissions
  VIEW_STEPS = 'view:steps',
  CREATE_STEP = 'create:step',
  EDIT_STEP = 'edit:step',
  DELETE_STEP = 'delete:step',

  // Prompt permissions
  VIEW_FRAMEWORKS_TEMPLATES = 'view:frameworks_templates',
  CREATE_FRAMEWORKS_TEMPLATES = 'create:frameworks_templates',
  EDIT_FRAMEWORKS_TEMPLATES = 'edit:frameworks_templates',
  DELETE_FRAMEWORKS_TEMPLATES = 'delete:frameworks_templates',

  // Add more permissions as needed
}

// Map roles to permissions
const rolePermissionsMap: Record<string, Permission[]> = {
  [ROLE_ADMIN]: [
    // Admins have all permissions
    Permission.VIEW_PROJECTS,
    Permission.CREATE_PROJECT,
    Permission.EDIT_PROJECT,
    Permission.DELETE_PROJECT,

    Permission.VIEW_USERS,
    Permission.CREATE_USER,
    Permission.EDIT_USER,
    Permission.DELETE_USER,

    Permission.VIEW_PROMPTS,
    Permission.CREATE_PROMPT,
    Permission.EDIT_PROMPT,
    Permission.DELETE_PROMPT,

    Permission.VIEW_STEPS,
    Permission.CREATE_STEP,
    Permission.EDIT_STEP,
    Permission.DELETE_STEP,

    Permission.VIEW_FRAMEWORKS_TEMPLATES,
    Permission.CREATE_FRAMEWORKS_TEMPLATES,
    Permission.EDIT_FRAMEWORKS_TEMPLATES,
    Permission.DELETE_FRAMEWORKS_TEMPLATES,
  ],
  [ROLE_USER]: [
    // Regular users have limited permissions
    Permission.VIEW_PROJECTS,
    Permission.CREATE_PROJECT,
    Permission.EDIT_PROJECT,
    Permission.DELETE_PROJECT,

    Permission.VIEW_STEPS,
    Permission.CREATE_STEP,
    Permission.EDIT_STEP,
    Permission.DELETE_STEP,
  ],
};

/**
 * Get permissions for a given set of roles
 */
export function getPermissionsForRoles(roles: UserRole[] = []): Permission[] {
  const permissions = new Set<Permission>();

  // Add permissions for each role
  roles.forEach((role) => {
    const rolePermissions = rolePermissionsMap[role.name] || [];
    rolePermissions.forEach(permission => permissions.add(permission));
  });

  return Array.from(permissions);
}

/**
 * Check if a user has a specific permission
 */
export function hasPermission(userPermissions: Permission[], permission: Permission): boolean {
  return userPermissions.includes(permission);
}
