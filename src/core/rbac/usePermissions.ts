'use client';

import type { Permission } from './permissions';
import { useAuthStore } from '@/features/auth/store/auth.store';
import { useMemo } from 'react';
import { getPermissionsForRoles, hasPermission } from './permissions';

/**
 * Hook to check user permissions based on auth store
 *
 * @returns Object with permissions array and can function
 */
export function usePermissions() {
  const { user } = useAuthStore();

  // Extract role names from user roles
  const roleNames = useMemo(() => {
    return user?.roles || [];
  }, [user?.roles]);

  // Get permissions for user roles
  const permissions = useMemo(() => {
    return getPermissionsForRoles(roleNames);
  }, [roleNames]);

  // Check if user has a specific permission
  const can = useMemo(() => {
    return (permission: Permission) => hasPermission(permissions, permission);
  }, [permissions]);

  return {
    permissions,
    can,
  };
}
