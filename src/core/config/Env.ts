import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const Env = createEnv({
  server: {
    ARCJET_KEY: z.string().startsWith('ajkey_').optional(),
    DATABASE_URL: z.string().optional(),
  },
  client: {
    NEXT_PUBLIC_APP_URL: z.string().optional(),
    NEXT_PUBLIC_API_SERVER: z.string().min(1),
    NEXT_PUBLIC_RUNTIME_URL: z.string().min(1),
    NEXT_PUBLIC_RUNTIME_API_URL: z.string().min(1),
  },
  shared: {
    NODE_ENV: z.enum(['test', 'development', 'production']).optional(),
  },
  // You need to destructure all the keys manually
  runtimeEnv: {
    ARCJET_KEY: process.env.ARCJET_KEY,
    DATABASE_URL: process.env.DATABASE_URL,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_API_SERVER: process.env.NEXT_PUBLIC_API_SERVER,
    NEXT_PUBLIC_RUNTIME_URL: process.env.NEXT_PUBLIC_RUNTIME_URL,
    NEXT_PUBLIC_RUNTIME_API_URL: process.env.NEXT_PUBLIC_RUNTIME_API_URL,
  },
});
