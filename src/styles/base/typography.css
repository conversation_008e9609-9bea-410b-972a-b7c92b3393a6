/*
 * Global Typography Styles
 * This file applies typography tokens to native HTML elements
 * for consistent styling throughout the application.
 */

@layer base {
  /* Headings */
  h1 {
    @apply text-heading-1 font-medium tracking-tight text-foreground;
    margin-bottom: 0.5em;
  }

  h2 {
    @apply text-heading-2 font-medium tracking-tight text-foreground;
    margin-bottom: 0.5em;
  }

  h3 {
    @apply text-heading-3 font-medium tracking-tight text-foreground;
    margin-bottom: 0.5em;
  }

  h4 {
    @apply text-heading-4 font-medium tracking-tight text-foreground;
    margin-bottom: 0.5em;
  }

  h5 {
    @apply text-heading-5 font-medium text-foreground;
    margin-bottom: 0.5em;
  }

  h6 {
    @apply text-heading-6 font-medium text-foreground;
    margin-bottom: 0.5em;
  }

  /* Body text */
  p {
    @apply text-body-medium font-normal text-muted-foreground;
    margin-bottom: 1em;
  }

  /* Inline elements */
  span {
    @apply text-muted-foreground;
  }

  a {
    @apply text-primary hover:no-underline focus:outline-none focus-visible:ring-2 focus-visible:ring-ring;
    text-decoration: none;
  }

  button {
    @apply text-sm font-medium;
  }

  /* Responsive adjustments for small screens */
  @media (max-width: 640px) {
    h1 {
      @apply text-heading-2;
    }

    h2 {
      @apply text-heading-3;
    }

    h3 {
      @apply text-heading-4;
    }

    h4 {
      @apply text-heading-5;
    }

    h5 {
      @apply text-heading-6;
    }

    h6 {
      @apply text-xl;
    }
  }

  /* Responsive adjustments for extra small screens */
  @media (max-width: 375px) {
    h1 {
      @apply text-heading-3;
    }

    h2 {
      @apply text-heading-4;
    }

    h3 {
      @apply text-heading-5;
    }

    h4 {
      @apply text-heading-6;
    }

    h5 {
      @apply text-xl;
    }

    h6 {
      @apply text-lg;
    }
  }
}
