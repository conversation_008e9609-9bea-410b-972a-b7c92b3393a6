/*
 * Utilities
 * This file contains utility classes and base styles
 */

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }

  button:not(:disabled),
  [role='button']:not(:disabled) {
    cursor: pointer;
  }

  body {
    @apply relative font-normal font-lexend z-1 bg-gray-50;
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* For Remove Date Icon */
  input[type='date']::-webkit-inner-spin-button,
  input[type='time']::-webkit-inner-spin-button,
  input[type='date']::-webkit-calendar-picker-indicator,
  input[type='time']::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }

  /* Font styles */
  .font-italic {
    font-style: italic;
  }
}

.input-date-icon::-webkit-inner-spin-button,
.input-date-icon::-webkit-calendar-picker-indicator {
  opacity: 0;
  -webkit-appearance: none;
}
