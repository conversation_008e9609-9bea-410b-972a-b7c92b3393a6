/*
 * CSS Variables and Theme Definitions
 * This file contains all the CSS variables used throughout the application
 *
 * The variables are organized by category:
 * 1. Typography
 * 2. Spacing and Layout
 * 3. Colors
 * 4. Shadows
 * 5. Z-indices
 * 6. Miscellaneous
 */

@custom-variant dark (&:is(.dark *));

/**
 * Typography
 * Font families, sizes, weights, line heights, and letter spacing
 */
@theme {
  /* Font families */
  --font-*: initial;
  --font-outfit: 'Lexend Deca', sans-serif;
  --font-lexend: 'Lexend Deca', sans-serif;

  /* Font weights */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Letter spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Base font sizes with line heights */
  --text-xs: 0.75rem; /* 12px */
  --text-xs--line-height: 1rem; /* 16px */
  --text-sm: 0.875rem; /* 14px */
  --text-sm--line-height: 1.25rem; /* 20px */
  --text-base: 1rem; /* 16px */
  --text-base--line-height: 1.5rem; /* 24px */
  --text-lg: 1.125rem; /* 18px */
  --text-lg--line-height: 1.75rem; /* 28px */
  --text-xl: 1.25rem; /* 20px */
  --text-xl--line-height: 1.75rem; /* 28px */
  --text-2xl: 1.5rem; /* 24px */
  --text-2xl--line-height: 2rem; /* 32px */
  --text-3xl: 1.875rem; /* 30px */
  --text-3xl--line-height: 2.25rem; /* 36px */
  --text-4xl: 2.25rem; /* 36px */
  --text-4xl--line-height: 2.5rem; /* 40px */
  --text-5xl: 3rem; /* 48px */
  --text-5xl--line-height: 3.5rem; /* 56px */
  --text-6xl: 3.75rem; /* 60px */
  --text-6xl--line-height: 4.5rem; /* 72px */
  --text-7xl: 4.5rem; /* 72px */
  --text-7xl--line-height: 5.625rem; /* 90px */
  --text-8xl: 6rem; /* 96px */
  --text-8xl--line-height: 7.5rem; /* 120px */
  --text-9xl: 8rem; /* 128px */
  --text-9xl--line-height: 10rem; /* 160px */

  /* Semantic typography tokens */
  /* Headings */
  --text-heading-1: var(--text-5xl);
  --text-heading-1--line-height: var(--text-5xl--line-height);
  --text-heading-2: var(--text-4xl);
  --text-heading-2--line-height: var(--text-4xl--line-height);
  --text-heading-3: var(--text-3xl);
  --text-heading-3--line-height: var(--text-3xl--line-height);
  --text-heading-4: var(--text-2xl);
  --text-heading-4--line-height: var(--text-2xl--line-height);
  --text-heading-5: var(--text-xl);
  --text-heading-5--line-height: var(--text-xl--line-height);
  --text-heading-6: var(--text-lg);
  --text-heading-6--line-height: var(--text-lg--line-height);

  /* Body text */
  --text-body-large: var(--text-xl);
  --text-body-large--line-height: var(--text-xl--line-height);
  --text-body-medium: var(--text-base);
  --text-body-medium--line-height: var(--text-base--line-height);
  --text-body-small: var(--text-sm);
  --text-body-small--line-height: var(--text-sm--line-height);
  --text-body-xs: var(--text-xs);
  --text-body-xs--line-height: var(--text-xs--line-height);

  /* Display text (for hero sections, etc.) */
  --text-display-1: var(--text-9xl);
  --text-display-1--line-height: var(--text-9xl--line-height);
  --text-display-2: var(--text-8xl);
  --text-display-2--line-height: var(--text-8xl--line-height);

  /* Preserve existing title tokens for backward compatibility */
  /* --text-title-2xl: 72px; */
  --text-title-2xl--line-height: 90px;
  /* --text-title-xl: 60px; */
  --text-title-xl--line-height: 72px;
  /* --text-title-lg: 48px; */
  --text-title-lg--line-height: 60px;
  /* --text-title-md: 36px; */
  --text-title-md--line-height: 44px;
  /* --text-title-sm: 30px; */
  --text-title-sm--line-height: 38px;
  --text-theme-xl: 20px;
  --text-theme-xl--line-height: 30px;
  --text-theme-sm: 14px;
  --text-theme-sm--line-height: 20px;
  --text-theme-xs: 12px;
  --text-theme-xs--line-height: 18px;

  /**
   * Spacing and Layout
   * Breakpoints and other layout-related variables
   */

  /* Breakpoints */
  --breakpoint-*: initial;
  --breakpoint-2xsm: 375px;
  --breakpoint-xsm: 425px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1440px;
  --breakpoint-3xl: 2000px;

  /* Z-indices */
  --z-index-1: 1;
  --z-index-9: 9;
  --z-index-99: 99;
  --z-index-999: 999;
  --z-index-9999: 9999;
  --z-index-99999: 99999;
  --z-index-999999: 999999;

  /**
   * Colors
   * Base colors, semantic colors, and color scales
   */

  /* Base colors */
  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #101828;

  /* Gray scale */
  /* --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f2f4f7;
  --color-gray-200: #e4e7ec;
  --color-gray-300: #d0d5dd;
  --color-gray-400: #98a2b3;
  --color-gray-500: #667085;
  --color-gray-600: #475467;
  --color-gray-700: #344054;
  --color-gray-800: #1d2939;
  --color-gray-900: #101828;
  --color-gray-950: #0c111d; */
  --color-gray-dark: #1a2231;

  /* Brand colors */
  --color-brand-25: #fffdf5;
  --color-brand-50: #fffaeb;
  --color-brand-100: #fef5d3;
  --color-brand-200: #feeba7;
  --color-brand-300: #fddb6f;
  --color-brand-400: #f8d43a;
  --color-brand-500: #f1c406;
  --color-brand-600: #d9ae05;
  --color-brand-700: #b38d04;
  --color-brand-800: #8c6e03;
  --color-brand-900: #735a02;
  --color-brand-950: #3d2f01;

  --color-brand-light-25: #fffef7;
  --color-brand-light-50: #fffdf0;
  --color-brand-light-100: #fff8d6;
  --color-brand-light-200: #fff2ad;
  --color-brand-light-300: #ffe77a;
  --color-brand-light-400: #ffdc47;
  --color-brand-light-500: #ffd014;
  --color-brand-light-600: #e6b800;
  --color-brand-light-700: #cc9900;
  --color-brand-light-800: #a37a00;
  --color-brand-light-900: #856400;
  --color-brand-light-950: #473500;

  /* Status colors */
  --color-success-25: #f6fef9;
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;
  --color-success-950: #053321;

  --color-error-25: #fffbfa;
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;
  --color-error-950: #55160c;

  --color-warning-25: #fffcf5;
  --color-warning-50: #fffaeb;
  --color-warning-100: #fef0c7;
  --color-warning-200: #fedf89;
  --color-warning-300: #fec84b;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #93370d;
  --color-warning-900: #7a2e0e;
  --color-warning-950: #4e1d09;

  --color-orange-25: #fffaf5;
  --color-orange-50: #fff6ed;
  --color-orange-100: #ffead5;
  --color-orange-200: #fddcab;
  --color-orange-300: #feb273;
  --color-orange-400: #fd853a;
  --color-orange-500: #fb6514;
  --color-orange-600: #ec4a0a;
  --color-orange-700: #c4320a;
  --color-orange-800: #9c2a10;
  --color-orange-900: #7e2410;
  --color-orange-950: #511c10;

  /* Info colors */
  --color-info-25: #f5faff;
  --color-info-50: #eff8ff;
  --color-info-100: #d1e9ff;
  --color-info-200: #b2ddff;
  --color-info-300: #84caff;
  --color-info-400: #53b1fd;
  --color-info-500: #2196f3;
  --color-info-600: #0072f5;
  --color-info-700: #005bc4;
  --color-info-800: #004593;
  --color-info-900: #00337a;
  --color-info-950: #001a3d;

  /* Additional theme colors */
  --color-theme-pink-500: #ee46bc;
  --color-theme-purple-500: #7a5af8;

  /**
   * Shadows
   * Box shadows and drop shadows
   */
  --shadow-theme-xs: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  --shadow-theme-sm: 0px 1px 3px 0px rgba(16, 24, 40, 0.1), 0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  --shadow-theme-md: 0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-theme-lg: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-theme-xl: 0px 20px 24px -4px rgba(16, 24, 40, 0.08), 0px 8px 8px -4px rgba(16, 24, 40, 0.03);

  /* Special shadows */
  --shadow-datepicker: -5px 0 0 #262d3c, 5px 0 0 #262d3c;
  --shadow-focus-ring: 0px 0px 0px 4px rgba(70, 95, 255, 0.12);
  --shadow-slider-navigation: 0px 1px 2px 0px rgba(16, 24, 40, 0.1), 0px 1px 3px 0px rgba(16, 24, 40, 0.1);
  --shadow-tooltip: 0px 4px 6px -2px rgba(16, 24, 40, 0.05), -8px 0px 20px 8px rgba(16, 24, 40, 0.05);
  --drop-shadow-4xl: 0 35px 35px rgba(0, 0, 0, 0.25), 0 45px 65px rgba(0, 0, 0, 0.15);
}

/**
 * Semantic Theme Tokens
 * These tokens map theme-specific values to semantic variables
 */
@theme inline {
  /* Border radius tokens */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Base UI colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Component colors */
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  /* Semantic colors */
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);

  /* Chart colors */
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);

  /* Sidebar-specific colors */
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Surface colors (from surface.css) */
  --color-surface: var(--surface);
  --color-surface-hover: var(--surface-hover);
  --color-surface-selected: var(--surface-selected);
  --color-surface-divider: var(--surface-divider);
}

/* Light theme variables */
:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --success: oklch(0.4 0.2 142.5);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.7 0.2 85);
  --warning-foreground: oklch(0.1 0 0);
  --info: oklch(0.5 0.2 240);
  --info-foreground: oklch(1 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  --height-header: 80px;
}

/* Dark theme variables */
.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(1 0 0);
  --success: oklch(0.5 0.2 142.5);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.7 0.2 85);
  --warning-foreground: oklch(0.1 0 0);
  --info: oklch(0.6 0.2 240);
  --info-foreground: oklch(1 0 0);
  --border: oklch(0.35 0 0);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Yellow theme variables */
.theme-yellow {
  --background: oklch(0.98 0.05 85);
  --foreground: oklch(0.2 0.05 85);
  --card: oklch(0.95 0.05 85);
  --card-foreground: oklch(0.2 0.05 85);
  --popover: oklch(0.95 0.05 85);
  --popover-foreground: oklch(0.2 0.05 85);
  --primary: oklch(0.8 0.15 85);
  --primary-foreground: oklch(0.1 0.05 85);
  --secondary: oklch(0.9 0.1 85);
  --secondary-foreground: oklch(0.2 0.05 85);
  --muted: oklch(0.9 0.05 85);
  --muted-foreground: oklch(0.5 0.05 85);
  --accent: oklch(0.9 0.1 85);
  --accent-foreground: oklch(0.2 0.05 85);
  --destructive: oklch(0.65 0.2 25);
  --destructive-foreground: oklch(1 0 0);
  --success: oklch(0.5 0.2 142.5);
  --success-foreground: oklch(1 0 0);
  --warning: oklch(0.7 0.2 85);
  --warning-foreground: oklch(0.1 0 0);
  --info: oklch(0.6 0.2 240);
  --info-foreground: oklch(1 0 0);
  --border: oklch(0.85 0.05 85);
  --input: oklch(0.85 0.05 85);
  --ring: oklch(0.7 0.1 85);
  --chart-1: oklch(0.7 0.15 85);
  --chart-2: oklch(0.65 0.15 65);
  --chart-3: oklch(0.6 0.15 45);
  --chart-4: oklch(0.55 0.15 25);
  --chart-5: oklch(0.5 0.15 5);
  --sidebar: oklch(0.95 0.05 85);
  --sidebar-foreground: oklch(0.2 0.05 85);
  --sidebar-primary: oklch(0.8 0.15 85);
  --sidebar-primary-foreground: oklch(0.1 0.05 85);
  --sidebar-accent: oklch(0.9 0.1 85);
  --sidebar-accent-foreground: oklch(0.2 0.05 85);
  --sidebar-border: oklch(0.85 0.05 85);
  --sidebar-ring: oklch(0.7 0.1 85);
}
