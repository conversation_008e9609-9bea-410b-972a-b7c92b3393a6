/*
 * Animations
 * This file contains all the animation keyframes and animation classes
 */

@layer base {
  /* Sound wave animation */
  @keyframes sound-wave {
    0% {
      height: 10%;
    }

    50% {
      height: 100%;
    }

    100% {
      height: 10%;
    }
  }

  .animate-sound-wave {
    animation: sound-wave 1.2s ease-in-out infinite;
  }

  /* Fade in animation */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out forwards;
  }

  /* Shiny animation */
  @keyframes shine {
    0% {
      background-position: 200%;
    }

    100% {
      background-position: -200%;
    }
  }

  .animate-shine {
    animation: shine 5s linear infinite;
  }

  /* Slide left animation */
  @keyframes slideLeft {
    from {
      opacity: 0;
      transform: translateX(20px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-slide-left {
    animation: slideLeft 0.3s ease-in-out forwards;
  }

  /* Slide right animation */
  @keyframes slideRight {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .animate-slide-right {
    animation: slideRight 0.3s ease-in-out forwards;
  }
}

@layer utilities {
  .animated-border-gradient {
    background: linear-gradient(-45deg, #8e2de2, #4a00e0, #00c6ff, #0072ff, #00ff6a, #ffcc00);
    background-size: 600% 600%;
    animation: gradientMove 5s ease infinite;
  }

  @keyframes gradientMove {
    0% {
      background-position: 0% 50%;
    }

    50% {
      background-position: 100% 50%;
    }

    100% {
      background-position: 0% 50%;
    }
  }
}
