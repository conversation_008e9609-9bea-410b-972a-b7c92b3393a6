/**
 * Base Theme Tokens
 *
 * This file defines the base theme tokens that are shared across all themes.
 * These tokens provide a foundation for creating consistent UI components.
 */

export type ThemeTokens = {
  // Color tokens
  colors: {
    // Base colors
    background: string;
    foreground: string;

    // Surface colors
    surface: {
      DEFAULT: string;
      hover: string;
      selected: string;
      divider: string;
    };

    // Primary colors
    primary: {
      DEFAULT: string;
      foreground: string;
      50: string;
      100: string;
      200: string;
      300: string;
      400: string;
      500: string;
      600: string;
      700: string;
      800: string;
      900: string;
    };

    // Secondary colors
    secondary: {
      DEFAULT: string;
      foreground: string;
      50: string;
      100: string;
      200: string;
      300: string;
      400: string;
      500: string;
      600: string;
      700: string;
      800: string;
      900: string;
    };

    // Accent colors
    accent: {
      DEFAULT: string;
      foreground: string;
    };

    // Muted colors
    muted: {
      DEFAULT: string;
      foreground: string;
    };

    // UI element colors
    card: {
      DEFAULT: string;
      foreground: string;
    };

    popover: {
      DEFAULT: string;
      foreground: string;
    };

    border: string;
    input: string;
    ring: string;

    // Semantic status colors
    destructive: {
      DEFAULT: string;
      foreground: string;
    };

    success: {
      DEFAULT: string;
      foreground: string;
    };

    warning: {
      DEFAULT: string;
      foreground: string;
    };

    info: {
      DEFAULT: string;
      foreground: string;
    };

    // Chart colors
    chart: {
      1: string;
      2: string;
      3: string;
      4: string;
      5: string;
    };
  };

  // Border radius tokens
  radius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };

  // Typography tokens
  typography: {
    fontFamily: {
      lexend: string;
    };
    fontSize: {
      'title-2xl': string;
      'title-xl': string;
      'title-lg': string;
      'title-md': string;
      'title-sm': string;
      'theme-xl': string;
      'theme-sm': string;
      'theme-xs': string;
    };
    lineHeight: {
      'title-2xl': string;
      'title-xl': string;
      'title-lg': string;
      'title-md': string;
      'title-sm': string;
      'theme-xl': string;
      'theme-sm': string;
      'theme-xs': string;
    };
  };
};

/**
 * CSS Variable Mapping
 *
 * This function generates CSS variable declarations from theme tokens.
 * It's used to create the CSS variables that will be injected into the :root selector.
 */
export function generateCssVariables(tokens: ThemeTokens): Record<string, string> {
  return {
    // Color variables
    '--color-background': tokens.colors.background,
    '--color-foreground': tokens.colors.foreground,

    // Surface colors
    '--color-surface': tokens.colors.surface.DEFAULT,
    '--color-surface-hover': tokens.colors.surface.hover,
    '--color-surface-selected': tokens.colors.surface.selected,
    '--color-surface-divider': tokens.colors.surface.divider,

    // Primary colors
    '--color-primary': tokens.colors.primary.DEFAULT,
    '--color-primary-foreground': tokens.colors.primary.foreground,
    '--color-primary-50': tokens.colors.primary[50],
    '--color-primary-100': tokens.colors.primary[100],
    '--color-primary-200': tokens.colors.primary[200],
    '--color-primary-300': tokens.colors.primary[300],
    '--color-primary-400': tokens.colors.primary[400],
    '--color-primary-500': tokens.colors.primary[500],
    '--color-primary-600': tokens.colors.primary[600],
    '--color-primary-700': tokens.colors.primary[700],
    '--color-primary-800': tokens.colors.primary[800],
    '--color-primary-900': tokens.colors.primary[900],

    // Secondary colors
    '--color-secondary': tokens.colors.secondary.DEFAULT,
    '--color-secondary-foreground': tokens.colors.secondary.foreground,
    '--color-secondary-50': tokens.colors.secondary[50],
    '--color-secondary-100': tokens.colors.secondary[100],
    '--color-secondary-200': tokens.colors.secondary[200],
    '--color-secondary-300': tokens.colors.secondary[300],
    '--color-secondary-400': tokens.colors.secondary[400],
    '--color-secondary-500': tokens.colors.secondary[500],
    '--color-secondary-600': tokens.colors.secondary[600],
    '--color-secondary-700': tokens.colors.secondary[700],
    '--color-secondary-800': tokens.colors.secondary[800],
    '--color-secondary-900': tokens.colors.secondary[900],

    // Other UI colors
    '--color-accent': tokens.colors.accent.DEFAULT,
    '--color-accent-foreground': tokens.colors.accent.foreground,
    '--color-muted': tokens.colors.muted.DEFAULT,
    '--color-muted-foreground': tokens.colors.muted.foreground,
    '--color-card': tokens.colors.card.DEFAULT,
    '--color-card-foreground': tokens.colors.card.foreground,
    '--color-popover': tokens.colors.popover.DEFAULT,
    '--color-popover-foreground': tokens.colors.popover.foreground,
    '--color-border': tokens.colors.border,
    '--color-input': tokens.colors.input,
    '--color-ring': tokens.colors.ring,

    // Status colors
    '--color-destructive': tokens.colors.destructive.DEFAULT,
    '--color-destructive-foreground': tokens.colors.destructive.foreground,
    '--color-success': tokens.colors.success.DEFAULT,
    '--color-success-foreground': tokens.colors.success.foreground,
    '--color-warning': tokens.colors.warning.DEFAULT,
    '--color-warning-foreground': tokens.colors.warning.foreground,
    '--color-info': tokens.colors.info.DEFAULT,
    '--color-info-foreground': tokens.colors.info.foreground,

    // Chart colors
    '--color-chart-1': tokens.colors.chart[1],
    '--color-chart-2': tokens.colors.chart[2],
    '--color-chart-3': tokens.colors.chart[3],
    '--color-chart-4': tokens.colors.chart[4],
    '--color-chart-5': tokens.colors.chart[5],

    // Border radius
    '--radius-sm': tokens.radius.sm,
    '--radius-md': tokens.radius.md,
    '--radius-lg': tokens.radius.lg,
    '--radius-xl': tokens.radius.xl,
  };
}
