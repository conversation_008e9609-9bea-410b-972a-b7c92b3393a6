/*
 * Surface Theme Variables
 * This file defines CSS variables for surface-related colors and properties.
 * These variables are referenced in the tailwind.config.js file.
 */

/* Light theme surface variables */
:root {
  /* Base surface color */
  --color-surface: oklch(0.98 0 0);

  /* Surface interaction states */
  --color-surface-hover: oklch(0.96 0 0);
  --color-surface-selected: oklch(0.94 0 0);

  /* Surface dividers */
  --color-surface-divider: oklch(0.92 0 0);
}

/* Dark theme surface variables */
.dark {
  /* Base surface color */
  --color-surface: oklch(0.2 0 0);

  /* Surface interaction states */
  --color-surface-hover: oklch(0.25 0 0);
  --color-surface-selected: oklch(0.3 0 0);

  /* Surface dividers */
  --color-surface-divider: oklch(0.35 0 0);
}

/* Yellow theme surface variables */
.theme-yellow {
  /* Base surface color */
  --color-surface: oklch(0.95 0.05 85);

  /* Surface interaction states */
  --color-surface-hover: oklch(0.9 0.07 85);
  --color-surface-selected: oklch(0.85 0.1 85);

  /* Surface dividers */
  --color-surface-divider: oklch(0.8 0.05 85);
}

/* Blue theme surface variables */
.theme-blue {
  /* Base surface color */
  --color-surface: oklch(0.95 0.02 240);

  /* Surface interaction states */
  --color-surface-hover: oklch(0.9 0.04 240);
  --color-surface-selected: oklch(0.85 0.06 240);

  /* Surface dividers */
  --color-surface-divider: oklch(0.8 0.02 240);
}

/* Frappe theme surface variables */
.theme-frappe {
  /* Base surface color */
  --color-surface: oklch(0.98 0 0);

  /* Surface interaction states */
  --color-surface-hover: oklch(0.96 0 0);
  --color-surface-selected: oklch(0.94 0 0);

  /* Surface dividers */
  --color-surface-divider: oklch(0.92 0 0);
}
