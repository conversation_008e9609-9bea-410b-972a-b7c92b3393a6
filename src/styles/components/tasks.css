/*
 * Task Styles
 * This file contains styles for task components
 */

.taskCheckbox:checked ~ .box span {
  @apply opacity-100 bg-brand-500;
}

.taskCheckbox:checked ~ p {
  @apply text-gray-400 line-through;
}

.taskCheckbox:checked ~ .box {
  @apply border-brand-500 bg-brand-500 dark:border-brand-500;
}

.task {
  transition: all 0.2s ease; /* Smooth transition for visual effects */
}

.task {
  border-radius: 0.75rem;
  box-shadow:
    0 1px 3px 0 rgba(16, 24, 40, 0.1),
    0 1px 2px 0 rgba(16, 24, 40, 0.06);
  opacity: 0.8;
  cursor: grabbing; /* Changes the cursor to indicate dragging */
}
