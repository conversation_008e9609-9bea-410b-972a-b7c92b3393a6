/*
 * Flatpickr Styles
 * This file contains styles for the Flatpickr date picker
 */

.flatpickr-wrapper {
  @apply w-full;
}

.flatpickr-calendar {
  @apply mt-2 bg-white! rounded-xl! p-5! border! border-gray-200! text-gray-500 dark:text-gray-300! dark:bg-gray-dark! dark:text-gray-400! dark:shadow-theme-xl! 2xsm:w-auto!;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  @apply stroke-brand-500;
}

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
  @apply hidden;
}

.flatpickr-current-month {
  @apply p-0!;
}

.flatpickr-current-month .cur-month,
.flatpickr-current-month input.cur-year {
  @apply h-auto! pt-0! text-lg! font-medium! text-gray-800! dark:text-white/90!;
}

.flatpickr-prev-month,
.flatpickr-next-month {
  @apply p-0!;
}

.flatpickr-weekdays {
  @apply h-auto mt-6 mb-4 bg-transparent!;
}

.flatpickr-weekday {
  @apply text-theme-sm! font-medium! text-gray-500 dark:text-gray-300! dark:text-gray-400! bg-transparent!;
}

.flatpickr-day {
  @apply flex! items-center! text-theme-sm! font-medium! text-gray-800! dark:text-white/90! dark:hover:border-gray-300! dark:hover:bg-gray-900!;
}

.flatpickr-day.nextMonthDay,
.flatpickr-day.prevMonthDay {
  @apply text-gray-400!;
}

.flatpickr-months > .flatpickr-month {
  background: none !important;
}

.flatpickr-month .flatpickr-current-month .flatpickr-monthDropdown-months {
  background: none !important;
  appearance: none;
  background-image: none !important;
  font-weight: 500;
}

.flatpickr-month .flatpickr-current-month .flatpickr-monthDropdown-months:focus {
  outline: none !important;
  border: 0 !important;
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply top-7! dark:fill-white! dark:text-white! bg-transparent!;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  @apply left-7!;
}

.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  @apply right-7!;
}

.flatpickr-days {
  @apply border-0!;
}

span.flatpickr-weekday,
.flatpickr-months .flatpickr-month {
  @apply dark:fill-white! dark:text-white! bg-none!;
}

.flatpickr-innerContainer {
  @apply border-b-0!;
}

.flatpickr-months .flatpickr-month {
  @apply bg-none!;
}

.flatpickr-day.inRange {
  box-shadow:
    -5px 0 0 #f9fafb,
    5px 0 0 #f9fafb !important;
  @apply dark:shadow-datepicker!;
}

.flatpickr-day.inRange,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.today.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day:hover,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day:focus,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.nextMonthDay:focus {
  @apply border-gray-50! bg-gray-50! dark:border-0! dark:border-white/5! dark:bg-white/5!;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.selected,
.flatpickr-day.endRange {
  @apply text-white! dark:text-white!;
}

.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: #465fff;
  @apply border-brand-500! bg-brand-500! hover:border-brand-500! hover:bg-brand-500!;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)) {
  box-shadow: -10px 0 0 #465fff;
}

.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  @apply hover:fill-none!;
}

.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: none !important;
}

.flatpickr-calendar.static {
  @apply right-0;
}
