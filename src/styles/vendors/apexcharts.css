/* Default legend styling */
.apexcharts-legend-text {
  @apply text-gray-700! dark:text-gray-400!;
}

/* Default text styling */
.apexcharts-text {
  @apply fill-gray-700! dark:fill-gray-400!;
}

/* Default tooltip styling */
.apexcharts-tooltip.apexcharts-theme-light {
  @apply gap-1 rounded-lg! border-gray-200! p-3 shadow-theme-sm! dark:border-gray-800! dark:bg-gray-900!;
}

/* Horizontal bar chart specific tooltip styling - high contrast */
.horizontal-bar-chart .apexcharts-tooltip.apexcharts-theme-light {
  @apply bg-white! dark:bg-white!;
}

/* Default legend styling */
.apexcharts-legend-text,
.horizontal-bar-chart .apexcharts-legend-text {
  @apply pl-5! text-gray-700! dark:text-gray-400!;
}

/* Horizontal bar chart specific tooltip styling */
.horizontal-bar-chart .apexcharts-tooltip-series-group {
  @apply p-0!;
}

.horizontal-bar-chart .apexcharts-tooltip-y-group {
  @apply p-0!;
}

/* Default tooltip text styling */
.horizontal-bar-chart .apexcharts-tooltip-title {
  @apply mb-0! border-b-0! bg-transparent! p-0! text-[10px]! leading-4! text-gray-800! dark:text-white/90!;
}

.horizontal-bar-chart .apexcharts-tooltip-text {
  @apply text-theme-xs! text-gray-700! dark:text-white/90!;
}

/* Horizontal bar chart specific tooltip text styling - high contrast */
.horizontal-bar-chart .apexcharts-tooltip-title {
  @apply dark:text-gray-800!;
}

.horizontal-bar-chart .apexcharts-tooltip-text {
  @apply dark:text-gray-700!;
}

.horizontal-bar-chart .apexcharts-tooltip-text-y-value {
  @apply font-medium!;
}

/* Default gridline styling */
.apexcharts-gridline {
  @apply stroke-gray-100! dark:stroke-gray-800!;
}
