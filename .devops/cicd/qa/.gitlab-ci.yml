stages:
  - deploy

deploy:
  stage: deploy
  needs: []
  variables:
    CONTAINER_NAME: mvv-ui_qa
    DOCKER_IMAGE_NAME: mvv/ui:qa
  before_script:
    # Clean up any existing container and image
    - cp $QA_ENV .env
  script:
    - docker build -f .devops/docker/Dockerfile -t $DOCKER_IMAGE_NAME-dry-run .
    - docker rm -f $CONTAINER_NAME || true
    - docker image rm $DOCKER_IMAGE_NAME || true
    - docker tag $DOCKER_IMAGE_NAME-dry-run $DOCKER_IMAGE_NAME
    - docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $QA_PORT:3000 $DOCKER_IMAGE_NAME
  tags:
    - mvv-ui-runner
