stages:
  - build
  - deploy

# ###BUILD####
build:
  stage: build
  needs: []
  variables:
    MODULE_NAME: mvv-ui
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/mvv/ui:uat
  before_script:
    - cp $UAT_ENV .env
  script:
    - docker build -f .devops/docker/Dockerfile -t $CI_IMAGE_NAME .
    - echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY
    - echo $CI_IMAGE_NAME
    - docker push $CI_IMAGE_NAME
  tags:
    - mvv-ui-runner

deploy:
  stage: deploy
  needs:
    - job: build
      optional: true
  variables:
    CONTAINER_NAME: mvv-ui_uat
    CI_IMAGE_NAME: $CI_REGISTRY_IMAGE/mvv/ui:uat
  script:
    - ssh -i ~/mvv-uat.pem $SERVER_UAT "docker --version"
    - ssh -i ~/mvv-uat.pem $SERVER_UAT
      "echo -n $CI_JOB_TOKEN | docker login -u $CI_REGISTRY_USER --password-stdin $CI_REGISTRY"
    - ssh -i ~/mvv-uat.pem $SERVER_UAT
      "docker pull $CI_IMAGE_NAME; docker rm -f $CONTAINER_NAME; docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $UAT_PORT:3000 $CI_IMAGE_NAME"
  tags:
    - mvv-ui-runner
