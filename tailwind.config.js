/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/app/**/*.{js,ts,jsx,tsx}',
    './src/pages/**/*.{js,ts,jsx,tsx}',
    './src/features/**/*.{js,ts,jsx,tsx}',
    './src/shared/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        // Semantic color tokens that map to CSS variables
        // These will be the foundation for future theming
        background: 'var(--color-background)',
        foreground: 'var(--color-foreground)',

        // Surface colors
        surface: {
          DEFAULT: 'var(--color-surface)',
          hover: 'var(--color-surface-hover)',
          selected: 'var(--color-surface-selected)',
          divider: 'var(--color-surface-divider)',
        },

        // Primary colors
        primary: {
          DEFAULT: 'var(--color-primary)',
          foreground: 'var(--color-primary-foreground)',
          50: 'var(--color-primary-50)',
          100: 'var(--color-primary-100)',
          200: 'var(--color-primary-200)',
          300: 'var(--color-primary-300)',
          400: 'var(--color-primary-400)',
          500: 'var(--color-primary-500)',
          600: 'var(--color-primary-600)',
          700: 'var(--color-primary-700)',
          800: 'var(--color-primary-800)',
          900: 'var(--color-primary-900)',
        },

        // Secondary colors
        secondary: {
          DEFAULT: 'var(--color-secondary)',
          foreground: 'var(--color-secondary-foreground)',
          50: 'var(--color-secondary-50)',
          100: 'var(--color-secondary-100)',
          200: 'var(--color-secondary-200)',
          300: 'var(--color-secondary-300)',
          400: 'var(--color-secondary-400)',
          500: 'var(--color-secondary-500)',
          600: 'var(--color-secondary-600)',
          700: 'var(--color-secondary-700)',
          800: 'var(--color-secondary-800)',
          900: 'var(--color-secondary-900)',
        },

        // Accent colors
        accent: {
          DEFAULT: 'var(--color-accent)',
          foreground: 'var(--color-accent-foreground)',
        },

        // Muted colors
        muted: {
          DEFAULT: 'var(--color-muted)',
          foreground: 'var(--color-muted-foreground)',
        },

        // UI element colors
        card: {
          DEFAULT: 'var(--color-card)',
          foreground: 'var(--color-card-foreground)',
        },
        popover: {
          DEFAULT: 'var(--color-popover)',
          foreground: 'var(--color-popover-foreground)',
        },
        border: 'var(--color-border)',
        input: 'var(--color-input)',
        ring: 'var(--color-ring)',

        // Semantic status colors
        destructive: {
          DEFAULT: 'var(--color-destructive)',
          foreground: 'var(--color-destructive-foreground)',
        },
        success: {
          DEFAULT: 'var(--color-success-100)',
          foreground: 'var(--color-success-25)',
          25: 'var(--color-success-25)',
          50: 'var(--color-success-50)',
          100: 'var(--color-success-100)',
          200: 'var(--color-success-200)',
          300: 'var(--color-success-300)',
          400: 'var(--color-success-400)',
          500: 'var(--color-success-500)',
          600: 'var(--color-success-600)',
          700: 'var(--color-success-700)',
          800: 'var(--color-success-800)',
          900: 'var(--color-success-900)',
          950: 'var(--color-success-950)',
        },
        warning: {
          DEFAULT: 'var(--color-warning)',
          foreground: 'var(--color-warning-foreground)',
          25: 'var(--color-warning-25)',
          50: 'var(--color-warning-50)',
          100: 'var(--color-warning-100)',
          200: 'var(--color-warning-200)',
          300: 'var(--color-warning-300)',
          400: 'var(--color-warning-400)',
          500: 'var(--color-warning-500)',
          600: 'var(--color-warning-600)',
          700: 'var(--color-warning-700)',
          800: 'var(--color-warning-800)',
          900: 'var(--color-warning-900)',
          950: 'var(--color-warning-950)',
        },
        info: {
          DEFAULT: 'var(--color-info)',
          foreground: 'var(--color-info-foreground)',
          25: 'var(--color-info-25)',
          50: 'var(--color-info-50)',
          100: 'var(--color-info-100)',
          200: 'var(--color-info-200)',
          300: 'var(--color-info-300)',
          400: 'var(--color-info-400)',
          500: 'var(--color-info-500)',
          600: 'var(--color-info-600)',
          700: 'var(--color-info-700)',
          800: 'var(--color-info-800)',
          900: 'var(--color-info-900)',
          950: 'var(--color-info-950)',
        },

        // Chart colors
        chart: {
          1: 'var(--color-chart-1)',
          2: 'var(--color-chart-2)',
          3: 'var(--color-chart-3)',
          4: 'var(--color-chart-4)',
          5: 'var(--color-chart-5)',
        },
      },
      borderRadius: {
        sm: 'var(--radius-sm)',
        md: 'var(--radius-md)',
        lg: 'var(--radius-lg)',
        xl: 'var(--radius-xl)',
      },
      fontFamily: {
        lexend: ['var(--font-lexend)', 'sans-serif'],
      },
      fontWeight: {
        regular: 'var(--font-weight-regular)',
        medium: 'var(--font-weight-medium)',
        semibold: 'var(--font-weight-semibold)',
        bold: 'var(--font-weight-bold)',
      },
      letterSpacing: {
        tighter: 'var(--letter-spacing-tighter)',
        tight: 'var(--letter-spacing-tight)',
        normal: 'var(--letter-spacing-normal)',
        wide: 'var(--letter-spacing-wide)',
        wider: 'var(--letter-spacing-wider)',
        widest: 'var(--letter-spacing-widest)',
      },
      fontSize: {
        // Base font sizes
        'xs': ['var(--text-xs)', {
          lineHeight: 'var(--text-xs--line-height)',
        }],
        'sm': ['var(--text-sm)', {
          lineHeight: 'var(--text-sm--line-height)',
        }],
        'base': ['var(--text-base)', {
          lineHeight: 'var(--text-base--line-height)',
        }],
        'lg': ['var(--text-lg)', {
          lineHeight: 'var(--text-lg--line-height)',
        }],
        'xl': ['var(--text-xl)', {
          lineHeight: 'var(--text-xl--line-height)',
        }],
        '2xl': ['var(--text-2xl)', {
          lineHeight: 'var(--text-2xl--line-height)',
        }],
        '3xl': ['var(--text-3xl)', {
          lineHeight: 'var(--text-3xl--line-height)',
        }],
        '4xl': ['var(--text-4xl)', {
          lineHeight: 'var(--text-4xl--line-height)',
        }],
        '5xl': ['var(--text-5xl)', {
          lineHeight: 'var(--text-5xl--line-height)',
        }],
        '6xl': ['var(--text-6xl)', {
          lineHeight: 'var(--text-6xl--line-height)',
        }],
        '7xl': ['var(--text-7xl)', {
          lineHeight: 'var(--text-7xl--line-height)',
        }],
        '8xl': ['var(--text-8xl)', {
          lineHeight: 'var(--text-8xl--line-height)',
        }],
        '9xl': ['var(--text-9xl)', {
          lineHeight: 'var(--text-9xl--line-height)',
        }],

        // Semantic typography tokens
        'heading-1': ['var(--text-heading-1)', {
          lineHeight: 'var(--text-heading-1--line-height)',
        }],
        'heading-2': ['var(--text-heading-2)', {
          lineHeight: 'var(--text-heading-2--line-height)',
        }],
        'heading-3': ['var(--text-heading-3)', {
          lineHeight: 'var(--text-heading-3--line-height)',
        }],
        'heading-4': ['var(--text-heading-4)', {
          lineHeight: 'var(--text-heading-4--line-height)',
        }],
        'heading-5': ['var(--text-heading-5)', {
          lineHeight: 'var(--text-heading-5--line-height)',
        }],
        'heading-6': ['var(--text-heading-6)', {
          lineHeight: 'var(--text-heading-6--line-height)',
        }],

        'body-large': ['var(--text-body-large)', {
          lineHeight: 'var(--text-body-large--line-height)',
        }],
        'body-medium': ['var(--text-body-medium)', {
          lineHeight: 'var(--text-body-medium--line-height)',
        }],
        'body-small': ['var(--text-body-small)', {
          lineHeight: 'var(--text-body-small--line-height)',
        }],
        'body-xs': ['var(--text-body-xs)', {
          lineHeight: 'var(--text-body-xs--line-height)',
        }],

        'display-1': ['var(--text-display-1)', {
          lineHeight: 'var(--text-display-1--line-height)',
        }],
        'display-2': ['var(--text-display-2)', {
          lineHeight: 'var(--text-display-2--line-height)',
        }],

        // Preserve existing title tokens for backward compatibility
        'title-2xl': ['var(--text-heading-1)', {
          lineHeight: 'var(--text-title-2xl--line-height)',
        }],
        'title-xl': ['var(--text-heading-2)', {
          lineHeight: 'var(--text-title-xl--line-height)',
        }],
        'title-lg': ['var(--text-heading-3)', {
          lineHeight: 'var(--text-title-lg--line-height)',
        }],
        'title-md': ['var(--text-heading-4)', {
          lineHeight: 'var(--text-title-md--line-height)',
        }],
        'title-sm': ['var(--text-heading-5)', {
          lineHeight: 'var(--text-title-sm--line-height)',
        }],
        'theme-xl': ['var(--text-theme-xl)', {
          lineHeight: 'var(--text-theme-xl--line-height)',
        }],
        'theme-sm': ['var(--text-theme-sm)', {
          lineHeight: 'var(--text-theme-sm--line-height)',
        }],
        'theme-xs': ['var(--text-theme-xs)', {
          lineHeight: 'var(--text-theme-xs--line-height)',
        }],
      },
    },
  },
  plugins: [],
};
