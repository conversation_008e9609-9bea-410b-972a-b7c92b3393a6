name: Release

on:
  workflow_run:
    workflows: [CI]
    types:
      - completed
    branches:
      - main

jobs:
  release:
    strategy:
      matrix:
        node-version: [20.x]

    name: Create a new release
    runs-on: ubuntu-latest

    permissions:
      contents: write # to be able to publish a GitHub release
      issues: write # to be able to comment on released issues
      pull-requests: write # to be able to comment on released pull requests

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: npm
      - run: HUSKY=0 npm ci

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npx semantic-release
